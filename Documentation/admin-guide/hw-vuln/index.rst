========================
Hardware vulnerabilities
========================

This section describes CPU vulnerabilities and provides an overview of the
possible mitigations along with guidance for selecting mitigations if they
are configurable at compile, boot or run time.

.. toctree::
   :maxdepth: 1

   spectre
   l1tf
   mds
   tsx_async_abort
   multihit
   special-register-buffer-data-sampling
   core-scheduling
   l1d_flush
   processor_mmio_stale_data
   cross-thread-rsb
   srso
   gather_data_sampling
   reg-file-data-sampling
   rsb
   old_microcode
   indirect-target-selection
