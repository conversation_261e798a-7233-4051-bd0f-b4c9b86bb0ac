.. SPDX-License-Identifier: GPL-2.0

.. include:: <isonum.txt>

====================================
Media subsystem admin and user guide
====================================

This section contains usage information about media subsystem and
its supported drivers.

Please see:

Documentation/userspace-api/media/index.rst

  - for the userspace APIs used on media devices.

Documentation/driver-api/media/index.rst

  - for driver development information and Kernel APIs used by
    media devices;

Documentation/process/debugging/media_specific_debugging_guide.rst

  - for advice about essential tools and techniques to debug drivers on this
    subsystem

.. toctree::
	:caption: Table of Contents
	:maxdepth: 2
	:numbered:

	intro
	building

	remote-controller

	cec

	dvb

	cardlist

	v4l-drivers
	dvb-drivers

**Copyright** |copy| 1999-2020 : LinuxTV Developers

::

  This documentation is free software; you can redistribute it and/or modify it
  under the terms of the GNU General Public License as published by the Free
  Software Foundation; either version 2 of the License, or (at your option) any
  later version.

  This program is distributed in the hope that it will be useful, but WITHOUT
  ANY WARRANTY; without even the implied warranty of MERCHANTABILITY or
  FITNESS FOR A PARTICULAR PURPOSE. See the GNU General Public License for
  more details.

  For more details see the file COPYING in the source distribution of Linux.
