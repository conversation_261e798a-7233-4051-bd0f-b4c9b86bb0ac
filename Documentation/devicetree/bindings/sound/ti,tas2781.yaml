# SPDX-License-Identifier: (GPL-2.0-only OR BSD-2-Clause)
# Copyright (C) 2022 - 2023 Texas Instruments Incorporated
%YAML 1.2
---
$id: http://devicetree.org/schemas/sound/ti,tas2781.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: Texas Instruments TAS2563/TAS2781 SmartAMP

maintainers:
  - <PERSON><PERSON><PERSON>g <<EMAIL>>

description: |
  The TAS2563/TAS2781 is a mono, digital input Class-D audio
  amplifier optimized for efficiently driving high peak power into
  small loudspeakers. An integrated on-chip DSP supports Texas
  Instruments Smart Amp speaker protection algorithm. The
  integrated speaker voltage and current sense provides for real time
  monitoring of loudspeaker behavior.

  Specifications about the audio amplifier can be found at:
    https://www.ti.com/lit/gpn/tas2563
    https://www.ti.com/lit/gpn/tas2781

properties:
  compatible:
    description: |
      ti,tas2563: 6.1-W Boosted Class-D Audio Amplifier With Integrated
      DSP and IV Sense, 16/20/24/32bit stereo I2S or multichannel TDM.

      ti,tas2781: 24-V Class-D Amplifier with Real Time Integrated Speaker
      Protection and Audio Processing, 16/20/24/32bit stereo I2S or
      multichannel TDM.
    oneOf:
      - items:
          - enum:
              - ti,tas2563
          - const: ti,tas2781
      - enum:
          - ti,tas2781

  reg:
    description:
      I2C address, in multiple-AMP case, all the i2c address
      aggregate as one Audio Device to support multiple audio slots.
    maxItems: 8
    minItems: 1

  reset-gpios:
    maxItems: 1

  interrupts:
    maxItems: 1

  '#sound-dai-cells':
    const: 0

required:
  - compatible
  - reg

allOf:
  - $ref: dai-common.yaml#
  - if:
      properties:
        compatible:
          contains:
            enum:
              - ti,tas2563
    then:
      properties:
        reg:
          description:
            I2C address, in multiple-AMP case, all the i2c address
            aggregate as one Audio Device to support multiple audio slots.
          maxItems: 4
          minItems: 1
          items:
            minimum: 0x4c
            maximum: 0x4f

  - if:
      properties:
        compatible:
          contains:
            enum:
              - ti,tas2781
    then:
      properties:
        reg:
          description:
            I2C address, in multiple-AMP case, all the i2c address
            aggregate as one Audio Device to support multiple audio slots.
          maxItems: 8
          minItems: 1
          items:
            minimum: 0x38
            maximum: 0x3f

additionalProperties: false

examples:
  - |
    #include <dt-bindings/gpio/gpio.h>

    i2c {
        /* example with quad tas2781s, such as tablet or pad device */
        #address-cells = <1>;
        #size-cells = <0>;

        audio-codec@38 {
            compatible = "ti,tas2781";
            reg = <0x38>, /* Audio slot 0 */
                  <0x3a>, /* Audio slot 1 */
                  <0x39>, /* Audio slot 2 */
                  <0x3b>; /* Audio slot 3 */

            #sound-dai-cells = <0>;
            reset-gpios = <&gpio1 10 GPIO_ACTIVE_HIGH>;
            interrupt-parent = <&gpio1>;
            interrupts = <15>;
        };
    };
...
