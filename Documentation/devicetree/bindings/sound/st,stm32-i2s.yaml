# SPDX-License-Identifier: (GPL-2.0-only OR BSD-2-Clause)
%YAML 1.2
---
$id: http://devicetree.org/schemas/sound/st,stm32-i2s.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: STMicroelectronics STM32 SPI/I2S Controller

maintainers:
  - <PERSON> <<EMAIL>>

description:
  The SPI/I2S block supports I2S/PCM protocols when configured on I2S mode.
  Only some SPI instances support I2S.

properties:
  compatible:
    enum:
      - st,stm32h7-i2s
      - st,stm32mp25-i2s

  "#sound-dai-cells":
    const: 0

  reg:
    maxItems: 1

  clocks:
    items:
      - description: clock feeding the peripheral bus interface.
      - description: clock feeding the internal clock generator.
      - description: I2S parent clock for sampling rates multiple of 8kHz.
      - description: I2S parent clock for sampling rates multiple of 11.025kHz.
    minItems: 2

  clock-names:
    items:
      - const: pclk
      - const: i2sclk
      - const: x8k
      - const: x11k
    minItems: 2

  interrupts:
    maxItems: 1

  dmas:
    items:
      - description: audio capture DMA.
      - description: audio playback DMA.

  dma-names:
    items:
      - const: rx
      - const: tx

  resets:
    maxItems: 1

  "#clock-cells":
    description: Configure the I2S device as MCLK clock provider.
    const: 0

  port:
    $ref: audio-graph-port.yaml#
    unevaluatedProperties: false

  access-controllers:
    minItems: 1
    maxItems: 2

required:
  - compatible
  - "#sound-dai-cells"
  - reg
  - clocks
  - clock-names
  - interrupts
  - dmas
  - dma-names

allOf:
  - $ref: dai-common.yaml#
  - if:
      properties:
        compatible:
          contains:
            const: st,stm32h7-i2s

    then:
      properties:
        clocks:
          minItems: 4

        clock-names:
          minItems: 4

  - if:
      properties:
        compatible:
          contains:
            const: st,stm32mp25-i2s

    then:
      properties:
        clocks:
          maxItems: 2

        clock-names:
          maxItems: 2

unevaluatedProperties: false

examples:
  - |
    #include <dt-bindings/interrupt-controller/arm-gic.h>
    #include <dt-bindings/clock/stm32mp1-clks.h>
    i2s2: audio-controller@4000b000 {
        compatible = "st,stm32h7-i2s";
        #sound-dai-cells = <0>;
        reg = <0x4000b000 0x400>;
        clocks = <&rcc SPI2>, <&rcc SPI2_K>, <&rcc PLL3_Q>, <&rcc PLL3_R>;
        clock-names = "pclk", "i2sclk", "x8k", "x11k";
        interrupts = <GIC_SPI 35 IRQ_TYPE_LEVEL_HIGH>;
        dmas = <&dmamux1 39 0x400 0x01>,
               <&dmamux1 40 0x400 0x01>;
        dma-names = "rx", "tx";
        pinctrl-names = "default";
        pinctrl-0 = <&i2s2_pins_a>;

        /* assume audio-graph */
        port {
            codec_endpoint: endpoint {
                remote-endpoint = <&codec_endpoint>;
            };
        };
    };

...
