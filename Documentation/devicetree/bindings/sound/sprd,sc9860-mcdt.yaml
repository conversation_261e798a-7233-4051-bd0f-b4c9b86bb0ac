# SPDX-License-Identifier: GPL-2.0-only OR BSD-2-Clause
%YAML 1.2
---
$id: http://devicetree.org/schemas/sound/sprd,sc9860-mcdt.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: Spreadtrum Multi-Channel Data Transfer controller

description:
  The Multi-channel data transfer controller is used for sound stream
  transmission between the audio subsystem and other AP/CP subsystem. It
  supports 10 DAC channels and 10 ADC channels, and each channel can be
  configured with DMA mode or interrupt mode.

maintainers:
  - <PERSON><PERSON> <or<PERSON><PERSON><PERSON>@gmail.com>
  - <PERSON><PERSON> <<EMAIL>>
  - <PERSON><PERSON> <<EMAIL>>

properties:
  compatible:
    const: sprd,sc9860-mcdt

  reg:
    maxItems: 1

  interrupts:
    maxItems: 1

required:
  - compatible
  - reg
  - interrupts

additionalProperties: false

examples:
  - |
    #include <dt-bindings/interrupt-controller/arm-gic.h>
    #include <dt-bindings/interrupt-controller/irq.h>

    mcdt@41490000 {
      compatible = "sprd,sc9860-mcdt";
      reg = <0x41490000 0x170>;
      interrupts = <GIC_SPI 48 IRQ_TYPE_LEVEL_HIGH>;
    };
...
