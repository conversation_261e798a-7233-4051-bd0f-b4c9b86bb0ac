# SPDX-License-Identifier: GPL-2.0-only OR BSD-2-Clause
%YAML 1.2
---
$id: http://devicetree.org/schemas/sound/wlf,wm8960.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: Wolfson WM8960 audio codec

maintainers:
  - <EMAIL>

properties:
  compatible:
    const: wlf,wm8960

  reg:
    maxItems: 1

  clocks:
    maxItems: 1

  clock-names:
    items:
      - const: mclk

  '#sound-dai-cells':
    const: 0

  AVDD-supply:
    description: Analogue supply.

  DBVDD-supply:
    description: Digital Buffer Supply.

  DCVDD-supply:
    description: Digital Core Supply.

  SPKVDD1-supply:
    description: Supply for speaker drivers 1.

  SPKVDD2-supply:
    description: Supply for speaker drivers 2.

  wlf,capless:
    type: boolean
    description:
      If present, OUT3 pin will be enabled and disabled together with HP_L and
      HP_R pins in response to jack detect events.

  wlf,gpio-cfg:
    $ref: /schemas/types.yaml#/definitions/uint32-array
    maxItems: 2
    description: |
      A list of GPIO configuration register values.
       - gpio-cfg[0]: ALRCGPIO of R9 (Audio interface)
       - gpio-cfg[1]: {GPIOPOL:GPIOSEL[2:0]} of R48 (Additional Control 4).

  wlf,hp-cfg:
    $ref: /schemas/types.yaml#/definitions/uint32-array
    maxItems: 3
    description: |
      A list of headphone jack detect configuration register values:
       - hp-cfg[0]: HPSEL[1:0] of R48 (Additional Control 4).
       - hp-cfg[1]: {HPSWEN:HPSWPOL} of R24 (Additional Control 2).
       - hp-cfg[2]: {TOCLKSEL:TOEN} of R23 (Additional Control 1).

  wlf,shared-lrclk:
    type: boolean
    description:
      If present, the LRCM bit of R24 (Additional control 2) gets set,
      indicating that ADCLRC and DACLRC pins will be disabled only when ADC
      (Left and Right) and DAC (Left and Right) are disabled.
      When WM8960 works on synchronize mode and DACLRC pin is used to supply
      frame clock, it will no frame clock for captrue unless enable DAC to
      enable DACLRC pin. If shared-lrclk is present, no need to enable DAC for
      captrue.

  port:
    $ref: audio-graph-port.yaml#
    unevaluatedProperties: false

required:
  - compatible
  - reg

allOf:
  - $ref: dai-common.yaml#

unevaluatedProperties: false

examples:
  - |
    i2c {
        #address-cells = <1>;
        #size-cells = <0>;

        audio-codec@1a {
            compatible = "wlf,wm8960";
            reg = <0x1a>;
            clocks = <&clks 0>;
            clock-names = "mclk";
            #sound-dai-cells = <0>;
            wlf,hp-cfg = <3 2 3>;
            wlf,gpio-cfg = <1 3>;
            wlf,shared-lrclk;
            DCVDD-supply = <&reg_audio>;
            DBVDD-supply = <&reg_audio>;
            AVDD-supply = <&reg_audio>;
            SPKVDD1-supply = <&reg_audio>;
            SPKVDD2-supply = <&reg_audio>;
        };
    };
