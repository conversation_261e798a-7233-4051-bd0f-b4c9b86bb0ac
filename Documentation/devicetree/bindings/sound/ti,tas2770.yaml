# SPDX-License-Identifier: (GPL-2.0-only OR BSD-2-Clause)
# Copyright (C) 2019-20 Texas Instruments Incorporated
%YAML 1.2
---
$id: http://devicetree.org/schemas/sound/ti,tas2770.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: Texas Instruments TAS2770 Smart PA

maintainers:
  - <PERSON> <<EMAIL>>

description: |
  The TAS2770 is a mono, digital input Class-D audio amplifier optimized for
  efficiently driving high peak power into small loudspeakers.
  Integrated speaker voltage and current sense provides for
  real time monitoring of loudspeaker behavior.

allOf:
  - $ref: dai-common.yaml#

properties:
  compatible:
    enum:
      - ti,tas2770
      - ti,tas5770l # Apple variant

  reg:
    maxItems: 1
    description: |
       I2C address of the device can be between 0x41 to 0x48.

  reset-gpio:
    maxItems: 1
    description: GPIO used to reset the device.

  shutdown-gpios:
    maxItems: 1
    description: GPIO used to control the state of the device.

  interrupts:
    maxItems: 1

  ti,imon-slot-no:
    $ref: /schemas/types.yaml#/definitions/uint32
    description: TDM TX current sense time slot.

  ti,vmon-slot-no:
    $ref: /schemas/types.yaml#/definitions/uint32
    description: TDM TX voltage sense time slot.

  ti,asi-format:
    deprecated: true
    $ref: /schemas/types.yaml#/definitions/uint32
    description: Sets TDM RX capture edge.
    enum:
      - 0 # Rising edge
      - 1 # Falling edge

  '#sound-dai-cells':
    # The codec has a single DAI, the #sound-dai-cells=<1>; case is left in for backward
    # compatibility but is deprecated.
    enum: [0, 1]

required:
  - compatible
  - reg

unevaluatedProperties: false

examples:
  - |
    #include <dt-bindings/gpio/gpio.h>

    i2c {
        #address-cells = <1>;
        #size-cells = <0>;

        codec@41 {
            compatible = "ti,tas2770";
            reg = <0x41>;
            #sound-dai-cells = <0>;
            interrupt-parent = <&gpio1>;
            interrupts = <14>;
            reset-gpio = <&gpio1 15 GPIO_ACTIVE_HIGH>;
            shutdown-gpios = <&gpio1 14 GPIO_ACTIVE_HIGH>;
            ti,imon-slot-no = <0>;
            ti,vmon-slot-no = <2>;
        };
    };
