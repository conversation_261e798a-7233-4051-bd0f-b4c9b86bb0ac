# SPDX-License-Identifier: (GPL-2.0-only OR BSD-2-Clause)
%YAML 1.2
---
$id: http://devicetree.org/schemas/sound/realtek,rt5682.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: Realtek rt5682 and rt5682i codecs

maintainers:
  - <PERSON><PERSON> <<EMAIL>>

allOf:
  - $ref: dai-common.yaml#

properties:
  compatible:
    enum:
      - realtek,rt5682
      - realtek,rt5682i

  reg:
    maxItems: 1
    description: I2C address of the device.

  interrupts:
    maxItems: 1
    description: The CODEC's interrupt output.

  realtek,dmic1-data-pin:
    $ref: /schemas/types.yaml#/definitions/uint32
    enum:
      - 0 # dmic1 data is not used
      - 1 # using GPIO2 pin as dmic1 data pin
      - 2 # using GPIO5 pin as dmic1 data pin
    description:
      Specify which GPIO pin be used as DMIC1 data pin.

  realtek,dmic1-clk-pin:
    $ref: /schemas/types.yaml#/definitions/uint32
    enum:
      - 0 # using GPIO1 pin as dmic1 clock pin
      - 1 # using GPIO3 pin as dmic1 clock pin
    description:
      Specify which GPIO pin be used as DMIC1 clk pin.

  realtek,jd-src:
    $ref: /schemas/types.yaml#/definitions/uint32
    enum:
      - 0 # No JD is used
      - 1 # using JD1 as JD source
    description:
      Specify which JD source be used.

  realtek,ldo1-en-gpios:
    description:
      The GPIO that controls the CODEC's LDO1_EN pin.

  realtek,btndet-delay:
    $ref: /schemas/types.yaml#/definitions/uint32
    description:
      The debounce delay for push button.
      The delay time is realtek,btndet-delay value multiple of 8.192 ms.
      If absent, the default is 16.

  realtek,dmic-clk-rate-hz:
    description:
      Set the clock rate (hz) for the requirement of the particular DMIC.

  realtek,dmic-delay-ms:
    description:
      Set the delay time (ms) for the requirement of the particular DMIC.

  realtek,dmic-clk-driving-high:
    type: boolean
    description:
      Set the high driving of the DMIC clock out.

  clocks:
    items:
      - description: phandle and clock specifier for codec MCLK.

  clock-names:
    items:
      - const: mclk

  "#clock-cells":
    const: 1

  clock-output-names:
    minItems: 2
    maxItems: 2
    description: Name given for DAI word clock and bit clock outputs.

  "#sound-dai-cells":
    const: 1

  AVDD-supply:
    description: Regulator supplying analog power through the AVDD pin.

  MICVDD-supply:
    description: Regulator supplying power for the microphone bias through
      the MICVDD pin.

  VBAT-supply:
    description: Regulator supplying battery power through the VBAT pin.

  DBVDD-supply:
    description: Regulator supplying I/O power through the DBVDD pin.

  LDO1-IN-supply:
    description: Regulator supplying power to the digital core and charge
      pump through the LDO1_IN pin.

required:
  - compatible
  - reg
  - AVDD-supply
  - VBAT-supply
  - MICVDD-supply
  - DBVDD-supply
  - LDO1-IN-supply

unevaluatedProperties: false

examples:
  - |
    #include <dt-bindings/gpio/gpio.h>
    #include <dt-bindings/interrupt-controller/irq.h>

    i2c {
        #address-cells = <1>;
        #size-cells = <0>;

        codec@1a {
            compatible = "realtek,rt5682";
            reg = <0x1a>;
            interrupts = <6 IRQ_TYPE_LEVEL_HIGH>;
            realtek,ldo1-en-gpios =
                <&gpio 2 GPIO_ACTIVE_HIGH>;
            realtek,dmic1-data-pin = <1>;
            realtek,dmic1-clk-pin = <1>;
            realtek,jd-src = <1>;

            #clock-cells = <1>;
            clock-output-names = "rt5682-dai-wclk", "rt5682-dai-bclk";

            clocks = <&osc>;
            clock-names = "mclk";

            AVDD-supply = <&avdd_reg>;
            VBAT-supply = <&vbat_reg>;
            MICVDD-supply = <&micvdd_reg>;
            DBVDD-supply = <&dbvdd_reg>;
            LDO1-IN-supply = <&ldo1_in_reg>;
        };
    };
