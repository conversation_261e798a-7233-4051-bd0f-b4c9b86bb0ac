# SPDX-License-Identifier: (GPL-2.0-only OR BSD-2-Clause)
%YAML 1.2
---
$id: http://devicetree.org/schemas/sound/rockchip,rk3036-codec.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: Rockchip RK3036 internal codec

maintainers:
  - <PERSON><PERSON> <<EMAIL>>

allOf:
  - $ref: dai-common.yaml#

properties:
  compatible:
    const: rockchip,rk3036-codec

  reg:
    maxItems: 1

  clocks:
    items:
      - description: clock for audio codec

  clock-names:
    items:
      - const: acodec_pclk

  rockchip,grf:
    $ref: /schemas/types.yaml#/definitions/phandle
    description:
      The phandle of the syscon node for the GRF register.

  "#sound-dai-cells":
    const: 0

required:
  - compatible
  - reg
  - clocks
  - clock-names
  - rockchip,grf
  - "#sound-dai-cells"

unevaluatedProperties: false

examples:
  - |
    #include <dt-bindings/clock/rk3036-cru.h>
    acodec: audio-codec@20030000 {
      compatible = "rockchip,rk3036-codec";
      reg = <0x20030000 0x4000>;
      rockchip,grf = <&grf>;
      clock-names = "acodec_pclk";
      clocks = <&cru ACLK_VCODEC>;
      #sound-dai-cells = <0>;
    };
