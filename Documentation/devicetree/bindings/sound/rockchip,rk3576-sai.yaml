# SPDX-License-Identifier: (GPL-2.0-only OR BSD-2-Clause)
%YAML 1.2
---
$id: http://devicetree.org/schemas/sound/rockchip,rk3576-sai.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: Rockchip Serial Audio Interface Controller

description:
  The Rockchip Serial Audio Interface (SAI) controller is a flexible audio
  controller that implements the I2S, I2S/TDM and the PDM standards.

maintainers:
  - <PERSON> <<EMAIL>>

allOf:
  - $ref: dai-common.yaml#

properties:
  compatible:
    const: rockchip,rk3576-sai

  reg:
    maxItems: 1

  interrupts:
    maxItems: 1

  dmas:
    minItems: 1
    maxItems: 2

  dma-names:
    minItems: 1
    items:
      - enum: [tx, rx]
      - const: rx

  clocks:
    items:
      - description: master audio clock
      - description: AHB clock driving the interface

  clock-names:
    items:
      - const: mclk
      - const: hclk

  resets:
    minItems: 1
    items:
      - description: reset for the mclk domain
      - description: reset for the hclk domain

  reset-names:
    minItems: 1
    items:
      - const: m
      - const: h

  port:
    $ref: audio-graph-port.yaml#
    unevaluatedProperties: false

  power-domains:
    maxItems: 1

  "#sound-dai-cells":
    const: 0

  rockchip,sai-rx-route:
    $ref: /schemas/types.yaml#/definitions/uint32-array
    description:
      Defines the mapping of the controller's SDI ports to actual input lanes,
      as well as the number of input lanes.
      rockchip,sai-rx-route = <3> would mean sdi3 is receiving from data0, and
      that there is only one receiving lane.
      This property's absence is to be understood as only one receiving lane
      being used if the controller has capture capabilities.
    maxItems: 4
    items:
      minimum: 0
      maximum: 3

  rockchip,sai-tx-route:
    $ref: /schemas/types.yaml#/definitions/uint32-array
    description:
      Defines the mapping of the controller's SDO ports to actual output lanes,
      as well as the number of output lanes.
      rockchip,sai-tx-route = <3> would mean sdo3 is sending to data0, and
      that there is only one transmitting lane.
      This property's absence is to be understood as only one transmitting lane
      being used if the controller has playback capabilities.
    maxItems: 4
    items:
      minimum: 0
      maximum: 3

required:
  - compatible
  - reg
  - dmas
  - dma-names
  - clocks
  - clock-names
  - "#sound-dai-cells"

unevaluatedProperties: false

examples:
  - |
    #include <dt-bindings/clock/rockchip,rk3576-cru.h>
    #include <dt-bindings/interrupt-controller/arm-gic.h>
    #include <dt-bindings/interrupt-controller/irq.h>
    #include <dt-bindings/pinctrl/rockchip.h>
    #include <dt-bindings/power/rockchip,rk3576-power.h>
    #include <dt-bindings/reset/rockchip,rk3576-cru.h>

    bus {
        #address-cells = <2>;
        #size-cells = <2>;
        sai1: sai@2a610000 {
            compatible = "rockchip,rk3576-sai";
            reg = <0x0 0x2a610000 0x0 0x1000>;
            interrupts = <GIC_SPI 188 IRQ_TYPE_LEVEL_HIGH>;
            clocks = <&cru MCLK_SAI1_8CH>, <&cru HCLK_SAI1_8CH>;
            clock-names = "mclk", "hclk";
            dmas = <&dmac0 2>, <&dmac0 3>;
            dma-names = "tx", "rx";
            power-domains = <&power RK3576_PD_AUDIO>;
            resets = <&cru SRST_M_SAI1_8CH>, <&cru SRST_H_SAI1_8CH>;
            reset-names = "m", "h";
            pinctrl-names = "default";
            pinctrl-0 = <&sai1m0_lrck
                         &sai1m0_sclk
                         &sai1m0_sdi0
                         &sai1m0_sdo0
                         &sai1m0_sdo1
                         &sai1m0_sdo2
                         &sai1m0_sdo3>;
            rockchip,sai-tx-route = <3 1 2 0>;
            #sound-dai-cells = <0>;
        };
    };
