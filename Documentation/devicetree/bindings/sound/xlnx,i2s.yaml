# SPDX-License-Identifier: (GPL-2.0-only OR BSD-2-Clause)
%YAML 1.2
---
$id: http://devicetree.org/schemas/sound/xlnx,i2s.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: Xilinx I2S PL block

description:
  The IP supports I2S based playback/capture audio.

maintainers:
  - <PERSON> <<EMAIL>>

allOf:
  - $ref: dai-common.yaml#

properties:
  compatible:
    enum:
      - xlnx,i2s-receiver-1.0
      - xlnx,i2s-transmitter-1.0

  reg:
    maxItems: 1

  xlnx,dwidth:
    $ref: /schemas/types.yaml#/definitions/uint32
    enum:
      - 16
      - 24
    description: |
      Sample data width.

  xlnx,num-channels:
    $ref: /schemas/types.yaml#/definitions/uint32
    minimum: 1
    maximum: 4
    description: |
      Number of I2S streams.

required:
  - compatible
  - reg
  - xlnx,dwidth
  - xlnx,num-channels

additionalProperties: false

examples:
  - |
    i2s@a0080000 {
      compatible = "xlnx,i2s-receiver-1.0";
      reg = <0xa0080000 0x10000>;
      xlnx,dwidth = <0x18>;
      xlnx,num-channels = <1>;
    };
    i2s@a0090000 {
      compatible = "xlnx,i2s-transmitter-1.0";
      reg = <0xa0090000 0x10000>;
      xlnx,dwidth = <0x18>;
      xlnx,num-channels = <1>;
    };

...
