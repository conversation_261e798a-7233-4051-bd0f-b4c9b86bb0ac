# SPDX-License-Identifier: (GPL-2.0-only OR BSD-2-Clause)
%YAML 1.2
---
$id: http://devicetree.org/schemas/sound/renesas,rz-ssi.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: Renesas RZ/{G2L,V2L} ASoC Sound Serial Interface (SSIF-2)

maintainers:
  - <PERSON><PERSON><PERSON> <<EMAIL>>

allOf:
  - $ref: dai-common.yaml#

properties:
  compatible:
    items:
      - enum:
          - renesas,r9a07g043-ssi  # RZ/G2UL and RZ/Five
          - renesas,r9a07g044-ssi  # RZ/G2{L,LC}
          - renesas,r9a07g054-ssi  # RZ/V2L
          - renesas,r9a08g045-ssi  # RZ/G3S
      - const: renesas,rz-ssi

  reg:
    maxItems: 1

  interrupts:
    minItems: 2
    maxItems: 3

  interrupt-names:
    oneOf:
      - items:
          - const: int_req
          - const: dma_rx
          - const: dma_tx
      - items:
          - const: int_req
          - const: dma_rt

  clocks:
    maxItems: 4

  clock-names:
    items:
      - const: ssi
      - const: ssi_sfr
      - const: audio_clk1
      - const: audio_clk2

  power-domains:
    maxItems: 1

  resets:
    maxItems: 1

  dmas:
    minItems: 1
    maxItems: 2

  dma-names:
    oneOf:
      - items:
          - const: tx
          - const: rx
      - items:
          - const: rt

  '#sound-dai-cells':
    const: 0

  port:
    $ref: audio-graph-port.yaml#/definitions/port-base
    description: Connection to controller providing I2S signals

required:
  - compatible
  - reg
  - interrupts
  - interrupt-names
  - clocks
  - clock-names
  - resets
  - '#sound-dai-cells'

unevaluatedProperties: false

examples:
  - |
    #include <dt-bindings/interrupt-controller/arm-gic.h>
    #include <dt-bindings/clock/r9a07g044-cpg.h>

    ssi0: ssi@10049c00 {
        compatible = "renesas,r9a07g044-ssi",
                     "renesas,rz-ssi";
            reg = <0x10049c00 0x400>;
            interrupts = <GIC_SPI 326 IRQ_TYPE_LEVEL_HIGH>,
                         <GIC_SPI 327 IRQ_TYPE_EDGE_RISING>,
                         <GIC_SPI 328 IRQ_TYPE_EDGE_RISING>;
            interrupt-names = "int_req", "dma_rx", "dma_tx";
            clocks = <&cpg CPG_MOD R9A07G044_SSI0_PCLK2>,
                     <&cpg CPG_MOD R9A07G044_SSI0_PCLK_SFR>,
                     <&audio_clk1>,
                     <&audio_clk2>;
            clock-names = "ssi", "ssi_sfr", "audio_clk1", "audio_clk2";
            power-domains = <&cpg>;
            resets = <&cpg R9A07G044_SSI0_RST_M2_REG>;
            dmas = <&dmac 0x2655>,
                   <&dmac 0x2656>;
            dma-names = "tx", "rx";
            #sound-dai-cells = <0>;
    };
