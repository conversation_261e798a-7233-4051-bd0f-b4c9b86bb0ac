# SPDX-License-Identifier: (GPL-2.0-only OR BSD-2-Clause)
# Copyright (C) 2019 Texas Instruments Incorporated
%YAML 1.2
---
$id: http://devicetree.org/schemas/sound/ti,tas2562.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: Texas Instruments TAS2562 Smart PA

maintainers:
  - <PERSON> <<EMAIL>>

description: |
  The TAS2562 is a mono, digital input Class-D audio amplifier optimized for
  efficiently driving high peak power into small loudspeakers.
  Integrated speaker voltage and current sense provides for
  real time monitoring of loudspeaker behavior.

  Specifications about the audio amplifier can be found at:
    https://www.ti.com/lit/gpn/tas2562
    https://www.ti.com/lit/gpn/tas2564
    https://www.ti.com/lit/gpn/tas2110

allOf:
  - $ref: dai-common.yaml#

properties:
  compatible:
    enum:
      - ti,tas2562
      - ti,tas2564
      - ti,tas2110

  reg:
    maxItems: 1
    description: |
       I2C address of the device can be one of these 0x4c, 0x4d, 0x4e or 0x4f

  shut-down-gpios:
    maxItems: 1
    description: GPIO used to control the state of the device.
    deprecated: true

  shutdown-gpios:
    maxItems: 1
    description: GPIO used to control the state of the device.

  interrupts:
    maxItems: 1

  ti,imon-slot-no:
    $ref: /schemas/types.yaml#/definitions/uint32
    description: TDM TX current sense time slot.

  '#sound-dai-cells':
    # The codec has a single DAI, the #sound-dai-cells=<1>; case is left in for backward
    # compatibility but is deprecated.
    enum: [0, 1]

required:
  - compatible
  - reg

unevaluatedProperties: false

examples:
  - |
    #include <dt-bindings/gpio/gpio.h>

    i2c {
        #address-cells = <1>;
        #size-cells = <0>;

        codec@4c {
            compatible = "ti,tas2562";
            reg = <0x4c>;
            #sound-dai-cells = <0>;
            interrupt-parent = <&gpio1>;
            interrupts = <14>;
            shutdown-gpios = <&gpio1 15 GPIO_ACTIVE_HIGH>;
            ti,imon-slot-no = <0>;
        };
    };
