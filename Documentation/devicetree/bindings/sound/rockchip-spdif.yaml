# SPDX-License-Identifier: GPL-2.0
%YAML 1.2
---
$id: http://devicetree.org/schemas/sound/rockchip-spdif.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: Rockchip SPDIF transceiver

description:
  The S/PDIF audio block is a stereo transceiver that allows the
  processor to receive and transmit digital audio via a coaxial or
  fibre cable.

maintainers:
  - <PERSON><PERSON> <<EMAIL>>

properties:
  compatible:
    oneOf:
      - const: rockchip,rk3066-spdif
      - const: rockchip,rk3228-spdif
      - const: rockchip,rk3328-spdif
      - const: rockchip,rk3366-spdif
      - const: rockchip,rk3368-spdif
      - const: rockchip,rk3399-spdif
      - const: rockchip,rk3568-spdif
      - items:
          - enum:
              - rockchip,rk3128-spdif
              - rockchip,rk3188-spdif
              - rockchip,rk3288-spdif
              - rockchip,rk3308-spdif
          - const: rockchip,rk3066-spdif
      - items:
          - enum:
              - rockchip,rk3588-spdif
          - const: rockchip,rk3568-spdif

  reg:
    maxItems: 1

  interrupts:
    maxItems: 1

  clocks:
    items:
      - description: clock for SPDIF bus
      - description: clock for SPDIF controller

  clock-names:
    items:
      - const: mclk
      - const: hclk

  dmas:
    maxItems: 1

  dma-names:
    const: tx

  power-domains:
    maxItems: 1

  rockchip,grf:
    $ref: /schemas/types.yaml#/definitions/phandle
    description:
      The phandle of the syscon node for the GRF register.
      Required property on RK3288.

  "#sound-dai-cells":
    const: 0

required:
  - compatible
  - reg
  - interrupts
  - clocks
  - clock-names
  - dmas
  - dma-names
  - "#sound-dai-cells"

allOf:
  - $ref: dai-common.yaml#
  - if:
      properties:
        compatible:
          contains:
            const: rockchip,rk3288-spdif
    then:
      required:
        - rockchip,grf

unevaluatedProperties: false

examples:
  - |
    #include <dt-bindings/clock/rk3188-cru.h>
    #include <dt-bindings/interrupt-controller/arm-gic.h>
    spdif: spdif@1011e000 {
      compatible = "rockchip,rk3188-spdif", "rockchip,rk3066-spdif";
      reg = <0x1011e000 0x2000>;
      interrupts = <GIC_SPI 32 IRQ_TYPE_LEVEL_HIGH>;
      clocks = <&cru SCLK_SPDIF>, <&cru HCLK_SPDIF>;
      clock-names = "mclk", "hclk";
      dmas = <&dmac1_s 8>;
      dma-names = "tx";
      #sound-dai-cells = <0>;
    };
