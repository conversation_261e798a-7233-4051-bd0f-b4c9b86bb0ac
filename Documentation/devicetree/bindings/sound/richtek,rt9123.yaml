# SPDX-License-Identifier: GPL-2.0-only OR BSD-2-Clause
%YAML 1.2
---
$id: http://devicetree.org/schemas/sound/richtek,rt9123.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: Richtek RT9123 Audio Amplifier

maintainers:
  - <PERSON><PERSON><PERSON> <<EMAIL>>

description:
  RT9123 is a 3.2W mono Class-D audio amplifier that features high efficiency
  and performance with ultra-low quiescent current. The digital audio interface
  support various formats, including I2S, left-justified, right-justified, and
  TDM formats.

allOf:
  - $ref: dai-common.yaml#

properties:
  compatible:
    enum:
      - richtek,rt9123

  reg:
    maxItems: 1

  '#sound-dai-cells':
    const: 0

  enable-gpios:
    maxItems: 1

required:
  - compatible
  - reg
  - '#sound-dai-cells'

unevaluatedProperties: false

examples:
  - |
    #include <dt-bindings/gpio/gpio.h>

    i2c {
        #address-cells = <1>;
        #size-cells = <0>;

        amplifier@5e {
            compatible = "richtek,rt9123";
            reg = <0x5e>;
            enable-gpios = <&gpio 26 GPIO_ACTIVE_HIGH>;
            #sound-dai-cells = <0>;
        };
    };
