# SPDX-License-Identifier: GPL-2.0-only OR BSD-2-Clause
%YAML 1.2
---
$id: http://devicetree.org/schemas/sound/realtek,rt5645.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: RT5650/RT5645 audio CODEC

maintainers:
  - <PERSON><PERSON><PERSON> <<EMAIL>>

description: |
  This device supports I2C only.

  Pins on the device (for linking into audio routes) for RT5645/RT5650:
    * DMIC L1
    * DMIC R1
    * DMIC L2
    * DMIC R2
    * IN1P
    * IN1N
    * IN2P
    * IN2N
    * Haptic Generator
    * HPOL
    * HPOR
    * LOUTL
    * LOUTR
    * PDM1L
    * PDM1R
    * SPOL
    * SPOR

allOf:
  - $ref: dai-common.yaml#

properties:
  compatible:
    enum:
      - realtek,rt5645
      - realtek,rt5650

  reg:
    maxItems: 1

  interrupts:
    maxItems: 1
    description: The CODEC's interrupt output.

  avdd-supply:
    description: Power supply for AVDD, providing 1.8V.

  cpvdd-supply:
    description: Power supply for CPVDD, providing 1.8V.

  hp-detect-gpios:
    description: 
      A GPIO spec for the external headphone detect pin. If jd-mode = 0, we
      will get the JD status by getting the value of hp-detect-gpios.
    maxItems: 1

  cbj-sleeve-gpios:
    description:
      A GPIO spec to control the external combo jack circuit to tie the
      sleeve/ring2 contacts to the ground or floating. It could avoid some
      electric noise from the active speaker jacks.
    maxItems: 1

  realtek,in2-differential:
    description:
      Indicate MIC2 input are differential, rather than single-ended.
    type: boolean

  realtek,dmic1-data-pin:
    description: Specify which pin to be used as DMIC1 data pin.
    $ref: /schemas/types.yaml#/definitions/uint32
    enum:
      - 0 # dmic1 is not used
      - 1 # using IN2P pin as dmic1 data pin
      - 2 # using GPIO6 pin as dmic1 data pin
      - 3 # using GPIO10 pin as dmic1 data pin
      - 4 # using GPIO12 pin as dmic1 data pin

  realtek,dmic2-data-pin:
    description: Specify which pin to be used as DMIC2 data pin.
    $ref: /schemas/types.yaml#/definitions/uint32
    enum:
      - 0 # dmic2 is not used
      - 1 # using IN2N pin as dmic2 data pin
      - 2 # using GPIO5 pin as dmic2 data pin
      - 3 # using GPIO11 pin as dmic2 data pin

  realtek,jd-mode:
    description: The JD mode of rt5645/rt5650.
    $ref: /schemas/types.yaml#/definitions/uint32
    enum:
      - 0 # rt5645/rt5650 JD function is not used
      - 1 # Mode-0 (VDD=3.3V), two port jack detection
      - 2 # Mode-1 (VDD=3.3V), one port jack detection
      - 3 # Mode-2 (VDD=1.8V), one port jack detection

required:
  - compatible
  - reg
  - interrupts
  - avdd-supply
  - cpvdd-supply

unevaluatedProperties: false

examples:
  - |
    #include <dt-bindings/gpio/gpio.h>
    #include <dt-bindings/interrupt-controller/irq.h>

    i2c {
        #address-cells = <1>;
        #size-cells = <0>;

        codec@1a {
            compatible = "realtek,rt5650";
            reg = <0x1a>;
            hp-detect-gpios = <&gpio 19 0>;
            cbj-sleeve-gpios = <&gpio 20 0>;
            interrupt-parent = <&gpio>;
            interrupts = <7 IRQ_TYPE_EDGE_FALLING>;
            avdd-supply = <&avdd_reg>;
            cpvdd-supply = <&cpvdd_supply>;
            realtek,jd-mode = <3>;
        };
    };
