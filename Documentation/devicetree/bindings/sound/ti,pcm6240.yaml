# SPDX-License-Identifier: (GPL-2.0-only OR BSD-2-Clause)
# Copyright (C) 2022 - 2024 Texas Instruments Incorporated
%YAML 1.2
---
$id: http://devicetree.org/schemas/sound/ti,pcm6240.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: Texas Instruments PCM6240 Family Audio ADC/DAC

maintainers:
  - <PERSON><PERSON><PERSON> Din<PERSON> <<EMAIL>>

description: |
  The PCM6240 Family is a big family of Audio ADC/DAC for
  different Specifications, range from Personal Electric
  to Automotive Electric, even some professional fields.

  Specifications about the audio chip can be found at:
    https://www.ti.com/lit/gpn/tlv320adc3120
    https://www.ti.com/lit/gpn/tlv320adc5120
    https://www.ti.com/lit/gpn/tlv320adc6120
    https://www.ti.com/lit/gpn/dix4192
    https://www.ti.com/lit/gpn/pcm1690
    https://www.ti.com/lit/gpn/pcm3120-q1
    https://www.ti.com/lit/gpn/pcm3140-q1
    https://www.ti.com/lit/gpn/pcm5120-q1
    https://www.ti.com/lit/gpn/pcm6120-q1
    https://www.ti.com/lit/gpn/pcm6260-q1
    https://www.ti.com/lit/gpn/pcm9211
    https://www.ti.com/lit/gpn/pcmd3140
    https://www.ti.com/lit/gpn/pcmd3180
    https://www.ti.com/lit/gpn/taa5212
    https://www.ti.com/lit/gpn/tad5212

properties:
  compatible:
    description: |
      ti,adc3120: Stereo-channel, 768-kHz, Burr-Brown™ audio analog-to-
      digital converter (ADC) with 106-dB SNR.

      ti,adc5120: 2-Channel, 768-kHz, Burr-Brown™ Audio ADC with 120-dB SNR.

      ti,adc6120: Stereo-channel, 768-kHz, Burr-Brown™ audio analog-to-
      digital converter (ADC) with 123-dB SNR.

      ti,dix4192: 216-kHz digital audio converter with Quad-Channel In
      and One-Channel Out.

      ti,pcm1690: Automotive Catalog 113dB SNR 8-Channel Audio DAC with
      Differential Outputs.

      ti,pcm3120: Automotive, stereo, 106-dB SNR, 768-kHz, low-power
      software-controlled audio ADC.

      ti,pcm3140: Automotive, Quad-Channel, 768-kHz, Burr-Brown™ Audio ADC
      with 106-dB SNR.

      ti,pcm5120: Automotive, stereo, 120-dB SNR, 768-kHz, low-power
      software-controlled audio ADC.

      ti,pcm5140: Automotive, Quad-Channel, 768-kHz, Burr-Brown™ Audio ADC
      with 120-dB SNR.

      ti,pcm6120: Automotive, stereo, 123-dB SNR, 768-kHz, low-power
      software-controlled audio ADC.

      ti,pcm6140: Automotive, Quad-Channel, 768-kHz, Burr-Brown™ Audio ADC
      with 123-dB SNR.

      ti,pcm6240: Automotive 4-ch audio ADC with integrated programmable mic
      bias, boost and input diagnostics.

      ti,pcm6260: Automotive 6-ch audio ADC with integrated programmable mic
      bias, boost and input diagnostics.

      ti,pcm9211: 216-kHz digital audio converter With Stereo ADC and
      Routing.

      ti,pcmd3140: Four-channel PDM-input to TDM or I2S output converter.

      ti,pcmd3180: Eight-channel pulse-density-modulation input to TDM or
      I2S output converter.

      ti,taa5212: Low-power high-performance stereo audio ADC with 118-dB
      dynamic range.

      ti,tad5212: Low-power stereo audio DAC with 120-dB dynamic range.
    oneOf:
      - items:
          - enum:
              - ti,adc3120
              - ti,adc5120
              - ti,pcm3120
              - ti,pcm5120
              - ti,pcm6120
          - const: ti,adc6120
      - items:
          - enum:
              - ti,pcmd512x
              - ti,pcm9211
              - ti,taa5212
              - ti,tad5212
          - const: ti,adc6120
      - items:
          - enum:
              - ti,pcm3140
              - ti,pcm5140
              - ti,dix4192
              - ti,pcm6140
              - ti,pcm6260
          - const: ti,pcm6240
      - items:
          - enum:
              - ti,pcmd3140
              - ti,pcmd3180
              - ti,pcm1690
              - ti,taa5412
              - ti,tad5412
          - const: ti,pcm6240
      - enum:
          - ti,adc6120
          - ti,pcm6240

  reg:
    description:
      I2C address, in multiple pcmdevices case, all the i2c address
      aggregate as one Audio Device to support multiple audio slots.
    minItems: 1
    maxItems: 4

  reset-gpios:
    maxItems: 1

  interrupts:
    maxItems: 1
    description:
      Invalid only for ti,pcm1690 because of no INT pin.

  '#sound-dai-cells':
    const: 0

required:
  - compatible
  - reg

allOf:
  - $ref: dai-common.yaml#
  - if:
      properties:
        compatible:
          contains:
            enum:
              - ti,pcm1690
    then:
      properties:
        interrupts: false

additionalProperties: false

examples:
  - |
    #include <dt-bindings/gpio/gpio.h>

    i2c {
        /* example for two devices with interrupt support */
        #address-cells = <1>;
        #size-cells = <0>;

        audio-codec@48 {
            compatible = "ti,pcm6240";
            reg = <0x48>, /* primary-device */
                  <0x4b>; /* secondary-device */
            #sound-dai-cells = <0>;
            reset-gpios = <&gpio1 10 GPIO_ACTIVE_HIGH>;
            interrupt-parent = <&gpio1>;
            interrupts = <15>;
        };
    };
...
