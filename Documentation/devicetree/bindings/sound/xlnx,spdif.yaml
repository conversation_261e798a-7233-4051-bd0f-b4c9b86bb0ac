# SPDX-License-Identifier: (GPL-2.0-only OR BSD-2-Clause)
%YAML 1.2
---
$id: http://devicetree.org/schemas/sound/xlnx,spdif.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: Xilinx SPDIF IP

description:
  The IP supports playback and capture of SPDIF audio.

maintainers:
  - <PERSON> <<EMAIL>>

allOf:
  - $ref: dai-common.yaml#

properties:
  compatible:
    enum:
      - xlnx,spdif-2.0

  reg:
    maxItems: 1

  interrupts:
    items:
      - description: SPDIF audio interrupt

  clock-names:
    items:
      - const: aud_clk_i
      - const: s_axi_aclk

  clocks:
    minItems: 1
    items:
      - description: input audio clock
      - description: clock for the AXI data stream

  xlnx,spdif-mode:
    $ref: /schemas/types.yaml#/definitions/uint32
    enum:
      - 0
      - 1
    description: |
      0 - receiver
      1 - transmitter

  xlnx,aud_clk_i:
    $ref: /schemas/types.yaml#/definitions/uint32
    description:
      Input audio clock frequency. It affects the sampling rate.

required:
  - compatible
  - reg
  - interrupts
  - clock-names
  - clocks

additionalProperties: false

examples:
  - |
    spdif@80010000 {
      compatible = "xlnx,spdif-2.0";
      reg = <0x80010000 0x10000>;
      clock-names = "aud_clk_i", "s_axi_aclk";
      clocks = <&misc_clk_0>, <&clk 71>;
      interrupt-parent = <&gic>;
      interrupts = <0 91 4>;
      xlnx,spdif-mode = <1>;
      xlnx,aud_clk_i = <49152913>;
    };

...
