# SPDX-License-Identifier: (GPL-2.0-only OR BSD-2-Clause)
%YAML 1.2
---
$id: http://devicetree.org/schemas/sound/st,stm32-spdifrx.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: STMicroelectronics STM32 S/PDIF receiver (SPDIFRX)

maintainers:
  - <PERSON> <<EMAIL>>

description: |
  The SPDIFRX peripheral, is designed to receive an S/PDIF flow compliant with
  IEC-60958 and IEC-61937.

allOf:
  - $ref: dai-common.yaml#

properties:
  compatible:
    enum:
      - st,stm32h7-spdifrx

  "#sound-dai-cells":
    const: 0

  reg:
    maxItems: 1

  clocks:
    maxItems: 1

  clock-names:
    items:
      - const: kclk

  interrupts:
    maxItems: 1

  dmas:
    items:
      - description: audio data capture DMA
      - description: IEC status bits capture DMA

  dma-names:
    items:
      - const: rx
      - const: rx-ctrl

  resets:
    maxItems: 1

  port:
    $ref: audio-graph-port.yaml#
    unevaluatedProperties: false

  access-controllers:
    minItems: 1
    maxItems: 2

required:
  - compatible
  - "#sound-dai-cells"
  - reg
  - clocks
  - clock-names
  - interrupts
  - dmas
  - dma-names

unevaluatedProperties: false

examples:
  - |
    #include <dt-bindings/interrupt-controller/arm-gic.h>
    #include <dt-bindings/clock/stm32mp1-clks.h>
    spdifrx: spdifrx@40004000 {
        compatible = "st,stm32h7-spdifrx";
        #sound-dai-cells = <0>;
        reg = <0x40004000 0x400>;
        clocks = <&rcc SPDIF_K>;
        clock-names = "kclk";
        interrupts = <GIC_SPI 97 IRQ_TYPE_LEVEL_HIGH>;
        dmas = <&dmamux1 2 93 0x400 0x0>,
               <&dmamux1 3 94 0x400 0x0>;
        dma-names = "rx", "rx-ctrl";
        pinctrl-0 = <&spdifrx_pins>;
        pinctrl-names = "default";
    };

...
