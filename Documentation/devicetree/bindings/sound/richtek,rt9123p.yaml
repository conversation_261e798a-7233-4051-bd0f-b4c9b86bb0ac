# SPDX-License-Identifier: GPL-2.0-only OR BSD-2-Clause
%YAML 1.2
---
$id: http://devicetree.org/schemas/sound/richtek,rt9123p.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: Richtek RT9123P Audio Amplifier

maintainers:
  - <PERSON><PERSON><PERSON> <<EMAIL>>

description:
  RT9123P is a RT9123 variant which does not support I2C control.

allOf:
  - $ref: dai-common.yaml#

properties:
  compatible:
    enum:
      - richtek,rt9123p

  '#sound-dai-cells':
    const: 0

  enable-gpios:
    maxItems: 1

  enable-delay-ms:
    description:
      Delay time for 'ENABLE' pin changes intended to make I2S clocks ready to
      prevent speaker pop noise. The unit is in millisecond.

required:
  - compatible
  - '#sound-dai-cells'

unevaluatedProperties: false

examples:
  - |
    #include <dt-bindings/gpio/gpio.h>

    amplifier {
        compatible = "richtek,rt9123p";
        enable-gpios = <&gpio 26 GPIO_ACTIVE_HIGH>;
        #sound-dai-cells = <0>;
    };
