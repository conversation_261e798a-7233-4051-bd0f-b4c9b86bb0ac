# SPDX-License-Identifier: (GPL-2.0-only OR BSD-2-Clause)
%YAML 1.2
---
$id: http://devicetree.org/schemas/sound/st,stm32-sai.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: STMicroelectronics STM32 Serial Audio Interface (SAI)

maintainers:
  - <PERSON> <<EMAIL>>

description:
  The SAI interface (Serial Audio Interface) offers a wide set of audio
  protocols as I2S standards, LSB or MSB-justified, PCM/DSP, TDM, and AC'97.
  The SAI contains two independent audio sub-blocks. Each sub-block has
  its own clock generator and I/O lines controller.

properties:
  compatible:
    enum:
      - st,stm32f4-sai
      - st,stm32h7-sai
      - st,stm32mp25-sai

  reg:
    items:
      - description: Base address and size of SAI common register set.
      - description: Base address and size of SAI identification register set.
    minItems: 1

  ranges:
    maxItems: 1

  interrupts:
    maxItems: 1

  resets:
    maxItems: 1

  "#address-cells":
    const: 1

  "#size-cells":
    const: 1

  clocks:
    minItems: 1
    maxItems: 3

  clock-names:
    minItems: 1
    maxItems: 3

  access-controllers:
    minItems: 1
    maxItems: 2

required:
  - compatible
  - reg
  - ranges
  - "#address-cells"
  - "#size-cells"
  - clocks
  - clock-names

patternProperties:
  "^audio-controller@[0-9a-f]+$":
    type: object
    additionalProperties: false
    description:
      Two subnodes corresponding to SAI sub-block instances A et B
      can be defined. Subnode can be omitted for unused sub-block.

    properties:
      compatible:
        description: Compatible for SAI sub-block A or B.
        pattern: "^st,stm32-sai-sub-[ab]$"

      "#sound-dai-cells":
        const: 0

      reg:
        maxItems: 1

      clocks:
        items:
          - description: sai_ck clock feeding the internal clock generator.
          - description: MCLK clock from a SAI set as master clock provider.
        minItems: 1

      clock-names:
        items:
          - const: sai_ck
          - const: MCLK
        minItems: 1

      dmas:
        maxItems: 1

      dma-names:
        description: |
          rx: SAI sub-block is configured as a capture DAI.
          tx: SAI sub-block is configured as a playback DAI.
        enum: [ rx, tx ]

      st,sync:
        description:
          Configure the SAI sub-block as slave of another SAI sub-block.
          By default SAI sub-block is in asynchronous mode.
          Must contain the phandle and index of the SAI sub-block providing
          the synchronization.
        $ref: /schemas/types.yaml#/definitions/phandle-array
        items:
          - items:
              - description: phandle of the SAI sub-block
              - description: index of the SAI sub-block

      st,iec60958:
        description:
          If set, support S/PDIF IEC6958 protocol for playback.
          IEC60958 protocol is not available for capture.
          By default, custom protocol is assumed, meaning that protocol is
          configured according to protocol defined in related DAI link node,
          such as i2s, left justified, right justified, dsp and pdm protocols.
        $ref: /schemas/types.yaml#/definitions/flag

      "#clock-cells":
        description: Configure the SAI device as master clock provider.
        const: 0

      port:
        $ref: audio-graph-port.yaml#
        unevaluatedProperties: false

    required:
      - compatible
      - "#sound-dai-cells"
      - reg
      - clocks
      - clock-names
      - dmas
      - dma-names

allOf:
  - if:
      properties:
        compatible:
          contains:
            const: st,stm32f4-sai
    then:
      properties:
        clocks:
          items:
            - description: x8k, SAI parent clock for sampling rates multiple of 8kHz.
            - description: x11k, SAI parent clock for sampling rates multiple of 11.025kHz.

        clock-names:
          items:
            - const: x8k
            - const: x11k

  - if:
      properties:
        compatible:
          contains:
            const: st,stm32mph7-sai
    then:
      properties:
        clocks:
          items:
            - description: pclk feeds the peripheral bus interface.
            - description: x8k, SAI parent clock for sampling rates multiple of 8kHz.
            - description: x11k, SAI parent clock for sampling rates multiple of 11.025kHz.

        clock-names:
          items:
            - const: pclk
            - const: x8k
            - const: x11k

  - if:
      properties:
        compatible:
          contains:
            const: st,stm32mp25-sai
    then:
      properties:
        clocks:
          items:
            - description: pclk feeds the peripheral bus interface.

        clock-names:
          items:
            - const: pclk

additionalProperties: false

examples:
  - |
    #include <dt-bindings/interrupt-controller/arm-gic.h>
    #include <dt-bindings/clock/stm32mp1-clks.h>
    #include <dt-bindings/reset/stm32mp1-resets.h>
    sai2: sai@4400b000 {
      compatible = "st,stm32h7-sai";
      #address-cells = <1>;
      #size-cells = <1>;
      ranges = <0 0x4400b000 0x400>;
      reg = <0x4400b000 0x4>, <0x4400b3f0 0x10>;
      clocks = <&rcc SAI2>, <&rcc PLL3_Q>, <&rcc PLL3_R>;
      clock-names = "pclk", "x8k", "x11k";
      pinctrl-names = "default", "sleep";
      pinctrl-0 = <&sai2a_pins_a>, <&sai2b_pins_b>;
      pinctrl-1 = <&sai2a_sleep_pins_a>, <&sai2b_sleep_pins_b>;

      sai2a: audio-controller@4400b004 {
        #sound-dai-cells = <0>;
        compatible = "st,stm32-sai-sub-a";
        reg = <0x4 0x1c>;
        dmas = <&dmamux1 89 0x400 0x01>;
        dma-names = "tx";
        clocks = <&rcc SAI2_K>;
        clock-names = "sai_ck";
      };
    };

...
