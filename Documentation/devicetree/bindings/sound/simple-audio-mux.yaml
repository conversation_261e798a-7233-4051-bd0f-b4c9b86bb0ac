# SPDX-License-Identifier: (GPL-2.0+ OR BSD-2-Clause)
%YAML 1.2
---
$id: http://devicetree.org/schemas/sound/simple-audio-mux.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: Simple Audio Multiplexer

maintainers:
  - <PERSON> <<EMAIL>>

description: |
  Simple audio multiplexers are driven using gpios, allowing to select which of
  their input line is connected to the output line.

allOf:
  - $ref: dai-common.yaml#

properties:
  compatible:
    const: simple-audio-mux

  mux-gpios:
    description: |
      GPIOs used to select the input line.

  state-labels:
    description: State of input line. default is "Input 1", "Input 2"
    $ref: /schemas/types.yaml#/definitions/string-array
    maxItems: 2

  idle-state:
    description: If present specifies the state when the mux is powered down
    $ref: /schemas/mux/mux-controller.yaml#/properties/idle-state

  sound-name-prefix: true

required:
  - compatible
  - mux-gpios

additionalProperties: false

examples:
  - |
    mux {
        compatible = "simple-audio-mux";
        mux-gpios = <&gpio 3 0>;
        state-labels = "Label_A", "Label_B";
        idle-state = <0>;
    };
