# SPDX-License-Identifier: (GPL-2.0-only OR BSD-2-Clause)
%YAML 1.2
---
$id: http://devicetree.org/schemas/sound/ti,pcm1681.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: Texas Instruments PCM1681 8-channel Digital-to-Analog Converter

maintainers:
  - <PERSON><PERSON><PERSON> <<EMAIL>>
  - <PERSON> <<EMAIL>>
  - <PERSON><PERSON><PERSON> <<EMAIL>>

allOf:
  - $ref: dai-common.yaml#

properties:
  compatible:
    const: ti,pcm1681

  reg:
    maxItems: 1

  "#sound-dai-cells":
    const: 0

required:
  - compatible
  - reg

unevaluatedProperties: false

examples:
  - |
    i2c {
        #address-cells = <1>;
        #size-cells = <0>;

        pcm1681: audio-codec@4c {
            compatible = "ti,pcm1681";
            reg = <0x4c>;
        };
    };
