# SPDX-License-Identifier: (GPL-2.0-only OR BSD-2-Clause)
%YAML 1.2
---
$id: http://devicetree.org/schemas/remoteproc/qcom,msm8916-mss-pil.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: Qualcomm MSM8916 MSS Peripheral Image Loader (and similar)

maintainers:
  - <PERSON> <<EMAIL>>

description:
  This document describes the hardware for a component that loads and boots
  firmware on the Qualcomm MSM8916 Modem Hexagon Core (and similar).

properties:
  compatible:
    oneOf:
      - enum:
          - qcom,msm8226-mss-pil
          - qcom,msm8909-mss-pil
          - qcom,msm8916-mss-pil
          - qcom,msm8926-mss-pil
          - qcom,msm8953-mss-pil
          - qcom,msm8974-mss-pil

      - const: qcom,q6v5-pil
        description: Deprecated, prefer using qcom,msm8916-mss-pil
        deprecated: true

  reg:
    items:
      - description: MSS QDSP6 registers
      - description: RMB registers

  reg-names:
    items:
      - const: qdsp6
      - const: rmb

  interrupts:
    items:
      - description: Watchdog interrupt
      - description: Fatal interrupt
      - description: Ready interrupt
      - description: Handover interrupt
      - description: Stop acknowledge interrupt

  interrupt-names:
    items:
      - const: wdog
      - const: fatal
      - const: ready
      - const: handover
      - const: stop-ack

  clocks:
    items:
      - description: Configuration interface (AXI) clock
      - description: Configuration bus (AHB) clock
      - description: Boot ROM (AHB) clock
      - description: XO proxy clock (control handed over after startup)

  clock-names:
    items:
      - const: iface
      - const: bus
      - const: mem
      - const: xo

  power-domains:
    items:
      - description: CX proxy power domain (control handed over after startup)
      - description: MX proxy power domain (control handed over after startup)
                     (not valid for qcom,msm8226-mss-pil, qcom,msm8926-mss-pil
                     and qcom,msm8974-mss-pil)
      - description: MSS proxy power domain (control handed over after startup)
                     (only valid for qcom,msm8953-mss-pil)
    minItems: 1

  power-domain-names:
    items:
      - const: cx
      - const: mx # not valid for qcom,msm8226-mss-pil, qcom-msm8926-mss-pil and qcom,msm8974-mss-pil
      - const: mss # only valid for qcom,msm8953-mss-pil
    minItems: 1

  pll-supply:
    description: PLL proxy supply (control handed over after startup)

  mss-supply:
    description: MSS power domain supply (only valid for qcom,msm8974-mss-pil)

  resets:
    items:
      - description: MSS restart control

  reset-names:
    items:
      - const: mss_restart

  qcom,smem-states:
    $ref: /schemas/types.yaml#/definitions/phandle-array
    description: States used by the AP to signal the Hexagon core
    items:
      - description: Stop modem

  qcom,smem-state-names:
    description: Names of the states used by the AP to signal the Hexagon core
    items:
      - const: stop

  qcom,ext-bhs-reg:
    $ref: /schemas/types.yaml#/definitions/phandle-array
    description: External power block headswitch (BHS) register
                 (only valid for qcom,msm8226-mss-pil)
    items:
      - items:
          - description: phandle to external BHS syscon region
          - description: offset to the external BHS register

  qcom,halt-regs:
    $ref: /schemas/types.yaml#/definitions/phandle-array
    description:
      Halt registers are used to halt transactions of various sub-components
      within MSS.
    items:
      - items:
          - description: phandle to TCSR syscon region
          - description: offset to the Q6 halt register
          - description: offset to the modem halt register
          - description: offset to the nc halt register

  memory-region:
    items:
      - description: MBA reserved region
      - description: MPSS reserved region

  firmware-name:
    $ref: /schemas/types.yaml#/definitions/string-array
    items:
      - description: Name of MBA firmware
      - description: Name of modem firmware

  bam-dmux:
    $ref: /schemas/net/qcom,bam-dmux.yaml#
    description:
      Qualcomm BAM Data Multiplexer (provides network interface to the modem)

  smd-edge:
    $ref: qcom,smd-edge.yaml#
    description:
      Qualcomm SMD subnode which represents communication edge, channels
      and devices related to the DSP.
    properties:
      label:
        enum:
          - modem
          - hexagon
    unevaluatedProperties: false

  # Deprecated properties
  cx-supply:
    description: CX power domain regulator supply (prefer using power-domains)
    deprecated: true

  mx-supply:
    description: MX power domain regulator supply (prefer using power-domains)
    deprecated: true

  mba:
    type: object
    additionalProperties: false
    description:
      MBA reserved region (prefer using memory-region with two items)
    properties:
      memory-region: true
    required:
      - memory-region
    deprecated: true

  mpss:
    type: object
    additionalProperties: false
    description:
      MPSS reserved region (prefer using memory-region with two items)
    properties:
      memory-region: true
    required:
      - memory-region
    deprecated: true

required:
  - compatible
  - reg
  - reg-names
  - interrupts
  - interrupt-names
  - clocks
  - clock-names
  - pll-supply
  - resets
  - reset-names
  - qcom,halt-regs
  - qcom,smem-states
  - qcom,smem-state-names
  - smd-edge

allOf:
  - if:
      properties:
        compatible:
          const: qcom,msm8953-mss-pil
    then:
      properties:
        power-domains:
          minItems: 3
        power-domain-names:
          minItems: 3
      required:
        - power-domains
        - power-domain-names

  - if:
      properties:
        compatible:
          contains:
            enum:
              - qcom,msm8909-mss-pil
              - qcom,msm8916-mss-pil
    then:
      properties:
        power-domains:
          minItems: 2
          maxItems: 2
        power-domain-names:
          minItems: 2
          maxItems: 2

  - if:
      properties:
        compatible:
          contains:
            enum:
              - qcom,msm8226-mss-pil
              - qcom,msm8926-mss-pil
              - qcom,msm8974-mss-pil
    then:
      properties:
        power-domains:
          maxItems: 1
        power-domain-names:
          maxItems: 1
      required:
        - mx-supply

  - if:
      properties:
        compatible:
          const: qcom,msm8226-mss-pil
    then:
      required:
        - qcom,ext-bhs-reg
    else:
      properties:
        qcom,ext-bhs-reg: false

  - if:
      properties:
        compatible:
          contains:
            enum:
              - qcom,msm8926-mss-pil
              - qcom,msm8974-mss-pil
    then:
      required:
        - mss-supply
    else:
      properties:
        mss-supply: false

  # Fallbacks for deprecated properties
  - oneOf:
      - required:
          - memory-region
      - required:
          - mba
          - mpss
  - oneOf:
      - required:
          - power-domains
          - power-domain-names
      - required:
          - cx-supply
          - mx-supply

additionalProperties: false

examples:
  - |
    #include <dt-bindings/clock/qcom,gcc-msm8916.h>
    #include <dt-bindings/interrupt-controller/arm-gic.h>
    #include <dt-bindings/power/qcom-rpmpd.h>

    remoteproc_mpss: remoteproc@4080000 {
        compatible = "qcom,msm8916-mss-pil";
        reg = <0x04080000 0x100>, <0x04020000 0x40>;
        reg-names = "qdsp6", "rmb";

        interrupts-extended = <&intc GIC_SPI 24 IRQ_TYPE_EDGE_RISING>,
                              <&hexagon_smp2p_in 0 IRQ_TYPE_EDGE_RISING>,
                              <&hexagon_smp2p_in 1 IRQ_TYPE_EDGE_RISING>,
                              <&hexagon_smp2p_in 2 IRQ_TYPE_EDGE_RISING>,
                              <&hexagon_smp2p_in 3 IRQ_TYPE_EDGE_RISING>;
        interrupt-names = "wdog", "fatal", "ready", "handover", "stop-ack";

        qcom,smem-states = <&hexagon_smp2p_out 0>;
        qcom,smem-state-names = "stop";
        qcom,halt-regs = <&tcsr 0x18000 0x19000 0x1a000>;

        clocks = <&gcc GCC_MSS_CFG_AHB_CLK>,
                 <&gcc GCC_MSS_Q6_BIMC_AXI_CLK>,
                 <&gcc GCC_BOOT_ROM_AHB_CLK>,
                 <&xo_board>;
        clock-names = "iface", "bus", "mem", "xo";

        power-domains = <&rpmpd MSM8916_VDDCX>, <&rpmpd MSM8916_VDDMX>;
        power-domain-names = "cx", "mx";
        pll-supply = <&pm8916_l7>;

        resets = <&scm 0>;
        reset-names = "mss_restart";

        memory-region = <&mba_mem>, <&mpss_mem>;

        smd-edge {
            interrupts = <GIC_SPI 25 IRQ_TYPE_EDGE_RISING>;

            qcom,smd-edge = <0>;
            qcom,ipc = <&apcs 8 12>;
            qcom,remote-pid = <1>;

            label = "hexagon";
        };
    };
