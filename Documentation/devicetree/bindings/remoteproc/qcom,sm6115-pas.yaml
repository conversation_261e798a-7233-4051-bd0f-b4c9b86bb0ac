# SPDX-License-Identifier: GPL-2.0 OR BSD-2-Clause
%YAML 1.2
---
$id: http://devicetree.org/schemas/remoteproc/qcom,sm6115-pas.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: Qualcomm SM6115 Peripheral Authentication Service

maintainers:
  - <PERSON><PERSON><PERSON> <and<PERSON><PERSON>@kernel.org>
  - <PERSON> <<EMAIL>>

description:
  Qualcomm SM6115 SoC Peripheral Authentication Service loads and boots
  firmware on the Qualcomm DSP Hexagon cores.

properties:
  compatible:
    oneOf:
      - enum:
          - qcom,sm6115-adsp-pas
          - qcom,sm6115-cdsp-pas
          - qcom,sm6115-mpss-pas

      - items:
          - const: qcom,qcm2290-adsp-pas
          - const: qcom,sm6115-adsp-pas

      - items:
          - const: qcom,qcm2290-mpss-pas
          - const: qcom,sm6115-mpss-pas

  reg:
    maxItems: 1

  clocks:
    items:
      - description: XO clock

  clock-names:
    items:
      - const: xo

  memory-region:
    maxItems: 1
    description: Reference to the reserved-memory for the Hexagon core

  smd-edge: false

  firmware-name:
    maxItems: 1
    description: Firmware name for the Hexagon core

required:
  - compatible
  - reg
  - memory-region

allOf:
  - $ref: /schemas/remoteproc/qcom,pas-common.yaml#
  - if:
      properties:
        compatible:
          contains:
            enum:
              - qcom,sm6115-adsp-pas
              - qcom,sm6115-cdsp-pas
    then:
      properties:
        interrupts:
          maxItems: 5
        interrupt-names:
          maxItems: 5
    else:
      properties:
        interrupts:
          minItems: 6
        interrupt-names:
          minItems: 6

  - if:
      properties:
        compatible:
          contains:
            enum:
              - qcom,sm6115-cdsp-pas
              - qcom,sm6115-mpss-pas
    then:
      properties:
        power-domains:
          items:
            - description: CX power domain
        power-domain-names:
          items:
            - const: cx

  - if:
      properties:
        compatible:
          contains:
            enum:
              - qcom,sm6115-adsp-pas
    then:
      properties:
        power-domains:
          items:
            - description: LPI CX power domain
            - description: LPI MX power domain
        power-domain-names:
          items:
            - const: lcx
            - const: lmx

unevaluatedProperties: false

examples:
  - |
    #include <dt-bindings/clock/qcom,rpmcc.h>
    #include <dt-bindings/interrupt-controller/arm-gic.h>
    #include <dt-bindings/interrupt-controller/irq.h>
    #include <dt-bindings/power/qcom-rpmpd.h>

    remoteproc@ab00000 {
        compatible = "qcom,sm6115-adsp-pas";
        reg = <0x0ab00000 0x100>;

        clocks = <&rpmcc RPM_SMD_XO_CLK_SRC>;
        clock-names = "xo";

        firmware-name = "qcom/sm6115/adsp.mbn";

        interrupts-extended = <&intc GIC_SPI 282 IRQ_TYPE_EDGE_RISING>,
                              <&adsp_smp2p_in 0 IRQ_TYPE_EDGE_RISING>,
                              <&adsp_smp2p_in 1 IRQ_TYPE_EDGE_RISING>,
                              <&adsp_smp2p_in 2 IRQ_TYPE_EDGE_RISING>,
                              <&adsp_smp2p_in 3 IRQ_TYPE_EDGE_RISING>;
        interrupt-names = "wdog", "fatal", "ready",
                          "handover", "stop-ack";

        memory-region = <&pil_adsp_mem>;

        power-domains = <&rpmpd SM6115_VDD_LPI_CX>,
                        <&rpmpd SM6115_VDD_LPI_MX>;

        qcom,smem-states = <&adsp_smp2p_out 0>;
        qcom,smem-state-names = "stop";

        glink-edge {
            interrupts = <GIC_SPI 277 IRQ_TYPE_EDGE_RISING>;
            label = "lpass";
            qcom,remote-pid = <2>;
            mboxes = <&apcs_glb 8>;

            /* ... */

        };
    };
