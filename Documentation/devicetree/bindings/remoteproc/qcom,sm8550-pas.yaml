# SPDX-License-Identifier: GPL-2.0 OR BSD-2-Clause
%YAML 1.2
---
$id: http://devicetree.org/schemas/remoteproc/qcom,sm8550-pas.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: Qualcomm SM8550 Peripheral Authentication Service

maintainers:
  - <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>

description:
  Qualcomm SM8550 SoC Peripheral Authentication Service loads and boots firmware
  on the Qualcomm DSP Hexagon cores.

properties:
  compatible:
    oneOf:
      - enum:
          - qcom,sdx75-mpss-pas
          - qcom,sm8550-adsp-pas
          - qcom,sm8550-cdsp-pas
          - qcom,sm8550-mpss-pas
          - qcom,sm8650-adsp-pas
          - qcom,sm8650-cdsp-pas
          - qcom,sm8650-mpss-pas
          - qcom,sm8750-mpss-pas
          - qcom,x1e80100-adsp-pas
          - qcom,x1e80100-cdsp-pas
      - items:
          - const: qcom,sm8750-adsp-pas
          - const: qcom,sm8550-adsp-pas
      - items:
          - const: qcom,sm8750-cdsp-pas
          - const: qcom,sm8650-cdsp-pas

  reg:
    maxItems: 1

  clocks:
    items:
      - description: XO clock

  clock-names:
    items:
      - const: xo

  qcom,qmp:
    $ref: /schemas/types.yaml#/definitions/phandle
    description: Reference to the AOSS side-channel message RAM.

  smd-edge: false

  firmware-name:
    $ref: /schemas/types.yaml#/definitions/string-array
    items:
      - description: Firmware name of the Hexagon core
      - description: Firmware name of the Hexagon Devicetree

  memory-region:
    minItems: 2
    items:
      - description: Memory region for main Firmware authentication
      - description: Memory region for Devicetree Firmware authentication
      - description: DSM Memory region
      - description: DSM Memory region 2
      - description: Memory region for Qlink Logging

required:
  - compatible
  - reg
  - memory-region

allOf:
  - $ref: /schemas/remoteproc/qcom,pas-common.yaml#
  - if:
      properties:
        compatible:
          enum:
            - qcom,sm8550-adsp-pas
            - qcom,sm8550-cdsp-pas
            - qcom,sm8650-adsp-pas
            - qcom,x1e80100-adsp-pas
            - qcom,x1e80100-cdsp-pas
    then:
      properties:
        interrupts:
          maxItems: 5
        interrupt-names:
          maxItems: 5
        memory-region:
          maxItems: 2
  - if:
      properties:
        compatible:
          contains:
            enum:
              - qcom,sm8750-adsp-pas
    then:
      properties:
        interrupts:
          maxItems: 6
        interrupt-names:
          maxItems: 6
        memory-region:
          maxItems: 2
  - if:
      properties:
        compatible:
          enum:
            - qcom,sm8650-cdsp-pas
    then:
      properties:
        interrupts:
          maxItems: 5
        interrupt-names:
          maxItems: 5
        memory-region:
          minItems: 3
          maxItems: 3

  - if:
      properties:
        compatible:
          contains:
            enum:
              - qcom,sm8750-cdsp-pas
    then:
      properties:
        interrupts:
          maxItems: 6
        interrupt-names:
          maxItems: 6
        memory-region:
          minItems: 3
          maxItems: 3

  - if:
      properties:
        compatible:
          enum:
            - qcom,sm8550-mpss-pas
    then:
      properties:
        interrupts:
          minItems: 6
        interrupt-names:
          minItems: 6
        memory-region:
          minItems: 3
          maxItems: 3
  - if:
      properties:
        compatible:
          enum:
            - qcom,sdx75-mpss-pas
            - qcom,sm8650-mpss-pas
    then:
      properties:
        interrupts:
          minItems: 6
        interrupt-names:
          minItems: 6
        memory-region:
          minItems: 5
          maxItems: 5

  - if:
      properties:
        compatible:
          enum:
            - qcom,sm8750-mpss-pas
    then:
      properties:
        interrupts:
          minItems: 6
        interrupt-names:
          minItems: 6
        memory-region:
          minItems: 4
          maxItems: 4

  - if:
      properties:
        compatible:
          contains:
            enum:
              - qcom,sm8550-adsp-pas
              - qcom,sm8650-adsp-pas
              - qcom,sm8750-adsp-pas
              - qcom,x1e80100-adsp-pas
    then:
      properties:
        power-domains:
          items:
            - description: LCX power domain
            - description: LMX power domain
        power-domain-names:
          items:
            - const: lcx
            - const: lmx

  - if:
      properties:
        compatible:
          enum:
            - qcom,sdx75-mpss-pas
            - qcom,sm8550-mpss-pas
            - qcom,sm8650-mpss-pas
            - qcom,sm8750-mpss-pas
    then:
      properties:
        power-domains:
          items:
            - description: CX power domain
            - description: MSS power domain
        power-domain-names:
          items:
            - const: cx
            - const: mss
  - if:
      properties:
        compatible:
          contains:
            enum:
              - qcom,sm8550-cdsp-pas
              - qcom,sm8650-cdsp-pas
              - qcom,x1e80100-cdsp-pas
    then:
      properties:
        power-domains:
          items:
            - description: CX power domain
            - description: MXC power domain
            - description: NSP power domain
        power-domain-names:
          items:
            - const: cx
            - const: mxc
            - const: nsp

unevaluatedProperties: false

examples:
  - |
    #include <dt-bindings/clock/qcom,rpmh.h>
    #include <dt-bindings/interrupt-controller/irq.h>
    #include <dt-bindings/mailbox/qcom-ipcc.h>

    remoteproc@30000000 {
        compatible = "qcom,sm8550-adsp-pas";
        reg = <0x030000000 0x100>;

        clocks = <&rpmhcc RPMH_CXO_CLK>;
        clock-names = "xo";

        interrupts-extended = <&pdc 6 IRQ_TYPE_EDGE_RISING>,
                              <&smp2p_adsp_in 0 IRQ_TYPE_EDGE_RISING>,
                              <&smp2p_adsp_in 1 IRQ_TYPE_EDGE_RISING>,
                              <&smp2p_adsp_in 2 IRQ_TYPE_EDGE_RISING>,
                              <&smp2p_adsp_in 3 IRQ_TYPE_EDGE_RISING>;
        interrupt-names = "wdog", "fatal", "ready",
                          "handover", "stop-ack";

        memory-region = <&adsp_mem>, <&dtb_adsp_mem>;

        firmware-name = "qcom/sm8550/adsp.mbn",
                        "qcom/sm8550/adsp_dtb.mbn";

        power-domains = <&rpmhpd_sm8550_lcx>,
                        <&rpmhpd_sm8550_lmx>;
        power-domain-names = "lcx", "lmx";

        qcom,qmp = <&aoss_qmp>;
        qcom,smem-states = <&smp2p_adsp_out 0>;
        qcom,smem-state-names = "stop";

        glink-edge {
            interrupts-extended = <&ipcc IPCC_CLIENT_LPASS
                                         IPCC_MPROC_SIGNAL_GLINK_QMP
                                         IRQ_TYPE_EDGE_RISING>;
            mboxes = <&ipcc IPCC_CLIENT_LPASS IPCC_MPROC_SIGNAL_GLINK_QMP>;

            label = "lpass";
            qcom,remote-pid = <2>;

            /* ... */
        };
    };
