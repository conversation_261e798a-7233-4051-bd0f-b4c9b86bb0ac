# SPDX-License-Identifier: (GPL-2.0 OR BSD-2-Clause)
%YAML 1.2
---
$id: http://devicetree.org/schemas/remoteproc/st,stm32-rproc.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: STMicroelectronics STM32 remote processor controller

description:
  This document defines the binding for the remoteproc component that loads and
  boots firmwares on the ST32MP family chipset.

maintainers:
  - <PERSON><PERSON><PERSON> <<EMAIL>>
  - <PERSON><PERSON><PERSON> <<EMAIL>>

properties:
  compatible:
    const: st,stm32mp1-m4

  reg:
    description:
      Address ranges of the RETRAM and MCU SRAM memories used by the remote
      processor.
    maxItems: 3

  resets:
    minItems: 1
    maxItems: 2

  reset-names:
    items:
      - const: mcu_rst
      - const: hold_boot
    minItems: 1

  st,syscfg-holdboot:
    description: remote processor reset hold boot
    $ref: /schemas/types.yaml#/definitions/phandle-array
    items:
      - items:
          - description: Phandle of syscon block
          - description: The offset of the hold boot setting register
          - description: The field mask of the hold boot

  st,syscfg-tz:
    deprecated: true
    description:
      Reference to the system configuration which holds the RCC trust zone mode
    $ref: /schemas/types.yaml#/definitions/phandle-array
    items:
      - items:
          - description: Phandle of syscon block
          - description: The offset of the trust zone setting register
          - description: The field mask of the trust zone state

  interrupts:
    description: Should contain the WWDG1 watchdog reset interrupt
    maxItems: 1

  wakeup-source: true

  mboxes:
    description:
      This property is required only if the rpmsg/virtio functionality is used.
    items:
      - description: |
          A channel (a) used to communicate through virtqueues with the
          remote proc.
          Bi-directional channel:
            - from local to remote = send message
            - from remote to local = send message ack
      - description: |
          A channel (b) working the opposite direction of channel (a)
      - description: |
          A channel (c) used by the local proc to notify the remote proc that it
          is about to be shut down.
          Unidirectional channel:
            - from local to remote, where ACK from the remote means that it is
              ready for shutdown
      - description: |
          A channel (d) used by the local proc to notify the remote proc that it
          has to stop interprocessor communication.
          Unidirectional channel:
            - from local to remote, where ACK from the remote means that communication
              as been stopped on the remote side.
    minItems: 1

  mbox-names:
    items:
      - const: vq0
      - const: vq1
      - const: shutdown
      - const: detach
    minItems: 1

  memory-region:
    description:
      List of phandles to the reserved memory regions associated with the
      remoteproc device. This is variable and describes the memories shared with
      the remote processor (e.g. remoteproc firmware and carveouts, rpmsg
      vrings, ...).
      (see ../reserved-memory/reserved-memory.txt)

  st,syscfg-pdds:
    $ref: /schemas/types.yaml#/definitions/phandle-array
    description: |
      Reference to the system configuration which holds the remote
    items:
      - items:
          - description: Phandle of syscon block
          - description: The offset of the power setting register
          - description: The field mask of the PDDS selection

  st,syscfg-m4-state:
    $ref: /schemas/types.yaml#/definitions/phandle-array
    description: |
      Reference to the tamp register which exposes the Cortex-M4 state.
    items:
      - items:
          - description: Phandle of syscon block with the tamp register
          - description: The offset of the tamp register
          - description: The field mask of the Cortex-M4 state

  st,syscfg-rsc-tbl:
    $ref: /schemas/types.yaml#/definitions/phandle-array
    description: |
      Reference to the tamp register which references the Cortex-M4
      resource table address.
    items:
      - items:
          - description: Phandle of syscon block with the tamp register
          - description: The offset of the tamp register
          - description: The field mask of the Cortex-M4 resource table address

  st,auto-boot:
    $ref: /schemas/types.yaml#/definitions/flag
    description:
      If defined, when remoteproc is probed, it loads the default firmware and
      starts the remote processor.

  firmware-name:
    maxItems: 1
    description: Default name of the remote processor firmware.

required:
  - compatible
  - reg
  - resets

allOf:
  - if:
      properties:
        reset-names:
          not:
            contains:
              const: hold_boot
    then:
      required:
        - st,syscfg-holdboot
    else:
      properties:
        st,syscfg-holdboot: false

additionalProperties: false

examples:
  - |
    #include <dt-bindings/reset/stm32mp1-resets.h>
    m4@10000000 {
      compatible = "st,stm32mp1-m4";
      reg = <0x10000000 0x40000>,
            <0x30000000 0x40000>,
            <0x38000000 0x10000>;
      resets = <&rcc MCU_R>;
      reset-names = "mcu_rst";
      /* Hold boot managed using system config*/
      st,syscfg-holdboot = <&rcc 0x10C 0x1>;
      st,syscfg-rsc-tbl = <&tamp 0x144 0xFFFFFFFF>;
      st,syscfg-m4-state = <&tamp 0x148 0xFFFFFFFF>;
    };
  - |
    #include <dt-bindings/reset/stm32mp1-resets.h>
    m4@10000000 {
      compatible = "st,stm32mp1-m4";
      reg = <0x10000000 0x40000>,
            <0x30000000 0x40000>,
            <0x38000000 0x10000>;
      /* Hold boot managed using SCMI reset controller */
      resets = <&scmi MCU_R>, <&scmi MCU_HOLD_BOOT_R>;
      reset-names = "mcu_rst", "hold_boot";
      st,syscfg-rsc-tbl = <&tamp 0x144 0xFFFFFFFF>;
      st,syscfg-m4-state = <&tamp 0x148 0xFFFFFFFF>;
    };

...
