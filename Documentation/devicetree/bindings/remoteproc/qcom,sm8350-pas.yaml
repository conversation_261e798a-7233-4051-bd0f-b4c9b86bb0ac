# SPDX-License-Identifier: GPL-2.0 OR BSD-2-Clause
%YAML 1.2
---
$id: http://devicetree.org/schemas/remoteproc/qcom,sm8350-pas.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: Qualcomm SM8350/SM8450 Peripheral Authentication Service

maintainers:
  - <PERSON><PERSON><PERSON><PERSON> Sad<PERSON>ivam <<EMAIL>>

description:
  Qualcomm SM8350/SM8450 SoC Peripheral Authentication Service loads and boots
  firmware on the Qualcomm DSP Hexagon cores.

properties:
  compatible:
    oneOf:
      - enum:
          - qcom,sar2130p-adsp-pas
          - qcom,sm8350-adsp-pas
          - qcom,sm8350-cdsp-pas
          - qcom,sm8350-slpi-pas
          - qcom,sm8350-mpss-pas
          - qcom,sm8450-adsp-pas
          - qcom,sm8450-cdsp-pas
          - qcom,sm8450-mpss-pas
          - qcom,sm8450-slpi-pas
      - items:
          - const: qcom,sc8280xp-slpi-pas
          - const: qcom,sm8350-slpi-pas

  reg:
    maxItems: 1

  clocks:
    items:
      - description: XO clock

  clock-names:
    items:
      - const: xo

  qcom,qmp:
    $ref: /schemas/types.yaml#/definitions/phandle
    description: Reference to the AOSS side-channel message RAM.

  smd-edge: false

  memory-region:
    maxItems: 1
    description: Reference to the reserved-memory for the Hexagon core

  firmware-name:
    maxItems: 1
    description: Firmware name for the Hexagon core

required:
  - compatible
  - reg
  - memory-region

allOf:
  - $ref: /schemas/remoteproc/qcom,pas-common.yaml#
  - if:
      properties:
        compatible:
          contains:
            enum:
              - qcom,sar2130p-adsp-pas
              - qcom,sm8350-adsp-pas
              - qcom,sm8350-cdsp-pas
              - qcom,sm8350-slpi-pas
              - qcom,sm8450-adsp-pas
              - qcom,sm8450-cdsp-pas
              - qcom,sm8450-slpi-pas
    then:
      properties:
        interrupts:
          maxItems: 5
        interrupt-names:
          maxItems: 5
    else:
      properties:
        interrupts:
          minItems: 6
        interrupt-names:
          minItems: 6

  - if:
      properties:
        compatible:
          enum:
            - qcom,sm8350-mpss-pas
            - qcom,sm8450-mpss-pas
    then:
      properties:
        power-domains:
          items:
            - description: CX power domain
            - description: MSS power domain
        power-domain-names:
          items:
            - const: cx
            - const: mss

  - if:
      properties:
        compatible:
          contains:
            enum:
              - qcom,sar2130p-adsp-pas
              - qcom,sm8350-adsp-pas
              - qcom,sm8350-slpi-pas
              - qcom,sm8450-adsp-pas
              - qcom,sm8450-slpi-pas
    then:
      properties:
        power-domains:
          items:
            - description: LCX power domain
            - description: LMX power domain
        power-domain-names:
          items:
            - const: lcx
            - const: lmx

  - if:
      properties:
        compatible:
          enum:
            - qcom,sm8350-cdsp-pas
            - qcom,sm8450-cdsp-pas
    then:
      properties:
        power-domains:
          items:
            - description: CX power domain
            - description: MXC power domain
        power-domain-names:
          items:
            - const: cx
            - const: mxc

unevaluatedProperties: false

examples:
  - |
    #include <dt-bindings/clock/qcom,rpmh.h>
    #include <dt-bindings/interrupt-controller/irq.h>
    #include <dt-bindings/mailbox/qcom-ipcc.h>
    #include <dt-bindings/power/qcom,rpmhpd.h>

    remoteproc@30000000 {
        compatible = "qcom,sm8450-adsp-pas";
        reg = <0x030000000 0x100>;

        clocks = <&rpmhcc RPMH_CXO_CLK>;
        clock-names = "xo";

        firmware-name = "qcom/sm8450/adsp.mbn";

        interrupts-extended = <&pdc 6 IRQ_TYPE_EDGE_RISING>,
                              <&smp2p_adsp_in 0 IRQ_TYPE_EDGE_RISING>,
                              <&smp2p_adsp_in 1 IRQ_TYPE_EDGE_RISING>,
                              <&smp2p_adsp_in 2 IRQ_TYPE_EDGE_RISING>,
                              <&smp2p_adsp_in 3 IRQ_TYPE_EDGE_RISING>;
        interrupt-names = "wdog", "fatal", "ready",
                          "handover", "stop-ack";

        memory-region = <&adsp_mem>;

        power-domains = <&rpmhpd RPMHPD_LCX>,
                        <&rpmhpd RPMHPD_LMX>;
        power-domain-names = "lcx", "lmx";

        qcom,qmp = <&aoss_qmp>;
        qcom,smem-states = <&smp2p_adsp_out 0>;
        qcom,smem-state-names = "stop";

        glink-edge {
            interrupts-extended = <&ipcc IPCC_CLIENT_LPASS
                                         IPCC_MPROC_SIGNAL_GLINK_QMP
                                         IRQ_TYPE_EDGE_RISING>;
            mboxes = <&ipcc IPCC_CLIENT_LPASS IPCC_MPROC_SIGNAL_GLINK_QMP>;

            label = "lpass";
            qcom,remote-pid = <2>;

            /* ... */
        };
    };
