# SPDX-License-Identifier: GPL-2.0 OR BSD-2-Clause
%YAML 1.2
---
$id: http://devicetree.org/schemas/remoteproc/qcom,sm8150-pas.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: Qualcomm SM8150/SM8250 Peripheral Authentication Service

maintainers:
  - <PERSON><PERSON><PERSON><PERSON> Sadhasivam <<EMAIL>>

description:
  Qualcomm SM8150/SM8250 SoC Peripheral Authentication Service loads and boots
  firmware on the Qualcomm DSP Hexagon cores.

properties:
  compatible:
    enum:
      - qcom,sc8180x-adsp-pas
      - qcom,sc8180x-cdsp-pas
      - qcom,sc8180x-slpi-pas
      - qcom,sm8150-adsp-pas
      - qcom,sm8150-cdsp-pas
      - qcom,sm8150-mpss-pas
      - qcom,sm8150-slpi-pas
      - qcom,sm8250-adsp-pas
      - qcom,sm8250-cdsp-pas
      - qcom,sm8250-slpi-pas

  reg:
    maxItems: 1

  clocks:
    items:
      - description: XO clock

  clock-names:
    items:
      - const: xo

  qcom,qmp:
    $ref: /schemas/types.yaml#/definitions/phandle
    description: Reference to the AOSS side-channel message RAM.

  memory-region:
    maxItems: 1
    description: Reference to the reserved-memory for the Hexagon core

  smd-edge: false

  firmware-name:
    maxItems: 1
    description: Firmware name for the Hexagon core

required:
  - compatible
  - reg
  - memory-region

allOf:
  - $ref: /schemas/remoteproc/qcom,pas-common.yaml#
  - if:
      properties:
        compatible:
          enum:
            - qcom,sc8180x-adsp-pas
            - qcom,sc8180x-cdsp-pas
            - qcom,sc8180x-slpi-pas
            - qcom,sm8150-adsp-pas
            - qcom,sm8150-cdsp-pas
            - qcom,sm8150-slpi-pas
            - qcom,sm8250-adsp-pas
            - qcom,sm8250-cdsp-pas
            - qcom,sm8250-slpi-pas
    then:
      properties:
        interrupts:
          maxItems: 5
        interrupt-names:
          maxItems: 5
    else:
      properties:
        interrupts:
          minItems: 6
        interrupt-names:
          minItems: 6

  - if:
      properties:
        compatible:
          enum:
            - qcom,sc8180x-adsp-pas
            - qcom,sc8180x-cdsp-pas
            - qcom,sm8150-adsp-pas
            - qcom,sm8150-cdsp-pas
            - qcom,sm8250-cdsp-pas
    then:
      properties:
        power-domains:
          items:
            - description: CX power domain
        power-domain-names:
          items:
            - const: cx

  - if:
      properties:
        compatible:
          enum:
            - qcom,sc8180x-mpss-pas
            - qcom,sm8150-mpss-pas
    then:
      properties:
        power-domains:
          items:
            - description: CX power domain
            - description: MSS power domain
        power-domain-names:
          items:
            - const: cx
            - const: mss

  - if:
      properties:
        compatible:
          enum:
            - qcom,sc8180x-slpi-pas
            - qcom,sm8150-slpi-pas
            - qcom,sm8250-adsp-pas
            - qcom,sm8250-slpi-pas
    then:
      properties:
        power-domains:
          items:
            - description: LCX power domain
            - description: LMX power domain
        power-domain-names:
          items:
            - const: lcx
            - const: lmx

unevaluatedProperties: false

examples:
  - |
    #include <dt-bindings/clock/qcom,rpmh.h>
    #include <dt-bindings/interrupt-controller/arm-gic.h>
    #include <dt-bindings/interrupt-controller/irq.h>
    #include <dt-bindings/power/qcom-rpmpd.h>

    remoteproc@17300000 {
        compatible = "qcom,sm8150-adsp-pas";
        reg = <0x17300000 0x4040>;

        clocks = <&rpmhcc RPMH_CXO_CLK>;
        clock-names = "xo";

        firmware-name = "qcom/sm8150/adsp.mbn";

        interrupts-extended = <&intc GIC_SPI 162 IRQ_TYPE_EDGE_RISING>,
                              <&adsp_smp2p_in 0 IRQ_TYPE_EDGE_RISING>,
                              <&adsp_smp2p_in 1 IRQ_TYPE_EDGE_RISING>,
                              <&adsp_smp2p_in 2 IRQ_TYPE_EDGE_RISING>,
                              <&adsp_smp2p_in 3 IRQ_TYPE_EDGE_RISING>;
        interrupt-names = "wdog", "fatal", "ready",
                          "handover", "stop-ack";

        memory-region = <&adsp_mem>;

        power-domains = <&rpmhpd SM8150_CX>;

        qcom,qmp = <&aoss_qmp>;
        qcom,smem-states = <&adsp_smp2p_out 0>;
        qcom,smem-state-names = "stop";

        glink-edge {
            interrupts = <GIC_SPI 156 IRQ_TYPE_EDGE_RISING>;
            label = "lpass";
            qcom,remote-pid = <2>;
            mboxes = <&apss_shared 8>;

            /* ... */

        };
    };
