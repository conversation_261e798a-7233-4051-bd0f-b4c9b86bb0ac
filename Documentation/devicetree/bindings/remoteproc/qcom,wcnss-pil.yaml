# SPDX-License-Identifier: (GPL-2.0 OR BSD-2-Clause)
%YAML 1.2
---
$id: http://devicetree.org/schemas/remoteproc/qcom,wcnss-pil.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: Qualcomm WCNSS Peripheral Image Loader

maintainers:
  - <PERSON><PERSON><PERSON> <and<PERSON><PERSON>@kernel.org>

description:
  This document defines the binding for a component that loads and boots
  firmware on the Qualcomm WCNSS core.

properties:
  compatible:
    description:
      Append "qcom,pronto" if the device is actually pronto, and not riva
    oneOf:
      - items:
          - enum:
              - qcom,pronto-v1-pil
              - qcom,pronto-v2-pil
              - qcom,pronto-v3-pil
          - const: qcom,pronto
      - const: qcom,riva-pil

  reg:
    maxItems: 3
    description:
      The base address and size of the CCU, DXE and PMU register blocks

  reg-names:
    items:
      - const: ccu
      - const: dxe
      - const: pmu

  interrupts:
    minItems: 2
    maxItems: 5

  interrupt-names:
    minItems: 2
    items:
      - const: wdog
      - const: fatal
      - const: ready
      - const: handover
      - const: stop-ack

  firmware-name:
    maxItems: 1
    description:
      Relative firmware image path for the WCNSS core. Defaults to
      "wcnss.mdt".

  vddpx-supply:
    description:
      PX regulator to be held on behalf of the booting of the WCNSS core

  vddmx-supply:
    description:
      MX regulator to be held on behalf of the booting of the WCNSS core.

  vddcx-supply:
    description:
      CX regulator to be held on behalf of the booting of the WCNSS core.

  power-domains:
    minItems: 1
    maxItems: 2

  power-domain-names:
    minItems: 1
    items:
      - const: cx
      - const: mx

  qcom,smem-states:
    $ref: /schemas/types.yaml#/definitions/phandle-array
    description:
      States used by the AP to signal the WCNSS core that it should shutdown
    items:
      - description: Stop the modem

  qcom,smem-state-names:
    description: The names of the state bits used for SMP2P output
    items:
      - const: stop

  memory-region:
    maxItems: 1
    description: reserved-memory for the WCNSS core

  smd-edge:
    $ref: /schemas/remoteproc/qcom,smd-edge.yaml#
    description:
      Qualcomm Shared Memory subnode which represents communication edge,
      channels and devices related to the ADSP.

  iris:
    type: object
    description:
      The iris subnode of the WCNSS PIL is used to describe the attached RF module
      and its resource dependencies.

    properties:
      compatible:
        enum:
          - qcom,wcn3620
          - qcom,wcn3660
          - qcom,wcn3660b
          - qcom,wcn3680

      clocks:
        minItems: 1
        items:
          - description: XO clock
          - description: RF clock

      clock-names:
        minItems: 1
        items:
          - const: xo
          - const: rf

      vddxo-supply:
        description:
          Reference to the regulator to be held on behalf of the booting WCNSS
          core

      vddrfa-supply:
        description:
          Reference to the regulator to be held on behalf of the booting WCNSS
          core

      vddpa-supply:
        description:
          Reference to the regulator to be held on behalf of the booting WCNSS
          core

      vdddig-supply:
        description:
          Reference to the regulator to be held on behalf of the booting WCNSS
          core

    required:
      - compatible
      - clocks
      - clock-names
      - vddxo-supply
      - vddrfa-supply
      - vddpa-supply
      - vdddig-supply

    additionalProperties: false

required:
  - compatible
  - reg
  - reg-names
  - interrupts
  - interrupt-names
  - iris
  - vddpx-supply
  - memory-region
  - smd-edge

additionalProperties: false

allOf:
  - if:
      properties:
        compatible:
          contains:
            const: qcom,riva-pil
    then:
      required:
        - vddcx-supply
        - vddmx-supply

  - if:
      properties:
        compatible:
          contains:
            enum:
              - qcom,pronto-v1-pil
              - qcom,pronto-v2-pil
    then:
      # CX and MX must be present either as power domains or regulators
      oneOf:
        # Both CX and MX represented as power domains
        - required:
            - power-domains
            - power-domain-names
          properties:
            power-domains:
              minItems: 2
            power-domain-names:
              minItems: 2
            vddmx-supply: false
            vddcx-supply: false
        # CX represented as power domain, MX as regulator
        - required:
            - power-domains
            - power-domain-names
            - vddmx-supply
          properties:
            power-domains:
              maxItems: 1
            power-domain-names:
              maxItems: 1
            vddcx-supply: false
        # Both CX and MX represented as regulators
        - required:
            - vddmx-supply
            - vddcx-supply
          properties:
            power-domains: false
            power-domain-names: false
            vddmx-supply:
              deprecated: true
              description: Deprecated for qcom,pronto-v1/2-pil
            vddcx-supply:
              deprecated: true
              description: Deprecated for qcom,pronto-v1/2-pil

  - if:
      properties:
        compatible:
          contains:
            enum:
              - qcom,pronto-v3-pil
    then:
      properties:
        power-domains:
          minItems: 2
        power-domain-names:
          minItems: 2
        vddmx-supply: false
        vddcx-supply: false

      required:
        - power-domains
        - power-domain-names

examples:
  - |
    #include <dt-bindings/interrupt-controller/arm-gic.h>
    #include <dt-bindings/clock/qcom,rpmcc.h>
    #include <dt-bindings/power/qcom-rpmpd.h>
    pronto@a21b000 {
        compatible = "qcom,pronto-v2-pil", "qcom,pronto";
        reg = <0x0a204000 0x2000>, <0x0a202000 0x1000>, <0x0a21b000 0x3000>;
        reg-names = "ccu", "dxe", "pmu";

        interrupts-extended = <&intc GIC_SPI 149 IRQ_TYPE_EDGE_RISING>,
                              <&wcnss_smp2p_in 0 IRQ_TYPE_EDGE_RISING>,
                              <&wcnss_smp2p_in 1 IRQ_TYPE_EDGE_RISING>,
                              <&wcnss_smp2p_in 2 IRQ_TYPE_EDGE_RISING>,
                              <&wcnss_smp2p_in 3 IRQ_TYPE_EDGE_RISING>;
        interrupt-names = "wdog", "fatal", "ready", "handover", "stop-ack";

        power-domains = <&rpmpd MSM8916_VDDCX>, <&rpmpd MSM8916_VDDMX>;
        power-domain-names = "cx", "mx";

        vddpx-supply = <&pm8916_l7>;

        qcom,smem-states = <&wcnss_smp2p_out 0>;
        qcom,smem-state-names = "stop";

        memory-region = <&wcnss_region>;

        pinctrl-names = "default";
        pinctrl-0 = <&wcnss_pin_a>;

        iris {
            compatible = "qcom,wcn3620";
            vddxo-supply = <&pm8916_l7>;
            vddrfa-supply = <&pm8916_s3>;
            vddpa-supply = <&pm8916_l9>;
            vdddig-supply = <&pm8916_l5>;

            clocks = <&rpmcc RPM_SMD_RF_CLK2>;
            clock-names = "xo";
        };

        smd-edge {
            interrupts = <GIC_SPI 142 IRQ_TYPE_EDGE_RISING>;

            qcom,ipc = <&apcs 8 17>;
            qcom,smd-edge = <6>;
            qcom,remote-pid = <4>;

            label = "pronto";

            wcnss_ctrl: wcnss {
                compatible = "qcom,wcnss";
                qcom,smd-channels = "WCNSS_CTRL";

                qcom,mmio = <&pronto>;

                bluetooth {
                    compatible = "qcom,wcnss-bt";
                };

                wifi {
                    compatible = "qcom,wcnss-wlan";

                    interrupts = <GIC_SPI 145 IRQ_TYPE_LEVEL_HIGH>,
                                 <GIC_SPI 146 IRQ_TYPE_LEVEL_HIGH>;
                    interrupt-names = "tx", "rx";

                    qcom,smem-states = <&apps_smsm 10>, <&apps_smsm 9>;
                    qcom,smem-state-names = "tx-enable", "tx-rings-empty";
                };
            };
        };
    };
