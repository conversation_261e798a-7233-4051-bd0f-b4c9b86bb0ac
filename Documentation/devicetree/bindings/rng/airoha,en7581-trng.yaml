# SPDX-License-Identifier: (GPL-2.0-only OR BSD-2-Clause)
%YAML 1.2
---
$id: http://devicetree.org/schemas/rng/airoha,en7581-trng.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: Airoha EN7851 True Random Number Generator

maintainers:
  - <PERSON> <<EMAIL>>

properties:
  compatible:
    const: airoha,en7581-trng

  reg:
    maxItems: 1

  interrupts:
    maxItems: 1

required:
  - compatible
  - reg
  - interrupts

additionalProperties: false

examples:
  - |
    #include <dt-bindings/interrupt-controller/irq.h>
    #include <dt-bindings/interrupt-controller/arm-gic.h>

    rng@1faa1000 {
        compatible = "airoha,en7581-trng";
        reg = <0x1faa1000 0x1000>;
        interrupts = <GIC_SPI 19 IRQ_TYPE_LEVEL_HIGH>;
    };
