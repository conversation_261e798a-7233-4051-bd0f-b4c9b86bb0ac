# SPDX-License-Identifier: (GPL-2.0-only OR BSD-2-Clause)
%YAML 1.2
---
$id: http://devicetree.org/schemas/rng/imx-rng.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: Freescale RNGA/RNGB/RNGC (Random Number Generator Versions A, B and C)

maintainers:
  - <PERSON> <<EMAIL>>

properties:
  compatible:
    oneOf:
      - const: fsl,imx21-rnga
      - const: fsl,imx25-rngb
      - const: fsl,imx31-rnga
      - items:
          - const: fsl,imx21-rnga
      - items:
          - enum:
              - fsl,imx6sl-rngb
              - fsl,imx6sll-rngb
              - fsl,imx6ull-rngb
          - const: fsl,imx25-rngb
      - const: fsl,imx35-rngc

  reg:
    maxItems: 1

  interrupts:
    maxItems: 1

  clocks:
    maxItems: 1

required:
  - compatible
  - reg
  - interrupts
  - clocks

additionalProperties: false

examples:
  - |
    rngb@53fb0000 {
        compatible = "fsl,imx25-rngb";
        reg = <0x53fb0000 0x4000>;
        clocks = <&clks 109>;
        interrupts = <22>;
    };
