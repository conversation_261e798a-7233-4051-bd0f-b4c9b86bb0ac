# SPDX-License-Identifier: (GPL-2.0-only OR BSD-2-Clause)
%YAML 1.2
---
$id: http://devicetree.org/schemas/rng/rockchip,rk3588-rng.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: Rockchip RK3576/RK3588 TRNG

description: True Random Number Generator on Rockchip RK3576/RK3588 SoCs

maintainers:
  - <PERSON> <<EMAIL>>

properties:
  compatible:
    enum:
      - rockchip,rk3576-rng
      - rockchip,rk3588-rng

  reg:
    maxItems: 1

  clocks:
    items:
      - description: TRNG AHB clock

  interrupts:
    maxItems: 1

  resets:
    maxItems: 1

required:
  - compatible
  - reg
  - clocks
  - interrupts

additionalProperties: false

examples:
  - |
    #include <dt-bindings/clock/rockchip,rk3588-cru.h>
    #include <dt-bindings/interrupt-controller/arm-gic.h>
    #include <dt-bindings/interrupt-controller/irq.h>
    #include <dt-bindings/reset/rockchip,rk3588-cru.h>
    bus {
      #address-cells = <2>;
      #size-cells = <2>;

      rng@fe378000 {
        compatible = "rockchip,rk3588-rng";
        reg = <0x0 0xfe378000 0x0 0x200>;
        interrupts = <GIC_SPI 400 IRQ_TYPE_LEVEL_HIGH 0>;
        clocks = <&scmi_clk SCMI_HCLK_SECURE_NS>;
        resets = <&scmi_reset SCMI_SRST_H_TRNG_NS>;
      };
    };

...
