# SPDX-License-Identifier: (GPL-2.0 OR BSD-2-Clause)
%YAML 1.2
---
$id: http://devicetree.org/schemas/rng/brcm,bcm74110-rng.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: BCM74110 Random number generator

description:
  Random number generator used on the BCM74110.

maintainers:
  - <PERSON> <<EMAIL>>
  - <PERSON><PERSON><PERSON> <<EMAIL>>

properties:
  compatible:
    enum:
      - brcm,bcm74110-rng

  reg:
    maxItems: 1

required:
  - compatible
  - reg

additionalProperties: false

examples:
  - |
    rng@83ba000 {
        compatible = "brcm,bcm74110-rng";
        reg = <0x83ba000 0x14>;
    };
