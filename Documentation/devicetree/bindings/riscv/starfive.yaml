# SPDX-License-Identifier: (GPL-2.0-only OR BSD-2-Clause)
%YAML 1.2
---
$id: http://devicetree.org/schemas/riscv/starfive.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: StarFive SoC-based boards

maintainers:
  - <PERSON> <<EMAIL>>
  - <PERSON> <<EMAIL>>

description:
  StarFive SoC-based boards

properties:
  $nodename:
    const: '/'
  compatible:
    oneOf:
      - items:
          - enum:
              - beagle,beaglev-starlight-jh7100-r0
              - starfive,visionfive-v1
          - const: starfive,jh7100

      - items:
          - enum:
              - deepcomputing,fml13v01
              - milkv,mars
              - pine64,star64
              - starfive,visionfive-2-v1.2a
              - starfive,visionfive-2-v1.3b
          - const: starfive,jh7110

additionalProperties: true

...
