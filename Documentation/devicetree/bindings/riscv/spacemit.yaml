# SPDX-License-Identifier: (GPL-2.0-only OR BSD-2-Clause)
%YAML 1.2
---
$id: http://devicetree.org/schemas/riscv/spacemit.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: SpacemiT SoC-based boards

maintainers:
  - <PERSON><PERSON> <<EMAIL>>
  - <PERSON><PERSON><PERSON> <<EMAIL>>

description:
  SpacemiT SoC-based boards

properties:
  $nodename:
    const: '/'
  compatible:
    oneOf:
      - items:
          - enum:
              - bananapi,bpi-f3
              - milkv,jupiter
          - const: spacemit,k1

additionalProperties: true

...
