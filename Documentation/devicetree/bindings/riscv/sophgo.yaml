# SPDX-License-Identifier: (GPL-2.0-only OR BSD-2-Clause)
%YAML 1.2
---
$id: http://devicetree.org/schemas/riscv/sophgo.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: Sophgo SoC-based boards

maintainers:
  - <PERSON> <<EMAIL>>
  - <PERSON> <<EMAIL>>

description:
  Sophgo SoC-based boards

properties:
  $nodename:
    const: '/'
  compatible:
    oneOf:
      - items:
          - enum:
              - milkv,duo
          - const: sophgo,cv1800b
      - items:
          - enum:
              - sophgo,huashan-pi
          - const: sophgo,cv1812h
      - items:
          - enum:
              - sipeed,licheerv-nano-b
          - const: sipeed,licheerv-nano
          - const: sophgo,sg2002
      - items:
          - enum:
              - milkv,pioneer
          - const: sophgo,sg2042
      - items:
          - enum:
              - sophgo,srd3-10
          - const: sophgo,sg2044

additionalProperties: true

...
