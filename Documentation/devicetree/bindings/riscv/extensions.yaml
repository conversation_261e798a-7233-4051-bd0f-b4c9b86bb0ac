# SPDX-License-Identifier: (GPL-2.0 OR MIT)
%YAML 1.2
---
$id: http://devicetree.org/schemas/riscv/extensions.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: RISC-V ISA extensions

maintainers:
  - <PERSON> <<EMAIL>>
  - <PERSON> <<EMAIL>>
  - <PERSON> <<EMAIL>>

description: |
  RISC-V has a large number of extensions, some of which are "standard"
  extensions, meaning they are ratified by RISC-V International, and others
  are "vendor" extensions.
  This document defines properties that indicate whether a hart supports a
  given extension.

  Once a standard extension has been ratified, no changes in behaviour can be
  made without the creation of a new extension.
  The properties for standard extensions therefore map to their originally
  ratified states, with the exception of the I, Zicntr & Zihpm extensions.
  See the "i" property for more information.

select:
  properties:
    compatible:
      contains:
        const: riscv

properties:
  riscv,isa:
    description:
      Identifies the specific RISC-V instruction set architecture
      supported by the hart.  These are documented in the RISC-V
      User-Level ISA document, available from
      https://riscv.org/specifications/

      Due to revisions of the ISA specification, some deviations
      have arisen over time.
      Notably, riscv,isa was defined prior to the creation of the
      Zicntr, Zicsr, Zifencei and Zihpm extensions and thus "i"
      implies "zicntr_zicsr_zifencei_zihpm".

      While the isa strings in ISA specification are case
      insensitive, letters in the riscv,isa string must be all
      lowercase.
    $ref: /schemas/types.yaml#/definitions/string
    pattern: ^rv(?:64|32)imaf?d?q?c?b?k?j?p?v?h?(?:[hsxz](?:[0-9a-z])+)?(?:_[hsxz](?:[0-9a-z])+)*$
    deprecated: true

  riscv,isa-base:
    description:
      The base ISA implemented by this hart, as described by the 20191213
      version of the unprivileged ISA specification.
    enum:
      - rv32i
      - rv64i

  riscv,isa-extensions:
    $ref: /schemas/types.yaml#/definitions/string-array
    minItems: 1
    description: Extensions supported by the hart.
    items:
      anyOf:
        # single letter extensions, in canonical order
        - const: i
          description: |
            The base integer instruction set, as ratified in the 20191213
            version of the unprivileged ISA specification.

            This does not include Chapter 10, "Counters", which was moved into
            the Zicntr and Zihpm extensions after the ratification of the
            20191213 version of the unprivileged specification.

        - const: m
          description:
            The standard M extension for integer multiplication and division, as
            ratified in the 20191213 version of the unprivileged ISA
            specification.

        - const: a
          description:
            The standard A extension for atomic instructions, as ratified in the
            20191213 version of the unprivileged ISA specification.

        - const: f
          description:
            The standard F extension for single-precision floating point, as
            ratified in the 20191213 version of the unprivileged ISA
            specification.

        - const: d
          description:
            The standard D extension for double-precision floating-point, as
            ratified in the 20191213 version of the unprivileged ISA
            specification.

        - const: q
          description:
            The standard Q extension for quad-precision floating-point, as
            ratified in the 20191213 version of the unprivileged ISA
            specification.

        - const: c
          description:
            The standard C extension for compressed instructions, as ratified in
            the 20191213 version of the unprivileged ISA specification.

        - const: v
          description:
            The standard V extension for vector operations, as ratified
            in-and-around commit 7a6c8ae ("Fix text that describes vfmv.v.f
            encoding") of the riscv-v-spec.

        - const: h
          description:
            The standard H extension for hypervisors as ratified in the 20191213
            version of the privileged ISA specification.

        # multi-letter extensions, sorted alphanumerically
        - const: smaia
          description: |
            The standard Smaia supervisor-level extension for the advanced
            interrupt architecture for machine-mode-visible csr and behavioural
            changes to interrupts as frozen at commit ccbddab ("Merge pull
            request #42 from riscv/jhauser-2023-RC4") of riscv-aia.

        - const: smmpm
          description: |
            The standard Smmpm extension for M-mode pointer masking as
            ratified at commit d70011dde6c2 ("Update to ratified state")
            of riscv-j-extension.

        - const: smnpm
          description: |
            The standard Smnpm extension for next-mode pointer masking as
            ratified at commit d70011dde6c2 ("Update to ratified state")
            of riscv-j-extension.

        - const: smstateen
          description: |
            The standard Smstateen extension for controlling access to CSRs
            added by other RISC-V extensions in H/S/VS/U/VU modes and as
            ratified at commit a28bfae (Ratified (#7)) of riscv-state-enable.

        - const: ssaia
          description: |
            The standard Ssaia supervisor-level extension for the advanced
            interrupt architecture for supervisor-mode-visible csr and
            behavioural changes to interrupts as frozen at commit ccbddab
            ("Merge pull request #42 from riscv/jhauser-2023-RC4") of riscv-aia.

        - const: sscofpmf
          description: |
            The standard Sscofpmf supervisor-level extension for count overflow
            and mode-based filtering as ratified at commit 01d1df0 ("Add ability
            to manually trigger workflow. (#2)") of riscv-count-overflow.

        - const: ssnpm
          description: |
            The standard Ssnpm extension for next-mode pointer masking as
            ratified at commit d70011dde6c2 ("Update to ratified state")
            of riscv-j-extension.

        - const: sstc
          description: |
            The standard Sstc supervisor-level extension for time compare as
            ratified at commit 3f9ed34 ("Add ability to manually trigger
            workflow. (#2)") of riscv-time-compare.

        - const: svade
          description: |
            The standard Svade supervisor-level extension for SW-managed PTE A/D
            bit updates as ratified in the 20240213 version of the privileged
            ISA specification.

            Both Svade and Svadu extensions control the hardware behavior when
            the PTE A/D bits need to be set. The default behavior for the four
            possible combinations of these extensions in the device tree are:
            1) Neither Svade nor Svadu present in DT => It is technically
               unknown whether the platform uses Svade or Svadu. Supervisor
               software should be prepared to handle either hardware updating
               of the PTE A/D bits or page faults when they need updated.
            2) Only Svade present in DT => Supervisor must assume Svade to be
               always enabled.
            3) Only Svadu present in DT => Supervisor must assume Svadu to be
               always enabled.
            4) Both Svade and Svadu present in DT => Supervisor must assume
               Svadu turned-off at boot time. To use Svadu, supervisor must
               explicitly enable it using the SBI FWFT extension.

        - const: svadu
          description: |
            The standard Svadu supervisor-level extension for hardware updating
            of PTE A/D bits as ratified in the 20240528 version of the
            privileged ISA specification. Please refer to Svade dt-binding
            description for more details.

        - const: svinval
          description:
            The standard Svinval supervisor-level extension for fine-grained
            address-translation cache invalidation as ratified in the 20191213
            version of the privileged ISA specification.

        - const: svnapot
          description:
            The standard Svnapot supervisor-level extensions for napot
            translation contiguity as ratified in the 20191213 version of the
            privileged ISA specification.

        - const: svpbmt
          description:
            The standard Svpbmt supervisor-level extensions for page-based
            memory types as ratified in the 20191213 version of the privileged
            ISA specification.

        - const: svvptc
          description:
            The standard Svvptc supervisor-level extension for
            address-translation cache behaviour with respect to invalid entries
            as ratified at commit 4a69197e5617 ("Update to ratified state") of
            riscv-svvptc.

        - const: zaamo
          description: |
            The standard Zaamo extension for atomic memory operations as
            ratified at commit e87412e621f1 ("integrate Zaamo and Zalrsc text
            (#1304)") of the unprivileged ISA specification.

        - const: zabha
          description: |
            The Zabha extension for Byte and Halfword Atomic Memory Operations
            as ratified at commit 49f49c842ff9 ("Update to Rafified state") of
            riscv-zabha.

        - const: zacas
          description: |
            The Zacas extension for Atomic Compare-and-Swap (CAS) instructions
            is supported as ratified at commit 5059e0ca641c ("update to
            ratified") of the riscv-zacas.

        - const: zalrsc
          description: |
            The standard Zalrsc extension for load-reserved/store-conditional as
            ratified at commit e87412e621f1 ("integrate Zaamo and Zalrsc text
            (#1304)") of the unprivileged ISA specification.

        - const: zawrs
          description: |
            The Zawrs extension for entering a low-power state or for trapping
            to a hypervisor while waiting on a store to a memory location, as
            ratified in commit 98918c844281 ("Merge pull request #1217 from
            riscv/zawrs") of riscv-isa-manual.

        - const: zba
          description: |
            The standard Zba bit-manipulation extension for address generation
            acceleration instructions as ratified at commit 6d33919 ("Merge pull
            request #158 from hirooih/clmul-fix-loop-end-condition") of
            riscv-bitmanip.

        - const: zbb
          description: |
            The standard Zbb bit-manipulation extension for basic bit-manipulation
            as ratified at commit 6d33919 ("Merge pull request #158 from
            hirooih/clmul-fix-loop-end-condition") of riscv-bitmanip.

        - const: zbc
          description: |
            The standard Zbc bit-manipulation extension for carry-less
            multiplication as ratified at commit 6d33919 ("Merge pull request
            #158 from hirooih/clmul-fix-loop-end-condition") of riscv-bitmanip.

        - const: zbkb
          description:
            The standard Zbkb bitmanip instructions for cryptography as ratified
            in version 1.0 of RISC-V Cryptography Extensions Volume I
            specification.

        - const: zbkc
          description:
            The standard Zbkc carry-less multiply instructions as ratified
            in version 1.0 of RISC-V Cryptography Extensions Volume I
            specification.

        - const: zbkx
          description:
            The standard Zbkx crossbar permutation instructions as ratified
            in version 1.0 of RISC-V Cryptography Extensions Volume I
            specification.

        - const: zbs
          description: |
            The standard Zbs bit-manipulation extension for single-bit
            instructions as ratified at commit 6d33919 ("Merge pull request #158
            from hirooih/clmul-fix-loop-end-condition") of riscv-bitmanip.

        - const: zca
          description: |
            The Zca extension part of Zc* standard extensions for code size
            reduction, as ratified in commit 8be3419c1c0 ("Zcf doesn't exist on
            RV64 as it contains no instructions") of riscv-code-size-reduction,
            merged in the riscv-isa-manual by commit dbc79cf28a2 ("Initial seed
            of zc.adoc to src tree.").

        - const: zcb
          description: |
            The Zcb extension part of Zc* standard extensions for code size
            reduction, as ratified in commit 8be3419c1c0 ("Zcf doesn't exist on
            RV64 as it contains no instructions") of riscv-code-size-reduction,
            merged in the riscv-isa-manual by commit dbc79cf28a2 ("Initial seed
            of zc.adoc to src tree.").

        - const: zcd
          description: |
            The Zcd extension part of Zc* standard extensions for code size
            reduction, as ratified in commit 8be3419c1c0 ("Zcf doesn't exist on
            RV64 as it contains no instructions") of riscv-code-size-reduction,
            merged in the riscv-isa-manual by commit dbc79cf28a2 ("Initial seed
            of zc.adoc to src tree.").

        - const: zcf
          description: |
            The Zcf extension part of Zc* standard extensions for code size
            reduction, as ratified in commit 8be3419c1c0 ("Zcf doesn't exist on
            RV64 as it contains no instructions") of riscv-code-size-reduction,
            merged in the riscv-isa-manual by commit dbc79cf28a2 ("Initial seed
            of zc.adoc to src tree.").

        - const: zcmop
          description:
            The standard Zcmop extension version 1.0, as ratified in commit
            c732a4f39a4 ("Zcmop is ratified/1.0") of the riscv-isa-manual.

        - const: zfa
          description:
            The standard Zfa extension for additional floating point
            instructions, as ratified in commit 056b6ff ("Zfa is ratified") of
            riscv-isa-manual.

        - const: zfbfmin
          description:
            The standard Zfbfmin extension which provides minimal support for
            16-bit half-precision brain floating-point instructions, as ratified
            in commit 4dc23d62 ("Added Chapter title to BF16") of riscv-isa-manual.

        - const: zfh
          description:
            The standard Zfh extension for 16-bit half-precision binary
            floating-point instructions, as ratified in commit 64074bc ("Update
            version numbers for Zfh/Zfinx") of riscv-isa-manual.

        - const: zfhmin
          description:
            The standard Zfhmin extension which provides minimal support for
            16-bit half-precision binary floating-point instructions, as ratified
            in commit 64074bc ("Update version numbers for Zfh/Zfinx") of
            riscv-isa-manual.

        - const: ziccrse
          description:
            The standard Ziccrse extension which provides forward progress
            guarantee on LR/SC sequences, as ratified in commit b1d806605f87
            ("Updated to ratified state.") of the riscv profiles specification.

        - const: zk
          description:
            The standard Zk Standard Scalar cryptography extension as ratified
            in version 1.0 of RISC-V Cryptography Extensions Volume I
            specification.

        - const: zkn
          description:
            The standard Zkn NIST algorithm suite extensions as ratified in
            version 1.0 of RISC-V Cryptography Extensions Volume I
            specification.

        - const: zknd
          description: |
            The standard Zknd for NIST suite: AES decryption instructions as
            ratified in version 1.0 of RISC-V Cryptography Extensions Volume I
            specification.

        - const: zkne
          description: |
            The standard Zkne for NIST suite: AES encryption instructions as
            ratified in version 1.0 of RISC-V Cryptography Extensions Volume I
            specification.

        - const: zknh
          description: |
            The standard Zknh for NIST suite: hash function instructions as
            ratified in version 1.0 of RISC-V Cryptography Extensions Volume I
            specification.

        - const: zkr
          description:
            The standard Zkr entropy source extension as ratified in version
            1.0 of RISC-V Cryptography Extensions Volume I specification.
            This string being present means that the CSR associated to this
            extension is accessible at the privilege level to which that
            device-tree has been provided.

        - const: zks
          description:
            The standard Zks ShangMi algorithm suite extensions as ratified in
            version 1.0 of RISC-V Cryptography Extensions Volume I
            specification.

        - const: zksed
          description: |
            The standard Zksed for ShangMi suite: SM4 block cipher instructions
            as ratified in version 1.0 of RISC-V Cryptography Extensions
            Volume I specification.

        - const: zksh
          description: |
            The standard Zksh for ShangMi suite: SM3 hash function instructions
            as ratified in version 1.0 of RISC-V Cryptography Extensions
            Volume I specification.

        - const: zkt
          description:
            The standard Zkt for data independent execution latency as ratified
            in version 1.0 of RISC-V Cryptography Extensions Volume I
            specification.

        - const: zicbom
          description:
            The standard Zicbom extension for base cache management operations as
            ratified in commit 3dd606f ("Create cmobase-v1.0.pdf") of riscv-CMOs.

        - const: zicbop
          description:
            The standard Zicbop extension for cache-block prefetch instructions
            as ratified in commit 3dd606f ("Create cmobase-v1.0.pdf") of
            riscv-CMOs.

        - const: zicboz
          description:
            The standard Zicboz extension for cache-block zeroing as ratified
            in commit 3dd606f ("Create cmobase-v1.0.pdf") of riscv-CMOs.

        - const: zicntr
          description:
            The standard Zicntr extension for base counters and timers, as
            ratified in the 20191213 version of the unprivileged ISA
            specification.

        - const: zicond
          description:
            The standard Zicond extension for conditional arithmetic and
            conditional-select/move operations as ratified in commit 95cf1f9
            ("Add changes requested by Ved during signoff") of riscv-zicond.

        - const: zicsr
          description: |
            The standard Zicsr extension for control and status register
            instructions, as ratified in the 20191213 version of the
            unprivileged ISA specification.

            This does not include Chapter 10, "Counters", which documents
            special case read-only CSRs, that were moved into the Zicntr and
            Zihpm extensions after the ratification of the 20191213 version of
            the unprivileged specification.

        - const: zifencei
          description:
            The standard Zifencei extension for instruction-fetch fence, as
            ratified in the 20191213 version of the unprivileged ISA
            specification.

        - const: zihintpause
          description:
            The standard Zihintpause extension for pause hints, as ratified in
            commit d8ab5c7 ("Zihintpause is ratified") of the riscv-isa-manual.

        - const: zihintntl
          description:
            The standard Zihintntl extension for non-temporal locality hints, as
            ratified in commit 0dc91f5 ("Zihintntl is ratified") of the
            riscv-isa-manual.

        - const: zihpm
          description:
            The standard Zihpm extension for hardware performance counters, as
            ratified in the 20191213 version of the unprivileged ISA
            specification.

        - const: zimop
          description:
            The standard Zimop extension version 1.0, as ratified in commit
            58220614a5f ("Zimop is ratified/1.0") of the riscv-isa-manual.

        - const: ztso
          description:
            The standard Ztso extension for total store ordering, as ratified
            in commit 2e5236 ("Ztso is now ratified.") of the
            riscv-isa-manual.

        - const: zvbb
          description:
            The standard Zvbb extension for vectored basic bit-manipulation
            instructions, as ratified in commit 56ed795 ("Update
            riscv-crypto-spec-vector.adoc") of riscv-crypto.

        - const: zvbc
          description:
            The standard Zvbc extension for vectored carryless multiplication
            instructions, as ratified in commit 56ed795 ("Update
            riscv-crypto-spec-vector.adoc") of riscv-crypto.

        - const: zve32f
          description:
            The standard Zve32f extension for embedded processors, as ratified
            in commit 6f702a2 ("Vector extensions are now ratified") of
            riscv-v-spec.

        - const: zve32x
          description:
            The standard Zve32x extension for embedded processors, as ratified
            in commit 6f702a2 ("Vector extensions are now ratified") of
            riscv-v-spec.

        - const: zve64d
          description:
            The standard Zve64d extension for embedded processors, as ratified
            in commit 6f702a2 ("Vector extensions are now ratified") of
            riscv-v-spec.

        - const: zve64f
          description:
            The standard Zve64f extension for embedded processors, as ratified
            in commit 6f702a2 ("Vector extensions are now ratified") of
            riscv-v-spec.

        - const: zve64x
          description:
            The standard Zve64x extension for embedded processors, as ratified
            in commit 6f702a2 ("Vector extensions are now ratified") of
            riscv-v-spec.

        - const: zvfbfmin
          description:
            The standard Zvfbfmin extension for minimal support for vectored
            16-bit half-precision brain floating-point instructions, as ratified
            in commit 4dc23d62 ("Added Chapter title to BF16") of riscv-isa-manual.

        - const: zvfbfwma
          description:
            The standard Zvfbfwma extension for vectored half-precision brain
            floating-point widening multiply-accumulate instructions, as ratified
            in commit 4dc23d62 ("Added Chapter title to BF16") of riscv-isa-manual.

        - const: zvfh
          description:
            The standard Zvfh extension for vectored half-precision
            floating-point instructions, as ratified in commit e2ccd05
            ("Remove draft warnings from Zvfh[min]") of riscv-v-spec.

        - const: zvfhmin
          description:
            The standard Zvfhmin extension for vectored minimal half-precision
            floating-point instructions, as ratified in commit e2ccd05
            ("Remove draft warnings from Zvfh[min]") of riscv-v-spec.

        - const: zvkb
          description:
            The standard Zvkb extension for vector cryptography bit-manipulation
            instructions, as ratified in commit 56ed795 ("Update
            riscv-crypto-spec-vector.adoc") of riscv-crypto.

        - const: zvkg
          description:
            The standard Zvkg extension for vector GCM/GMAC instructions, as
            ratified in commit 56ed795 ("Update riscv-crypto-spec-vector.adoc")
            of riscv-crypto.

        - const: zvkn
          description:
            The standard Zvkn extension for NIST algorithm suite instructions, as
            ratified in commit 56ed795 ("Update riscv-crypto-spec-vector.adoc")
            of riscv-crypto.

        - const: zvknc
          description:
            The standard Zvknc extension for NIST algorithm suite with carryless
            multiply instructions, as ratified in commit 56ed795 ("Update
            riscv-crypto-spec-vector.adoc") of riscv-crypto.

        - const: zvkned
          description:
            The standard Zvkned extension for Vector AES block cipher
            instructions, as ratified in commit 56ed795 ("Update
            riscv-crypto-spec-vector.adoc") of riscv-crypto.

        - const: zvkng
          description:
            The standard Zvkng extension for NIST algorithm suite with GCM
            instructions, as ratified in commit 56ed795 ("Update
            riscv-crypto-spec-vector.adoc") of riscv-crypto.

        - const: zvknha
          description: |
            The standard Zvknha extension for NIST suite: vector SHA-2 secure,
            hash (SHA-256 only) instructions, as ratified in commit
            56ed795 ("Update riscv-crypto-spec-vector.adoc") of riscv-crypto.

        - const: zvknhb
          description: |
            The standard Zvknhb extension for NIST suite: vector SHA-2 secure,
            hash (SHA-256 and SHA-512) instructions, as ratified in commit
            56ed795 ("Update riscv-crypto-spec-vector.adoc") of riscv-crypto.

        - const: zvks
          description:
            The standard Zvks extension for ShangMi algorithm suite
            instructions, as ratified in commit 56ed795 ("Update
            riscv-crypto-spec-vector.adoc") of riscv-crypto.

        - const: zvksc
          description:
            The standard Zvksc extension for ShangMi algorithm suite with
            carryless multiplication instructions, as ratified in commit 56ed795
            ("Update riscv-crypto-spec-vector.adoc") of riscv-crypto.

        - const: zvksed
          description: |
            The standard Zvksed extension for ShangMi suite: SM4 block cipher
            instructions, as ratified in commit 56ed795 ("Update
            riscv-crypto-spec-vector.adoc") of riscv-crypto.

        - const: zvksh
          description: |
            The standard Zvksh extension for ShangMi suite: SM3 secure hash
            instructions, as ratified in commit 56ed795 ("Update
            riscv-crypto-spec-vector.adoc") of riscv-crypto.

        - const: zvksg
          description:
            The standard Zvksg extension for ShangMi algorithm suite with GCM
            instructions, as ratified in commit 56ed795 ("Update
            riscv-crypto-spec-vector.adoc") of riscv-crypto.

        - const: zvkt
          description:
            The standard Zvkt extension for vector data-independent execution
            latency, as ratified in commit 56ed795 ("Update
            riscv-crypto-spec-vector.adoc") of riscv-crypto.

        # vendor extensions, each extension sorted alphanumerically under the
        # vendor they belong to. Vendors are sorted alphanumerically as well.

        # Andes
        - const: xandespmu
          description:
            The Andes Technology performance monitor extension for counter overflow
            and privilege mode filtering. For more details, see Counter Related
            Registers in the AX45MP datasheet.
            https://www.andestech.com/wp-content/uploads/AX45MP-1C-Rev.-5.0.0-Datasheet.pdf

        # SiFive
        - const: xsfvqmaccdod
          description:
            SiFive Int8 Matrix Multiplication Extensions Specification.
            See more details in
            https://www.sifive.com/document-file/sifive-int8-matrix-multiplication-extensions-specification

        - const: xsfvqmaccqoq
          description:
            SiFive Int8 Matrix Multiplication Extensions Specification.
            See more details in
            https://www.sifive.com/document-file/sifive-int8-matrix-multiplication-extensions-specification

        - const: xsfvfnrclipxfqf
          description:
            SiFive FP32-to-int8 Ranged Clip Instructions Extensions Specification.
            See more details in
            https://www.sifive.com/document-file/fp32-to-int8-ranged-clip-instructions

        - const: xsfvfwmaccqqq
          description:
            SiFive Matrix Multiply Accumulate Instruction Extensions Specification.
            See more details in
            https://www.sifive.com/document-file/matrix-multiply-accumulate-instruction

        # T-HEAD
        - const: xtheadvector
          description:
            The T-HEAD specific 0.7.1 vector implementation as written in
            https://github.com/T-head-Semi/thead-extension-spec/blob/95358cb2cca9489361c61d335e03d3134b14133f/xtheadvector.adoc.

    allOf:
      - if:
          contains:
            const: d
        then:
          contains:
            const: f
      # Zcb depends on Zca
      - if:
          contains:
            const: zcb
        then:
          contains:
            const: zca
      # Zcd depends on Zca and D
      - if:
          contains:
            const: zcd
        then:
          allOf:
            - contains:
                const: zca
            - contains:
                const: d
      # Zcf depends on Zca and F
      - if:
          contains:
            const: zcf
        then:
          allOf:
            - contains:
                const: zca
            - contains:
                const: f
      # Zcmop depends on Zca
      - if:
          contains:
            const: zcmop
        then:
          contains:
            const: zca
      # Zfbfmin depends on F
      - if:
          contains:
            const: zfbfmin
        then:
          contains:
            const: f
      # Zvfbfmin depends on V or Zve32f
      - if:
          contains:
            const: zvfbfmin
        then:
          oneOf:
            - contains:
                const: v
            - contains:
                const: zve32f
      # Zvfbfwma depends on Zfbfmin and Zvfbfmin
      - if:
          contains:
            const: zvfbfwma
        then:
          allOf:
            - contains:
                const: zfbfmin
            - contains:
                const: zvfbfmin
      # Zacas depends on Zaamo
      - if:
          contains:
            const: zacas
        then:
          contains:
            const: zaamo

      - if:
          contains:
            const: zve32x
        then:
          contains:
            const: zicsr

      - if:
          contains:
            const: zve32f
        then:
          allOf:
            - contains:
                const: f
            - contains:
                const: zve32x

      - if:
          contains:
            const: zve64x
        then:
          contains:
            const: zve32x

      - if:
          contains:
            const: zve64f
        then:
          allOf:
            - contains:
                const: f
            - contains:
                const: zve32f
            - contains:
                const: zve64x

      - if:
          contains:
            const: zve64d
        then:
          allOf:
            - contains:
                const: d
            - contains:
                const: zve64f

      - if:
          contains:
            anyOf:
              - const: zvbc
              - const: zvkn
              - const: zvknc
              - const: zvkng
              - const: zvknhb
              - const: zvksc
        then:
          contains:
            anyOf:
              - const: v
              - const: zve64x

      - if:
          contains:
            anyOf:
              - const: zvbb
              - const: zvkb
              - const: zvkg
              - const: zvkned
              - const: zvknha
              - const: zvksed
              - const: zvksh
              - const: zvks
              - const: zvkt
        then:
          contains:
            anyOf:
              - const: v
              - const: zve32x

allOf:
  # Zcf extension does not exist on rv64
  - if:
      properties:
        riscv,isa-extensions:
          contains:
            const: zcf
        riscv,isa-base:
          contains:
            const: rv64i
    then:
      properties:
        riscv,isa-extensions:
          not:
            contains:
              const: zcf

additionalProperties: true
...
