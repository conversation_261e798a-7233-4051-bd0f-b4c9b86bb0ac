# SPDX-License-Identifier: (GPL-2.0 OR MIT)
%YAML 1.2
---
$id: http://devicetree.org/schemas/riscv/cpus.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: RISC-V CPUs

maintainers:
  - <PERSON> <<EMAIL>>
  - <PERSON> <<EMAIL>>
  - <PERSON> <<EMAIL>>

description: |
  This document uses some terminology common to the RISC-V community
  that is not widely used, the definitions of which are listed here:

  hart: A hardware execution context, which contains all the state
  mandated by the RISC-V ISA: a PC and some registers.  This
  terminology is designed to disambiguate software's view of execution
  contexts from any particular microarchitectural implementation
  strategy.  For example, an Intel laptop containing one socket with
  two cores, each of which has two hyperthreads, could be described as
  having four harts.

allOf:
  - $ref: /schemas/cpu.yaml#
  - $ref: extensions.yaml
  - if:
      not:
        properties:
          compatible:
            contains:
              enum:
                - thead,c906
                - thead,c910
                - thead,c920
    then:
      properties:
        thead,vlenb: false

properties:
  compatible:
    oneOf:
      - items:
          - enum:
              - amd,mbv32
              - and<PERSON>ch,ax45mp
              - canaan,k210
              - sifive,bullet0
              - sifive,e5
              - sifive,e7
              - sifive,e71
              - sifive,rocket0
              - sifive,s7
              - sifive,u5
              - sifive,u54
              - sifive,u7
              - sifive,u74
              - sifive,u74-mc
              - spacemit,x60
              - thead,c906
              - thead,c908
              - thead,c910
              - thead,c920
          - const: riscv
      - items:
          - enum:
              - sifive,e51
              - sifive,u54-mc
          - const: sifive,rocket0
          - const: riscv
      - const: riscv    # Simulator only
    description:
      Identifies that the hart uses the RISC-V instruction set
      and identifies the type of the hart.

  mmu-type:
    description:
      Identifies the largest MMU address translation mode supported by
      this hart.  These values originate from the RISC-V Privileged
      Specification document, available from
      https://riscv.org/specifications/
    $ref: /schemas/types.yaml#/definitions/string
    enum:
      - riscv,sv32
      - riscv,sv39
      - riscv,sv48
      - riscv,sv57
      - riscv,none

  reg:
    description:
      The hart ID of this CPU node.

  riscv,cbom-block-size:
    $ref: /schemas/types.yaml#/definitions/uint32
    description:
      The blocksize in bytes for the Zicbom cache operations.

  riscv,cbop-block-size:
    $ref: /schemas/types.yaml#/definitions/uint32
    description:
      The blocksize in bytes for the Zicbop cache operations.

  riscv,cboz-block-size:
    $ref: /schemas/types.yaml#/definitions/uint32
    description:
      The blocksize in bytes for the Zicboz cache operations.

  thead,vlenb:
    $ref: /schemas/types.yaml#/definitions/uint32
    description:
      VLEN/8, the vector register length in bytes. This property is required on
      thead systems where the vector register length is not identical on all harts, or
      the vlenb CSR is not available.

  # RISC-V has multiple properties for cache op block sizes as the sizes
  # differ between individual CBO extensions
  cache-op-block-size: false
  # RISC-V requires 'timebase-frequency' in /cpus, so disallow it here
  timebase-frequency: false

  interrupt-controller:
    type: object
    $ref: /schemas/interrupt-controller/riscv,cpu-intc.yaml#

  cpu-idle-states:
    $ref: /schemas/types.yaml#/definitions/phandle-array
    items:
      maxItems: 1
    description: |
      List of phandles to idle state nodes supported
      by this hart (see ./idle-states.yaml).

  capacity-dmips-mhz:
    description:
      u32 value representing CPU capacity (see ../cpu/cpu-capacity.txt) in
      DMIPS/MHz, relative to highest capacity-dmips-mhz
      in the system.

anyOf:
  - required:
      - riscv,isa
  - required:
      - riscv,isa-base

dependencies:
  riscv,isa-base: [ "riscv,isa-extensions" ]
  riscv,isa-extensions: [ "riscv,isa-base" ]

required:
  - interrupt-controller

unevaluatedProperties: false

examples:
  - |
    // Example 1: SiFive Freedom U540G Development Kit
    cpus {
        #address-cells = <1>;
        #size-cells = <0>;
        timebase-frequency = <1000000>;
        cpu@0 {
                clock-frequency = <0>;
                compatible = "sifive,rocket0", "riscv";
                device_type = "cpu";
                i-cache-block-size = <64>;
                i-cache-sets = <128>;
                i-cache-size = <16384>;
                reg = <0>;
                riscv,isa-base = "rv64i";
                riscv,isa-extensions = "i", "m", "a", "c";

                cpu_intc0: interrupt-controller {
                        #interrupt-cells = <1>;
                        compatible = "riscv,cpu-intc";
                        interrupt-controller;
                };
        };
        cpu@1 {
                clock-frequency = <0>;
                compatible = "sifive,rocket0", "riscv";
                d-cache-block-size = <64>;
                d-cache-sets = <64>;
                d-cache-size = <32768>;
                d-tlb-sets = <1>;
                d-tlb-size = <32>;
                device_type = "cpu";
                i-cache-block-size = <64>;
                i-cache-sets = <64>;
                i-cache-size = <32768>;
                i-tlb-sets = <1>;
                i-tlb-size = <32>;
                mmu-type = "riscv,sv39";
                reg = <1>;
                tlb-split;
                riscv,isa-base = "rv64i";
                riscv,isa-extensions = "i", "m", "a", "f", "d", "c";

                cpu_intc1: interrupt-controller {
                        #interrupt-cells = <1>;
                        compatible = "riscv,cpu-intc";
                        interrupt-controller;
                };
        };
    };

  - |
    // Example 2: Spike ISA Simulator with 1 Hart
    cpus {
        #address-cells = <1>;
        #size-cells = <0>;
        cpu@0 {
                device_type = "cpu";
                reg = <0>;
                compatible = "riscv";
                mmu-type = "riscv,sv48";
                riscv,isa-base = "rv64i";
                riscv,isa-extensions = "i", "m", "a", "f", "d", "c";

                interrupt-controller {
                        #interrupt-cells = <1>;
                        interrupt-controller;
                        compatible = "riscv,cpu-intc";
                };
        };
    };
...
