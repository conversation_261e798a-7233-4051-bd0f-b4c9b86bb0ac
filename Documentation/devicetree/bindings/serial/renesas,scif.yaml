# SPDX-License-Identifier: (GPL-2.0-only OR BSD-2-Clause)
%YAML 1.2
---
$id: http://devicetree.org/schemas/serial/renesas,scif.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: Renesas Serial Communication Interface with FIFO (SCIF)

maintainers:
  - <PERSON><PERSON> <<EMAIL>>

properties:
  compatible:
    oneOf:
      - items:
          - enum:
              - renesas,scif-r7s72100     # RZ/A1H
          - const: renesas,scif           # generic SCIF compatible UART

      - items:
          - enum:
              - renesas,scif-r7s9210      # RZ/A2

      - items:
          - enum:
              - renesas,scif-r8a7778      # R-Car M1
              - renesas,scif-r8a7779      # R-Car H1
          - const: renesas,rcar-gen1-scif # R-Car Gen1
          - const: renesas,scif           # generic SCIF compatible UART

      - items:
          - enum:
              - renesas,scif-r8a7742      # RZ/G1H
              - renesas,scif-r8a7743      # RZ/G1M
              - renesas,scif-r8a7744      # RZ/G1N
              - renesas,scif-r8a7745      # RZ/G1E
              - renesas,scif-r8a77470     # RZ/G1C
              - renesas,scif-r8a7790      # R-Car H2
              - renesas,scif-r8a7791      # R-Car M2-W
              - renesas,scif-r8a7792      # R-Car V2H
              - renesas,scif-r8a7793      # R-Car M2-N
              - renesas,scif-r8a7794      # R-Car E2
          - const: renesas,rcar-gen2-scif # R-Car Gen2 and RZ/G1
          - const: renesas,scif           # generic SCIF compatible UART

      - items:
          - enum:
              - renesas,scif-r8a774a1     # RZ/G2M
              - renesas,scif-r8a774a3     # RZ/G2M v3.0
              - renesas,scif-r8a774b1     # RZ/G2N
              - renesas,scif-r8a774c0     # RZ/G2E
              - renesas,scif-r8a774e1     # RZ/G2H
              - renesas,scif-r8a7795      # R-Car H3
              - renesas,scif-r8a7796      # R-Car M3-W
              - renesas,scif-r8a77961     # R-Car M3-W+
              - renesas,scif-r8a77965     # R-Car M3-N
              - renesas,scif-r8a77970     # R-Car V3M
              - renesas,scif-r8a77980     # R-Car V3H
              - renesas,scif-r8a77990     # R-Car E3
              - renesas,scif-r8a77995     # R-Car D3
          - const: renesas,rcar-gen3-scif # R-Car Gen3 and RZ/G2
          - const: renesas,scif           # generic SCIF compatible UART

      - items:
          - enum:
              - renesas,scif-r8a779a0     # R-Car V3U
              - renesas,scif-r8a779f0     # R-Car S4-8
              - renesas,scif-r8a779g0     # R-Car V4H
              - renesas,scif-r8a779h0     # R-Car V4M
          - const: renesas,rcar-gen4-scif # R-Car Gen4
          - const: renesas,scif           # generic SCIF compatible UART

      - items:
          - enum:
              - renesas,scif-r9a07g044      # RZ/G2{L,LC}

      - items:
          - enum:
              - renesas,scif-r9a07g043      # RZ/G2UL and RZ/Five
              - renesas,scif-r9a07g054      # RZ/V2L
              - renesas,scif-r9a08g045      # RZ/G3S
          - const: renesas,scif-r9a07g044   # RZ/G2{L,LC} fallback

      - const: renesas,scif-r9a09g057       # RZ/V2H(P)

      - items:
          - enum:
              - renesas,scif-r9a09g047      # RZ/G3E
          - const: renesas,scif-r9a09g057   # RZ/V2H fallback

  reg:
    maxItems: 1

  interrupts:
    oneOf:
      - items:
          - description: A combined interrupt
      - items:
          - description: Error interrupt
          - description: Receive buffer full interrupt
          - description: Transmit buffer empty interrupt
          - description: Break interrupt
          - description: Data Ready interrupt
          - description: Transmit End interrupt
          - description: Transmit End/Data Ready interrupt
          - description: Receive buffer full interrupt (EDGE trigger)
          - description: Transmit buffer empty interrupt (EDGE trigger)
        minItems: 4

  interrupt-names:
    minItems: 4
    items:
      - const: eri
      - const: rxi
      - const: txi
      - const: bri
      - const: dri
      - const: tei
      - const: tei-dri
      - const: rxi-edge
      - const: txi-edge

  clocks:
    minItems: 1
    maxItems: 4

  clock-names:
    minItems: 1
    maxItems: 4
    items:
      enum:
        - fck # UART functional clock
        - sck # optional external clock input
        - brg_int # optional internal clock source for BRG frequency divider
        - scif_clk # optional external clock source for BRG frequency divider

  power-domains:
    maxItems: 1

  resets:
    maxItems: 1

  dmas:
    minItems: 2
    maxItems: 4
    description:
      Must contain a list of pairs of references to DMA specifiers, one for
      transmission, and one for reception.

  dma-names:
    minItems: 2
    maxItems: 4
    items:
      enum:
        - tx
        - rx

required:
  - compatible
  - reg
  - interrupts
  - clocks
  - clock-names
  - power-domains

allOf:
  - $ref: serial.yaml#

  - if:
      properties:
        compatible:
          contains:
            enum:
              - renesas,rcar-gen2-scif
              - renesas,rcar-gen3-scif
              - renesas,rcar-gen4-scif
              - renesas,scif-r9a07g044
              - renesas,scif-r9a09g057
    then:
      required:
        - resets

  - if:
      properties:
        compatible:
          contains:
            enum:
              - renesas,rcar-gen1-scif
              - renesas,rcar-gen2-scif
              - renesas,rcar-gen3-scif
              - renesas,rcar-gen4-scif
    then:
      properties:
        interrupts:
          maxItems: 1

        interrupt-names: false
    else:
      required:
        - interrupt-names

  - if:
      properties:
        compatible:
          contains:
            enum:
              - renesas,scif-r7s72100
    then:
      properties:
        interrupts:
          minItems: 4
          maxItems: 4

        interrupt-names:
          maxItems: 4

  - if:
      properties:
        compatible:
          contains:
            enum:
              - renesas,scif-r7s9210
              - renesas,scif-r9a07g044
    then:
      properties:
        interrupts:
          minItems: 6
          maxItems: 6

        interrupt-names:
          minItems: 6
          maxItems: 6

  - if:
      properties:
        compatible:
          contains:
            const: renesas,scif-r9a09g057
    then:
      properties:
        clocks:
          maxItems: 1

        clock-names:
          maxItems: 1

        interrupts:
          minItems: 9

        interrupt-names:
          minItems: 9

unevaluatedProperties: false

examples:
  - |
    #include <dt-bindings/clock/r8a7791-cpg-mssr.h>
    #include <dt-bindings/interrupt-controller/arm-gic.h>
    #include <dt-bindings/power/r8a7791-sysc.h>
    aliases {
        serial0 = &scif0;
    };

    scif0: serial@e6e60000 {
        compatible = "renesas,scif-r8a7791", "renesas,rcar-gen2-scif",
                     "renesas,scif";
        reg = <0xe6e60000 64>;
        interrupts = <GIC_SPI 152 IRQ_TYPE_LEVEL_HIGH>;
        clocks = <&cpg CPG_MOD 721>, <&cpg CPG_CORE R8A7791_CLK_ZS>,
                 <&scif_clk>;
        clock-names = "fck", "brg_int", "scif_clk";
        dmas = <&dmac0 0x29>, <&dmac0 0x2a>, <&dmac1 0x29>, <&dmac1 0x2a>;
        dma-names = "tx", "rx", "tx", "rx";
        power-domains = <&sysc R8A7791_PD_ALWAYS_ON>;
        resets = <&cpg 721>;
    };
