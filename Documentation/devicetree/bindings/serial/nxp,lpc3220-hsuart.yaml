# SPDX-License-Identifier: (GPL-2.0-only OR BSD-2-Clause)
%YAML 1.2
---
$id: http://devicetree.org/schemas/serial/nxp,lpc3220-hsuart.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: NXP LPC32xx SoC High Speed UART

maintainers:
  - <PERSON> <<EMAIL>>
  - <PERSON><PERSON><PERSON> <<EMAIL>>

allOf:
  - $ref: /schemas/serial/serial.yaml#

properties:
  compatible:
    const: nxp,lpc3220-hsuart

  reg:
    maxItems: 1

  interrupts:
    maxItems: 1

required:
  - compatible
  - reg
  - interrupts

unevaluatedProperties: false

examples:
  - |
    serial@40014000 {
        compatible = "nxp,lpc3220-hsuart";
        reg = <0x40014000 0x1000>;
        interrupts = <26 0>;
    };
