# Copyright 2020 <PERSON><PERSON><PERSON> <<EMAIL>>
%YAML 1.2
---
$id: http://devicetree.org/schemas/serial/8250.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: UART (Universal Asynchronous Receiver/Transmitter)

maintainers:
  - <EMAIL>

allOf:
  - $ref: serial.yaml#
  - $ref: /schemas/memory-controllers/mc-peripheral-props.yaml#
  - if:
      anyOf:
        - required:
            - aspeed,lpc-io-reg
        - required:
            - aspeed,lpc-interrupts
        - required:
            - aspeed,sirq-polarity-sense
    then:
      properties:
        compatible:
          const: aspeed,ast2500-vuart
  - if:
      properties:
        compatible:
          const: mrvl,mmp-uart
    then:
      properties:
        reg-shift:
          const: 2
      required:
        - reg-shift
  - if:
      not:
        properties:
          compatible:
            items:
              - enum:
                  - ns8250
                  - ns16450
                  - ns16550
                  - ns16550a
    then:
      oneOf:
        - required: [ clock-frequency ]
        - required: [ clocks ]

properties:
  compatible:
    oneOf:
      - const: ns8250
      - const: ns16450
      - const: ns16550
      - const: ns16550a
      - const: ns16850
      - const: aspeed,ast2400-vuart
      - const: aspeed,ast2500-vuart
      - const: intel,xscale-uart
      - const: mrvl,pxa-uart
      - const: nuvoton,wpcm450-uart
      - const: nuvoton,npcm750-uart
      - const: nvidia,tegra20-uart
      - const: nxp,lpc3220-uart
      - items:
          - enum:
              - exar,xr16l2552
              - exar,xr16l2551
              - exar,xr16l2550
          - const: ns8250
      - items:
          - enum:
              - altr,16550-FIFO32
              - altr,16550-FIFO64
              - altr,16550-FIFO128
              - fsl,16550-FIFO64
              - andestech,uart16550
              - nxp,lpc1850-uart
              - opencores,uart16550-rtlsvn105
              - ti,da830-uart
          - const: ns16550a
      - items:
          - enum:
              - ns16750
              - fsl,ns16550
              - cavium,octeon-3860-uart
              - xlnx,xps-uart16550-2.00.b
              - ralink,rt2880-uart
          - enum:
              - ns16550 # Deprecated, unless the FIFO really is broken
              - ns16550a
      - items:
          - enum:
              - nuvoton,npcm845-uart
          - const: nuvoton,npcm750-uart
      - items:
          - enum:
              - ralink,mt7620a-uart
              - ralink,rt3052-uart
              - ralink,rt3883-uart
          - const: ralink,rt2880-uart
          - enum:
              - ns16550 # Deprecated, unless the FIFO really is broken
              - ns16550a
      - items:
          - enum:
              - mediatek,mt7622-btif
              - mediatek,mt7623-btif
          - const: mediatek,mtk-btif
      - items:
          - enum:
              - mrvl,mmp-uart
              - spacemit,k1-uart
          - const: intel,xscale-uart
      - items:
          - enum:
              - nvidia,tegra30-uart
              - nvidia,tegra114-uart
              - nvidia,tegra124-uart
              - nvidia,tegra210-uart
              - nvidia,tegra186-uart
              - nvidia,tegra194-uart
              - nvidia,tegra234-uart
          - const: nvidia,tegra20-uart

  reg:
    maxItems: 1

  interrupts:
    maxItems: 1

  clock-frequency: true

  clocks:
    minItems: 1
    items:
      - description: The core function clock
      - description: An optional bus clock

  clock-names:
    minItems: 1
    items:
      - const: core
      - const: bus

  resets:
    maxItems: 1

  current-speed:
    $ref: /schemas/types.yaml#/definitions/uint32
    description: The current active speed of the UART.

  reg-offset:
    $ref: /schemas/types.yaml#/definitions/uint32
    description: |
      Offset to apply to the mapbase from the start of the registers.

  reg-shift:
    description: Quantity to shift the register offsets by.

  reg-io-width:
    description: |
      The size (in bytes) of the IO accesses that should be performed on the
      device. There are some systems that require 32-bit accesses to the
      UART (e.g. TI davinci).

  used-by-rtas:
    type: boolean
    description: |
      Set to indicate that the port is in use by the OpenFirmware RTAS and
      should not be registered.

  no-loopback-test:
    type: boolean
    description: |
      Set to indicate that the port does not implement loopback test mode.

  fifo-size:
    $ref: /schemas/types.yaml#/definitions/uint32
    description: The fifo size of the UART.

  auto-flow-control:
    type: boolean
    description: |
      One way to enable automatic flow control support. The driver is
      allowed to detect support for the capability even without this
      property.

  tx-threshold:
    description: |
      Specify the TX FIFO low water indication for parts with programmable
      TX FIFO thresholds.

  overrun-throttle-ms:
    description: |
      How long to pause uart rx when input overrun is encountered.

  rts-gpios: true
  cts-gpios: true
  dtr-gpios: true
  dsr-gpios: true
  rng-gpios: true
  dcd-gpios: true

  aspeed,sirq-polarity-sense:
    $ref: /schemas/types.yaml#/definitions/phandle-array
    description: |
      Phandle to aspeed,ast2500-scu compatible syscon alongside register
      offset and bit number to identify how the SIRQ polarity should be
      configured. One possible data source is the LPC/eSPI mode bit. Only
      applicable to aspeed,ast2500-vuart.
    deprecated: true

  aspeed,lpc-io-reg:
    $ref: /schemas/types.yaml#/definitions/uint32-array
    maxItems: 1
    description: |
      The VUART LPC address.  Only applicable to aspeed,ast2500-vuart.

  aspeed,lpc-interrupts:
    $ref: /schemas/types.yaml#/definitions/uint32-array
    minItems: 2
    maxItems: 2
    description: |
      A 2-cell property describing the VUART SIRQ number and SIRQ
      polarity (IRQ_TYPE_LEVEL_LOW or IRQ_TYPE_LEVEL_HIGH).  Only
      applicable to aspeed,ast2500-vuart.

required:
  - reg
  - interrupts

if:
  properties:
    compatible:
      contains:
        const: spacemit,k1-uart
then:
  required: [clock-names]
  properties:
    clocks:
      minItems: 2
    clock-names:
      minItems: 2
else:
  properties:
    clocks:
      maxItems: 1
    clock-names:
      maxItems: 1

unevaluatedProperties: false

examples:
  - |
    serial@80230000 {
        compatible = "ns8250";
        reg = <0x80230000 0x100>;
        interrupts = <10>;
        reg-shift = <2>;
        clock-frequency = <48000000>;
    };
  - |
    #include <dt-bindings/gpio/gpio.h>
    serial@49042000 {
        compatible = "andestech,uart16550", "ns16550a";
        reg = <0x49042000 0x400>;
        interrupts = <80>;
        clock-frequency = <48000000>;
        cts-gpios = <&gpio3 5 GPIO_ACTIVE_LOW>;
        rts-gpios = <&gpio3 6 GPIO_ACTIVE_LOW>;
        dtr-gpios = <&gpio1 12 GPIO_ACTIVE_LOW>;
        dsr-gpios = <&gpio1 13 GPIO_ACTIVE_LOW>;
        dcd-gpios = <&gpio1 14 GPIO_ACTIVE_LOW>;
        rng-gpios = <&gpio1 15 GPIO_ACTIVE_LOW>;
    };
  - |
    #include <dt-bindings/clock/aspeed-clock.h>
    #include <dt-bindings/interrupt-controller/irq.h>
    serial@1e787000 {
        compatible = "aspeed,ast2500-vuart";
        reg = <0x1e787000 0x40>;
        reg-shift = <2>;
        interrupts = <8>;
        clocks = <&syscon ASPEED_CLK_APB>;
        no-loopback-test;
        aspeed,lpc-io-reg = <0x3f8>;
        aspeed,lpc-interrupts = <4 IRQ_TYPE_LEVEL_LOW>;
    };

...
