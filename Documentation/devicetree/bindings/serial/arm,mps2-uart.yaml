# SPDX-License-Identifier: (GPL-2.0-only OR BSD-2-Clause)
%YAML 1.2
---
$id: http://devicetree.org/schemas/serial/arm,mps2-uart.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: Arm MPS2 UART

maintainers:
  - <PERSON> <<EMAIL>>

allOf:
  - $ref: /schemas/serial/serial.yaml#

properties:
  compatible:
    const: arm,mps2-uart

  reg:
    maxItems: 1

  interrupts:
    items:
      - description: RX interrupt
      - description: TX interrupt
      - description: Overrun interrupt

  clocks:
    maxItems: 1

required:
  - compatible
  - reg
  - interrupts
  - clocks

unevaluatedProperties: false

examples:
  - |
    serial@40004000 {
        compatible = "arm,mps2-uart";
        reg = <0x40004000 0x1000>;
        interrupts = <0>, <1>, <12>;
        clocks = <&sysclk>;
    };
