# SPDX-License-Identifier: (GPL-2.0-only OR BSD-2-Clause)
%YAML 1.2
---
$id: http://devicetree.org/schemas/serial/8250_omap.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: 8250 compliant UARTs on TI's OMAP2+ and K3 SoCs

maintainers:
  - <PERSON><PERSON><PERSON> <<EMAIL>>

allOf:
  - $ref: /schemas/serial/serial.yaml#
  - $ref: /schemas/serial/rs485.yaml#

properties:
  compatible:
    oneOf:
      - enum:
          - ti,am3352-uart
          - ti,am4372-uart
          - ti,am654-uart
          - ti,dra742-uart
          - ti,omap2-uart
          - ti,omap3-uart
          - ti,omap4-uart
      - items:
          - enum:
              - ti,am64-uart
              - ti,j721e-uart
          - const: ti,am654-uart

  ti,hwmods:
    description:
      Must be "uart<n>", n being the instance number (1-based)
      This property is applicable only on legacy platforms mainly omap2/3
      and ti81xx and should not be used on other platforms.
    $ref: /schemas/types.yaml#/definitions/string
    deprecated: true

  dmas:
    minItems: 1
    maxItems: 2

  dma-names:
    items:
      - const: tx
      - const: rx

  reg:
    maxItems: 1

  interrupts:
    minItems: 1
    maxItems: 2
    description:
      First entry is module IRQ required for normal IO operation.
      Second entry is optional and corresponds to system wakeup IRQ
      where supported.

  clocks:
    maxItems: 1

  clock-names:
    const: fclk

  rs485-rts-active-high: true
  power-domains: true
  clock-frequency: true
  current-speed: true
  overrun-throttle-ms: true
  wakeup-source: true

required:
  - compatible
  - reg
  - interrupts

unevaluatedProperties: false

if:
  properties:
    compatible:
      contains:
        enum:
          - ti,omap2-uart
          - ti,omap3-uart
          - ti,omap4-uart

then:
  properties:
    ti,hwmods:
      items:
        - pattern: "^uart([1-9])$"

else:
  properties:
    ti,hwmods: false

examples:
  - |
    serial@49042000 {
        compatible = "ti,omap3-uart";
        reg = <0x49042000 0x400>;
        interrupts = <80>;
        dmas = <&sdma 81 &sdma 82>;
        dma-names = "tx", "rx";
        ti,hwmods = "uart4";
        clock-frequency = <48000000>;
    };
