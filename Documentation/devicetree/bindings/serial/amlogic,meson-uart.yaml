# SPDX-License-Identifier: (GPL-2.0 OR BSD-2-Clause)
# Copyright 2019 Bay<PERSON>ibre, SAS
%YAML 1.2
---
$id: http://devicetree.org/schemas/serial/amlogic,meson-uart.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: Amlogic Meson SoC UART Serial Interface

maintainers:
  - <PERSON> <<EMAIL>>

description: |
  The Amlogic Meson SoC UART Serial Interface is present on a large range
  of SoCs, and can be present either in the "Always-On" power domain or the
  "Everything-Else" power domain.

  The particularity of the "Always-On" Serial Interface is that the hardware
  is active since power-on and does not need any clock gating and is usable
  as very early serial console.

allOf:
  - $ref: serial.yaml#

properties:
  compatible:
    oneOf:
      - description: Always-on power domain UART controller
        items:
          - enum:
              - amlogic,meson6-uart
              - amlogic,meson8-uart
              - amlogic,meson8b-uart
              - amlogic,meson-gx-uart
              - amlogic,meson-s4-uart
              - amlogic,meson-a1-uart
          - const: amlogic,meson-ao-uart
      - description: Always-on power domain UART controller on G12A SoCs
        items:
          - const: amlogic,meson-g12a-uart
          - const: amlogic,meson-gx-uart
          - const: amlogic,meson-ao-uart
      - description: Everything-Else power domain UART controller
        enum:
          - amlogic,meson6-uart
          - amlogic,meson8-uart
          - amlogic,meson8b-uart
          - amlogic,meson-gx-uart
          - amlogic,meson-s4-uart
          - amlogic,meson-a1-uart
      - description: Everything-Else power domain UART controller on G12A SoCs
        items:
          - const: amlogic,meson-g12a-uart
          - const: amlogic,meson-gx-uart
      - description: UART controller on S4 compatible SoCs
        items:
          - enum:
              - amlogic,a4-uart
              - amlogic,s6-uart
              - amlogic,s7-uart
              - amlogic,s7d-uart
              - amlogic,t7-uart
          - const: amlogic,meson-s4-uart

  reg:
    maxItems: 1

  interrupts:
    maxItems: 1

  clocks:
    items:
      - description: external xtal clock identifier
      - description: the bus core clock, either the clk81 clock or the gate clock
      - description: the source of the baudrate generator, can be either the xtal or the pclk

  clock-names:
    items:
      - const: xtal
      - const: pclk
      - const: baud

  fifo-size:
    description: The fifo size supported by the UART channel.
    $ref: /schemas/types.yaml#/definitions/uint32
    enum: [64, 128]

required:
  - compatible
  - reg
  - interrupts
  - clocks
  - clock-names

unevaluatedProperties: false

examples:
  - |
    serial@84c0 {
        compatible = "amlogic,meson-gx-uart";
        reg = <0x84c0 0x14>;
        interrupts = <26>;
        clocks = <&xtal>, <&pclk>, <&xtal>;
        clock-names = "xtal", "pclk", "baud";
    };
