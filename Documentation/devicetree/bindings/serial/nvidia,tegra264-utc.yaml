# SPDX-License-Identifier: (GPL-2.0-only OR BSD-2-Clause)
%YAML 1.2
---
$id: http://devicetree.org/schemas/serial/nvidia,tegra264-utc.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: NVIDIA Tegra UTC (UART Trace Controller) client

maintainers:
  - <PERSON><PERSON><PERSON> <<EMAIL>>
  - <PERSON><PERSON><PERSON> <<EMAIL>>
  - <PERSON> <<EMAIL>>

description:
  Represents a client interface of the Tegra UTC (UART Trace Controller). The
  Tegra UTC allows multiple clients within the Tegra SoC to share a physical
  UART interface. It supports up to 16 clients. Each client operates as an
  independent UART endpoint with a dedicated interrupt and 128-character TX/RX
  FIFOs.

  The Tegra UTC clients use 8-N-1 configuration and operates on a baudrate
  configured by the bootloader at the controller level.

allOf:
  - $ref: serial.yaml#

properties:
  compatible:
    const: nvidia,tegra264-utc

  reg:
    items:
      - description: TX region.
      - description: RX region.

  reg-names:
    items:
      - const: tx
      - const: rx

  interrupts:
    maxItems: 1

  tx-threshold:
    minimum: 1
    maximum: 128

  rx-threshold:
    minimum: 1
    maximum: 128

required:
  - compatible
  - reg
  - reg-names
  - interrupts
  - tx-threshold
  - rx-threshold

additionalProperties: false

examples:
  - |
    #include <dt-bindings/interrupt-controller/arm-gic.h>

    tegra_utc: serial@c4e0000 {
        compatible = "nvidia,tegra264-utc";
        reg = <0xc4e0000 0x8000>, <0xc4e8000 0x8000>;
        reg-names = "tx", "rx";
        interrupts = <GIC_SPI 514 IRQ_TYPE_LEVEL_HIGH>;
        tx-threshold = <4>;
        rx-threshold = <4>;
    };
