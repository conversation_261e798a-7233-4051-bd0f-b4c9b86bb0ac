# SPDX-License-Identifier: GPL-2.0
%YAML 1.2
---
$id: http://devicetree.org/schemas/serial/samsung_uart.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: Samsung S3C, S5P, Exynos, and S5L (Apple SoC) SoC UART Controller

maintainers:
  - <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> <<EMAIL>>
  - <PERSON> <<EMAIL>>

description: |+
  Each Samsung UART should have an alias correctly numbered in the "aliases"
  node, according to serialN format, where N is the port number (non-negative
  decimal integer) as specified by User's Manual of respective SoC.

properties:
  compatible:
    oneOf:
      - enum:
          - apple,s5l-uart
          - axis,artpec8-uart
          - google,gs101-uart
          - samsung,s3c6400-uart
          - samsung,s5pv210-uart
          - samsung,exynos4210-uart
          - samsung,exynos5433-uart
          - samsung,exynos850-uart
          - samsung,exynos8895-uart
      - items:
          - enum:
              - samsung,exynos7-uart
              - tesla,fsd-uart
          - const: samsung,exynos4210-uart
      - items:
          - enum:
              - samsung,exynos7885-uart
          - const: samsung,exynos5433-uart
      - items:
          - enum:
              - samsung,exynosautov9-uart
              - samsung,exynosautov920-uart
          - const: samsung,exynos850-uart
      - items:
          - enum:
              - samsung,exynos7870-uart
          - const: samsung,exynos8895-uart

  reg:
    maxItems: 1

  reg-io-width:
    description: |
      The size (in bytes) of the IO accesses that should be performed
      on the device.
    enum: [ 1, 4 ]

  clocks:
    minItems: 2
    maxItems: 5

  clock-names:
    minItems: 2
    maxItems: 5

  dmas:
    items:
      - description: DMA controller phandle and request line for RX
      - description: DMA controller phandle and request line for TX

  dma-names:
    items:
      - const: rx
      - const: tx

  interrupts:
    description: RX interrupt and optionally TX interrupt.
    minItems: 1
    maxItems: 2

  power-domains:
    maxItems: 1

  samsung,uart-fifosize:
    description: The fifo size supported by the UART channel.
    $ref: /schemas/types.yaml#/definitions/uint32
    enum: [16, 64, 256]

required:
  - compatible
  - clocks
  - clock-names
  - interrupts
  - reg

allOf:
  - $ref: serial.yaml#

  - if:
      properties:
        compatible:
          contains:
            enum:
              - samsung,s3c6400-uart
    then:
      properties:
        clocks:
          minItems: 3
          maxItems: 3

        clock-names:
          items:
            - const: uart
            - const: clk_uart_baud2
            - const: clk_uart_baud3

    else:
      properties:
        clock-names:
          minItems: 2
          items:
            - const: uart
            - const: clk_uart_baud0
            - const: clk_uart_baud1
            - const: clk_uart_baud2
            - const: clk_uart_baud3

  - if:
      properties:
        compatible:
          contains:
            enum:
              - samsung,s5pv210-uart
    then:
      properties:
        clocks:
          minItems: 3
          maxItems: 3

        clock-names:
          minItems: 3
          maxItems: 3

  - if:
      properties:
        compatible:
          contains:
            enum:
              - apple,s5l-uart
              - axis,artpec8-uart
              - samsung,exynos4210-uart
              - samsung,exynos5433-uart
    then:
      properties:
        clocks:
          maxItems: 2

        clock-names:
          maxItems: 2

  - if:
      properties:
        compatible:
          contains:
            enum:
              - google,gs101-uart
              - samsung,exynos8895-uart
    then:
      required:
        - samsung,uart-fifosize
      properties:
        clocks:
          maxItems: 2

        clock-names:
          maxItems: 2

  - if:
      properties:
        compatible:
          contains:
            enum:
              - google,gs101-uart
    then:
      properties:
        reg-io-width: false

unevaluatedProperties: false

examples:
  - |
    #include <dt-bindings/clock/samsung,s3c64xx-clock.h>

    uart0: serial@7f005000 {
        compatible = "samsung,s3c6400-uart";
        reg = <0x7f005000 0x100>;
        interrupt-parent = <&vic1>;
        interrupts = <5>;
        clock-names = "uart", "clk_uart_baud2",
                      "clk_uart_baud3";
        clocks = <&clocks PCLK_UART0>, <&clocks PCLK_UART0>,
                 <&clocks SCLK_UART>;
        samsung,uart-fifosize = <16>;
    };
  - |
    #include <dt-bindings/clock/google,gs101.h>
    #include <dt-bindings/interrupt-controller/arm-gic.h>
    #include <dt-bindings/interrupt-controller/irq.h>

    serial_0: serial@10a00000 {
      compatible = "google,gs101-uart";
      reg = <0x10a00000 0xc0>;
      clocks = <&cmu_peric0 CLK_GOUT_PERIC0_PERIC0_TOP1_PCLK_0>,
               <&cmu_peric0 CLK_GOUT_PERIC0_PERIC0_TOP1_IPCLK_0>;
      clock-names = "uart", "clk_uart_baud0";
      interrupts = <GIC_SPI 634 IRQ_TYPE_LEVEL_HIGH 0>;
      pinctrl-0 = <&uart0_bus>;
      pinctrl-names = "default";
      samsung,uart-fifosize = <256>;
    };
