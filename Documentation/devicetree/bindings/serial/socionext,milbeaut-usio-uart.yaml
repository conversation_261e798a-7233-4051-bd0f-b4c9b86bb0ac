# SPDX-License-Identifier: (GPL-2.0-only OR BSD-2-Clause)
%YAML 1.2
---
$id: http://devicetree.org/schemas/serial/socionext,milbeaut-usio-uart.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: Socionext Milbeaut UART controller

maintainers:
  - <PERSON><PERSON><PERSON> <<EMAIL>>

allOf:
  - $ref: /schemas/serial/serial.yaml#

properties:
  compatible:
    const: socionext,milbeaut-usio-uart

  reg:
    maxItems: 1

  interrupts:
    items:
      - description: RX interrupt specifier
      - description: TX interrupt specifier

  interrupt-names:
    items:
      - const: rx
      - const: tx

  clocks:
    maxItems: 1

  auto-flow-control:
    description: Enable automatic flow control.
    type: boolean

required:
  - compatible
  - reg
  - interrupts
  - interrupt-names

unevaluatedProperties: false

examples:
  - |
    serial@1e700010 {
        compatible = "socionext,milbeaut-usio-uart";
        reg = <0x1e700010 0x10>;
        interrupts = <0 141 0x4>, <0 149 0x4>;
        interrupt-names = "rx", "tx";
        clocks = <&clk 2>;
        auto-flow-control;
    };
