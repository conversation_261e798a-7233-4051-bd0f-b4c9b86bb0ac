# SPDX-License-Identifier: (GPL-2.0-only OR BSD-2-Clause)
%YAML 1.2
---
$id: http://devicetree.org/schemas/serial/renesas,rsci.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: Renesas RSCI Serial Communication Interface

maintainers:
  - <PERSON><PERSON> <<EMAIL>>
  - <PERSON><PERSON><PERSON> <<EMAIL>>

allOf:
  - $ref: serial.yaml#

properties:
  compatible:
    const: renesas,r9a09g077-rsci      # RZ/T2H

  reg:
    maxItems: 1

  interrupts:
    items:
      - description: Error interrupt
      - description: Receive buffer full interrupt
      - description: Transmit buffer empty interrupt
      - description: Transmit end interrupt

  interrupt-names:
    items:
      - const: eri
      - const: rxi
      - const: txi
      - const: tei

  clocks:
    maxItems: 1

  clock-names:
    const: fck # UART functional clock

  power-domains:
    maxItems: 1

  uart-has-rtscts: false

required:
  - compatible
  - reg
  - interrupts
  - clocks
  - clock-names
  - power-domains

unevaluatedProperties: false

examples:
  - |
    #include <dt-bindings/interrupt-controller/arm-gic.h>
    #include <dt-bindings/clock/renesas-cpg-mssr.h>

    aliases {
        serial0 = &sci0;
    };

    sci0: serial@80005000 {
        compatible = "renesas,r9a09g077-rsci";
        reg = <0x80005000 0x400>;
        interrupts = <GIC_SPI 590 IRQ_TYPE_LEVEL_HIGH>,
                     <GIC_SPI 591 IRQ_TYPE_EDGE_RISING>,
                     <GIC_SPI 592 IRQ_TYPE_EDGE_RISING>,
                     <GIC_SPI 593 IRQ_TYPE_LEVEL_HIGH>;
        interrupt-names = "eri", "rxi", "txi", "tei";
        clocks = <&cpg CPG_MOD 108>;
        clock-names = "fck";
        power-domains = <&cpg>;
    };
