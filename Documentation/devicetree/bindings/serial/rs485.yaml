# SPDX-License-Identifier: GPL-2.0
%YAML 1.2
---
$id: http://devicetree.org/schemas/serial/rs485.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: RS485 serial communications

description: The RTS signal is capable of automatically controlling line
  direction for the built-in half-duplex mode. The properties described
  hereafter shall be given to a half-duplex capable UART node.

maintainers:
  - <PERSON> <<EMAIL>>

properties:
  rs485-rts-delay:
    description: prop-encoded-array <a b>
    $ref: /schemas/types.yaml#/definitions/uint32-array
    items:
      - description: Delay between rts signal and beginning of data sent in
          milliseconds. It corresponds to the delay before sending data.
        default: 0
        maximum: 100
      - description: Delay between end of data sent and rts signal in milliseconds.
          It corresponds to the delay after sending data and actual release
          of the line.
        default: 0
        maximum: 100

  rs485-rts-active-high:
    description: drive RTS high when sending (this is the default).
    $ref: /schemas/types.yaml#/definitions/flag

  rs485-rts-active-low:
    description: drive RTS low when sending (default is high).
    $ref: /schemas/types.yaml#/definitions/flag

  rs485-rx-active-high:
    description: Polarity of receiver enable signal (when separate from RTS).
      True indicates active high (default is low).
    $ref: /schemas/types.yaml#/definitions/flag

  linux,rs485-enabled-at-boot-time:
    description: enables the rs485 feature at boot time. It can be disabled
      later with proper ioctl.
    $ref: /schemas/types.yaml#/definitions/flag

  rs485-rx-during-tx:
    description: enables the receiving of data even while sending data.
    $ref: /schemas/types.yaml#/definitions/flag

  rs485-term-gpios:
    description: GPIO pin to enable RS485 bus termination.
    maxItems: 1

  rs485-rx-during-tx-gpios:
    description: Output GPIO pin that sets the state of rs485-rx-during-tx. This
      signal can be used to control the RX part of an RS485 transceiver. Thereby
      the active state enables RX during TX.
    maxItems: 1

additionalProperties: true

...
