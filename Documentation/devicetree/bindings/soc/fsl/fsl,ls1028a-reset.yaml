# SPDX-License-Identifier: (GPL-2.0-only OR BSD-2-Clause)
%YAML 1.2
---
$id: http://devicetree.org/schemas/soc/fsl/fsl,ls1028a-reset.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: Freescale Layerscape Reset Registers Module

maintainers:
  - <PERSON> <<EMAIL>>

description:
  Reset Module includes chip reset, service processor control and Reset Control
  Word (RCW) status.

properties:
  $nodename:
    pattern: "^syscon@[0-9a-f]+$"

  compatible:
    items:
      - enum:
          - fsl,ls1028a-reset
      - const: syscon
      - const: simple-mfd

  reg:
    maxItems: 1

  little-endian: true

  reboot:
    $ref: /schemas/power/reset/syscon-reboot.yaml#
    unevaluatedProperties: false

required:
  - compatible
  - reg
  - reboot

additionalProperties: false

examples:
  - |
    syscon@1e60000 {
        compatible = "fsl,ls1028a-reset", "syscon", "simple-mfd";
        reg = <0x1e60000 0x10000>;
        little-endian;

        reboot {
            compatible = "syscon-reboot";
            offset = <0>;
            mask = <0x02>;
        };
    };

