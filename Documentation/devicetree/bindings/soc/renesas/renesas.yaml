# SPDX-License-Identifier: GPL-2.0
%YAML 1.2
---
$id: http://devicetree.org/schemas/soc/renesas/renesas.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: Renesas SH-Mobile, R-Mobile, and R-Car Platform

maintainers:
  - <PERSON><PERSON> <<EMAIL>>

properties:
  $nodename:
    const: '/'
  compatible:
    oneOf:
      - description: Emma Mobile EV2
        items:
          - enum:
              - renesas,kzm9d # Kyoto Microcomputer Co. KZM-A9-Dual
          - const: renesas,emev2

      - description: RZ/A1H (R7S72100)
        items:
          - enum:
              - renesas,genmai # Genmai (RTK772100BC00000BR)
              - renesas,gr-peach # GR-Peach (X28A-M01-E/F)
              - renesas,rskrza1 # RSKRZA1 (YR0K77210C000BE)
          - const: renesas,r7s72100

      - description: RZ/A2 (R7S9210)
        items:
          - enum:
              - renesas,rza2mevb # RZ/A2M Eval Board (RTK7921053S00000BE)
          - const: renesas,r7s9210

      - description: SH-Mobile AG5 (R8A73A00/SH73A0)
        items:
          - enum:
              - renesas,kzm9g # Kyoto Microcomputer Co. KZM-A9-GT
          - const: renesas,sh73a0

      - description: R-Mobile APE6 (R8A73A40)
        items:
          - enum:
              - renesas,ape6evm
          - const: renesas,r8a73a4

      - description: R-Mobile A1 (R8A77400)
        items:
          - enum:
              - renesas,armadillo800eva # Atmark Techno Armadillo-800 EVA
          - const: renesas,r8a7740

      - description: RZ/G1H (R8A77420)
        items:
          - enum:
              # iWave Systems RZ/G1H Qseven System On Module (iW-RainboW-G21M-Qseven)
              - iwave,g21m
          - const: renesas,r8a7742

      - items:
          - enum:
              # iWave Systems RZ/G1H Qseven Development Platform (iW-RainboW-G21D-Qseven)
              - iwave,g21d
          - const: iwave,g21m
          - const: renesas,r8a7742

      - description: RZ/G1M (R8A77430)
        items:
          - enum:
              # iWave Systems RZ/G1M Qseven Development Platform (iW-RainboW-G20D-Qseven)
              - iwave,g20d
          - const: iwave,g20m
          - const: renesas,r8a7743

      - items:
          - enum:
              # iWave Systems RZ/G1M Qseven System On Module (iW-RainboW-G20M-Qseven)
              - iwave,g20m
              - renesas,sk-rzg1m # SK-RZG1M (YR8A77430S000BE)
          - const: renesas,r8a7743

      - description: RZ/G1N (R8A77440)
        items:
          - enum:
              # iWave Systems RZ/G1N Qseven Development Platform (iW-RainboW-G20D-Qseven)
              - iwave,g20d
          - const: iwave,g20m
          - const: renesas,r8a7744

      - items:
          - enum:
              # iWave Systems RZ/G1N Qseven System On Module (iW-RainboW-G20M-Qseven)
              - iwave,g20m
          - const: renesas,r8a7744

      - description: RZ/G1E (R8A77450)
        items:
          - enum:
              - iwave,g22m # iWave Systems RZ/G1E SODIMM System On Module (iW-RainboW-G22M-SM)
              - renesas,sk-rzg1e # SK-RZG1E (YR8A77450S000BE)
          - const: renesas,r8a7745

      - description: iWave Systems RZ/G1E SODIMM SOM Development Platform (iW-RainboW-G22D)
        items:
          - const: iwave,g22d
          - const: iwave,g22m
          - const: renesas,r8a7745

      - description: RZ/G1C (R8A77470)
        items:
          - enum:
              - iwave,g23s # iWave Systems RZ/G1C Single Board Computer (iW-RainboW-G23S)
          - const: renesas,r8a77470

      - description: RZ/G2M (R8A774A1)
        items:
          - enum:
              - hoperun,hihope-rzg2m # HopeRun HiHope RZ/G2M platform
              - beacon,beacon-rzg2m # Beacon EmbeddedWorks RZ/G2M Kit
          - const: renesas,r8a774a1

      - items:
          - enum:
              - hoperun,hihope-rzg2-ex # HopeRun expansion board for HiHope RZ/G2 platforms
          - const: hoperun,hihope-rzg2m
          - const: renesas,r8a774a1

      - description: RZ/G2M v3.0 (R8A774A3)
        items:
          - enum:
              - hoperun,hihope-rzg2m # HopeRun HiHope RZ/G2M platform
          - const: renesas,r8a774a3

      - items:
          - enum:
              - hoperun,hihope-rzg2-ex # HopeRun expansion board for HiHope RZ/G2 platforms
          - const: hoperun,hihope-rzg2m
          - const: renesas,r8a774a3

      - description: RZ/G2N (R8A774B1)
        items:
          - enum:
              - beacon,beacon-rzg2n # Beacon EmbeddedWorks RZ/G2N Kit
              - hoperun,hihope-rzg2n # HopeRun HiHope RZ/G2N platform
          - const: renesas,r8a774b1

      - items:
          - enum:
              - hoperun,hihope-rzg2-ex # HopeRun expansion board for HiHope RZ/G2 platforms
          - const: hoperun,hihope-rzg2n
          - const: renesas,r8a774b1

      - description: RZ/G2E (R8A774C0)
        items:
          - enum:
              - si-linux,cat874 # Silicon Linux RZ/G2E 96board platform (CAT874)
          - const: renesas,r8a774c0

      - items:
          - enum:
              - si-linux,cat875 # Silicon Linux sub board for CAT874 (CAT875)
          - const: si-linux,cat874
          - const: renesas,r8a774c0

      - description: RZ/G2H (R8A774E1)
        items:
          - enum:
              - beacon,beacon-rzg2h # Beacon EmbeddedWorks RZ/G2H Kit
              - hoperun,hihope-rzg2h # HopeRun HiHope RZ/G2H platform
          - const: renesas,r8a774e1

      - items:
          - enum:
              - hoperun,hihope-rzg2-ex # HopeRun expansion board for HiHope RZ/G2 platforms
          - const: hoperun,hihope-rzg2h
          - const: renesas,r8a774e1

      - description: R-Car M1A (R8A77781)
        items:
          - enum:
              - renesas,bockw
          - const: renesas,r8a7778

      - description: R-Car H1 (R8A77790)
        items:
          - enum:
              - renesas,marzen # Marzen (R0P7779A00010S)
          - const: renesas,r8a7779

      - description: R-Car H2 (R8A77900)
        items:
          - enum:
              - renesas,lager # Lager (RTP0RC7790SEB00010S)
              - renesas,stout # Stout (ADAS Starterkit, Y-R-CAR-ADAS-SKH2-BOARD)
          - const: renesas,r8a7790

      - description: R-Car M2-W (R8A77910)
        items:
          - enum:
              - renesas,henninger
              - renesas,koelsch # Koelsch (RTP0RC7791SEB00010S)
              - renesas,porter # Porter (M2-LCDP)
          - const: renesas,r8a7791

      - description: R-Car V2H (R8A77920)
        items:
          - enum:
              - renesas,blanche # Blanche (RTP0RC7792SEB00010S)
              - renesas,wheat # Wheat (RTP0RC7792ASKB0000JE)
          - const: renesas,r8a7792

      - description: R-Car M2-N (R8A77930)
        items:
          - enum:
              - renesas,gose # Gose (RTP0RC7793SEB00010S)
          - const: renesas,r8a7793

      - description: R-Car E2 (R8A77940)
        items:
          - enum:
              - renesas,alt # Alt (RTP0RC7794SEB00010S)
              - renesas,silk # SILK (RTP0RC7794LCB00011S)
          - const: renesas,r8a7794

      # Note: R-Car H3 ES1.* (R8A77950) is not supported upstream anymore!

      - description: R-Car H3 ES2.0 and later (R8A77951)
        items:
          - enum:
              - renesas,h3ulcb # H3ULCB (R-Car Starter Kit Premier, RTP0RC77951SKBX010SA00 (H3 ES2.0))
              - renesas,salvator-x # Salvator-X (RTP0RC7795SIPB0010S)
              - renesas,salvator-xs # Salvator-XS (Salvator-X 2nd version, RTP0RC7795SIPB0012S)
          - const: renesas,r8a7795

      - description: R-Car M3-W (R8A77960)
        items:
          - enum:
              - renesas,m3ulcb # M3ULCB (R-Car Starter Kit Pro, RTP0RC7796SKBX0010SA09 (M3 ES1.0))
              - renesas,salvator-x # Salvator-X (RTP0RC7796SIPB0011S)
              - renesas,salvator-xs # Salvator-XS (Salvator-X 2nd version, RTP0RC7796SIPB0012S)
          - const: renesas,r8a7796

      - description: R-Car M3-W+ (R8A77961)
        items:
          - enum:
              - renesas,m3ulcb # M3ULCB (R-Car Starter Kit Pro, RTP8J77961ASKB0SK0SA05A (M3 ES3.0))
              - renesas,salvator-xs # Salvator-XS (Salvator-X 2nd version, RTP0RC7796SIPB0012SA5A)
          - const: renesas,r8a77961

      - description: Kingfisher (SBEV-RCAR-KF-M03)
        oneOf:
          - items:
              - const: shimafuji,kingfisher
              - enum:
                  - renesas,h3ulcb
                  - renesas,m3ulcb
                  - renesas,m3nulcb
              - enum:
                  - renesas,r8a7795
                  - renesas,r8a7796
                  - renesas,r8a77961
                  - renesas,r8a77965
          - items:
              - const: shimafuji,kingfisher
              - enum:
                  - renesas,h3ulcb
                  - renesas,m3ulcb
                  - renesas,m3nulcb
              - enum:
                  - renesas,r8a779m0
                  - renesas,r8a779m1
                  - renesas,r8a779m2
                  - renesas,r8a779m3
                  - renesas,r8a779m4
                  - renesas,r8a779m5
                  - renesas,r8a779m8
                  - renesas,r8a779mb
              - enum:
                  - renesas,r8a7795
                  - renesas,r8a77961
                  - renesas,r8a77965

      - description: R-Car M3-N (R8A77965)
        items:
          - enum:
              - renesas,m3nulcb # M3NULCB (R-Car Starter Kit Pro, RTP0RC77965SKBX010SA00 (M3-N ES1.1))
              - renesas,salvator-x # Salvator-X (RTP0RC7796SIPB0011S (M3-N))
              - renesas,salvator-xs # Salvator-XS (Salvator-X 2nd version, RTP0RC77965SIPB012S)
          - const: renesas,r8a77965

      - description: R-Car V3M (R8A77970)
        items:
          - enum:
              - renesas,eagle # Eagle (RTP0RC77970SEB0010S)
              - renesas,v3msk # V3MSK (Y-ASK-RCAR-V3M-WS10)
          - const: renesas,r8a77970

      - description: R-Car V3H (R8A77980)
        items:
          - enum:
              - renesas,condor # Condor (RTP0RC77980SEB0010SS/RTP0RC77980SEB0010SA01)
              - renesas,v3hsk # V3HSK (Y-ASK-RCAR-V3H-WS10)
          - const: renesas,r8a77980

      - description: R-Car V3H2 (R8A77980A)
        items:
          - enum:
              - renesas,condor-i # Condor-I (RTP0RC77980SEBS012SA01)
          - const: renesas,r8a77980a
          - const: renesas,r8a77980

      - description: R-Car E3 (R8A77990)
        items:
          - enum:
              - renesas,ebisu # Ebisu (RTP0RC77990SEB0010S), Ebisu-4D (RTP0RC77990SEB0020S)
          - const: renesas,r8a77990

      - description: R-Car D3 (R8A77995)
        items:
          - enum:
              - renesas,draak # Draak (RTP0RC77995SEB0010S)
          - const: renesas,r8a77995

      - description: R-Car V3U (R8A779A0)
        items:
          - enum:
              - renesas,falcon-cpu # Falcon CPU board (RTP0RC779A0CPB0010S)
          - const: renesas,r8a779a0

      - items:
          - enum:
              - renesas,falcon-breakout # Falcon BreakOut board (RTP0RC779A0BOB0010S)
          - const: renesas,falcon-cpu
          - const: renesas,r8a779a0

      - description: R-Car S4-8 (R8A779F0)
        items:
          - enum:
              - renesas,spider-cpu # Spider CPU board (RTP8A779F0ASKB0SC2S)
          - const: renesas,r8a779f0

      - items:
          - enum:
              - renesas,spider-breakout # Spider BreakOut board (RTP8A779F0ASKB0SB0S)
          - const: renesas,spider-cpu
          - const: renesas,r8a779f0

      - description: R-Car S4-8 (R8A779F4)
        items:
          - enum:
              - renesas,s4sk # R-Car S4 Starter Kit board (Y-ASK-RCAR-S4-1000BASE-T#WS12)
          - const: renesas,r8a779f4
          - const: renesas,r8a779f0

      - description: R-Car V4H (R8A779G0)
        items:
          - enum:
              - renesas,white-hawk-cpu # White Hawk CPU board (RTP8A779G0ASKB0FC0SA000)
          - const: renesas,r8a779g0

      - items:
          - enum:
              - renesas,white-hawk-breakout # White Hawk BreakOut board (RTP8A779G0ASKB0SB0SA000)
          - const: renesas,white-hawk-cpu
          - const: renesas,r8a779g0

      - description: R-Car V4H (R8A779G[23])
        items:
          - enum:
              - renesas,white-hawk-single # White Hawk Single board (RTP8A779G[23]ASKB0F10SA001)
          - enum:
              - renesas,r8a779g2 # ES2.x
              - renesas,r8a779g3 # ES3.x
          - const: renesas,r8a779g0

      - description: R-Car V4H (R8A779G3)
        items:
          - enum:
              - retronix,sparrow-hawk # Sparrow Hawk board
          - const: renesas,r8a779g3 # ES3.x
          - const: renesas,r8a779g0

      - description: R-Car V4M (R8A779H0)
        items:
          - enum:
              - renesas,gray-hawk-single # Gray Hawk Single board (RTP8A779H0ASKB0F10S)
          - const: renesas,r8a779h0

      - description: R-Car H3e (R8A779M0)
        items:
          - enum:
              - renesas,h3ulcb      # H3ULCB (R-Car Starter Kit Premier)
              - renesas,salvator-xs # Salvator-XS (Salvator-X 2nd version)
          - const: renesas,r8a779m0
          - const: renesas,r8a7795

      - description: R-Car H3e-2G (R8A779M1)
        items:
          - enum:
              - renesas,h3ulcb      # H3ULCB (R-Car Starter Kit Premier)
              - renesas,salvator-xs # Salvator-XS (Salvator-X 2nd version)
          - const: renesas,r8a779m1
          - const: renesas,r8a7795

      - description: R-Car M3e (R8A779M2)
        items:
          - enum:
              - renesas,m3ulcb      # M3ULCB (R-Car Starter Kit Pro)
              - renesas,salvator-xs # Salvator-XS (Salvator-X 2nd version)
          - const: renesas,r8a779m2
          - const: renesas,r8a77961

      - description: R-Car M3e-2G (R8A779M3)
        items:
          - enum:
              - renesas,m3ulcb      # M3ULCB (R-Car Starter Kit Pro)
              - renesas,salvator-xs # Salvator-XS (Salvator-X 2nd version)
          - const: renesas,r8a779m3
          - const: renesas,r8a77961

      - description: R-Car M3Ne (R8A779M4)
        items:
          - enum:
              - renesas,m3nulcb     # M3NULCB (R-Car Starter Kit Pro)
              - renesas,salvator-xs # Salvator-XS (Salvator-X 2nd version)
          - const: renesas,r8a779m4
          - const: renesas,r8a77965

      - description: R-Car M3Ne-2G (R8A779M5)
        items:
          - enum:
              - renesas,m3nulcb     # M3NULCB (R-Car Starter Kit Pro)
              - renesas,salvator-xs # Salvator-XS (Salvator-X 2nd version)
          - const: renesas,r8a779m5
          - const: renesas,r8a77965

      - description: R-Car E3e (R8A779M6)
        items:
          - enum:
              - renesas,ebisu       # Ebisu
          - const: renesas,r8a779m6
          - const: renesas,r8a77990

      - description: R-Car D3e (R8A779M7)
        items:
          - enum:
              - renesas,draak       # Draak
          - const: renesas,r8a779m7
          - const: renesas,r8a77995

      - description: R-Car H3Ne (R8A779M8)
        items:
          - enum:
              - renesas,h3ulcb      # H3ULCB (R-Car Starter Kit Premier)
              - renesas,salvator-xs # Salvator-XS (Salvator-X 2nd version)
          - const: renesas,r8a779m8
          - const: renesas,r8a7795

      - description: R-Car H3Ne-1.7G (R8A779MB)
        items:
          - enum:
              - renesas,h3ulcb      # H3ULCB (R-Car Starter Kit Premier)
              - renesas,salvator-xs # Salvator-XS (Salvator-X 2nd version)
          - const: renesas,r8a779mb
          - const: renesas,r8a7795

      - description: RZ/N1D (R9A06G032)
        items:
          - enum:
              - renesas,rzn1d400-db # RZN1D-DB (RZ/N1D Demo Board for the RZ/N1D 400 pins package)
          - const: renesas,r9a06g032

      - description: RZ/N1{D,S} EB
        items:
          - enum:
              - renesas,rzn1d400-eb # RZN1D-EB (Expansion Board when using a RZN1D-DB)
          - const: renesas,rzn1d400-db
          - const: renesas,r9a06g032

      - description: RZ/Five and RZ/G2UL (R9A07G043)
        items:
          - enum:
              - renesas,smarc-evk # SMARC EVK
          - enum:
              - renesas,r9a07g043f01 # RZ/Five
              - renesas,r9a07g043u11 # RZ/G2UL Type-1
              - renesas,r9a07g043u12 # RZ/G2UL Type-2
          - const: renesas,r9a07g043

      - description: RZ/G2{L,LC} (R9A07G044)
        items:
          - enum:
              - renesas,smarc-evk # SMARC EVK
          - enum:
              - renesas,r9a07g044c1 # Single Cortex-A55 RZ/G2LC
              - renesas,r9a07g044c2 # Dual Cortex-A55 RZ/G2LC
              - renesas,r9a07g044l1 # Single Cortex-A55 RZ/G2L
              - renesas,r9a07g044l2 # Dual Cortex-A55 RZ/G2L
          - const: renesas,r9a07g044

      - items:
          - enum:
              # MYIR Remi Pi SBC (MYB-YG2LX-REMI)
              - myir,remi-pi
          - const: renesas,r9a07g044l2
          - const: renesas,r9a07g044

      - description: RZ/V2L (R9A07G054)
        items:
          - enum:
              - renesas,smarc-evk # SMARC EVK
          - enum:
              - renesas,r9a07g054l1 # Single Cortex-A55 RZ/V2L
              - renesas,r9a07g054l2 # Dual Cortex-A55 RZ/V2L
          - const: renesas,r9a07g054

      - description: RZ/G3S (R9A08G045)
        items:
          - enum:
              - renesas,r9a08g045s33 # PCIe support
          - const: renesas,r9a08g045

      - description: RZ/G3S SMARC Module (SoM)
        items:
          - const: renesas,rzg3s-smarcm # RZ/G3S SMARC Module (SoM)
          - const: renesas,r9a08g045s33 # PCIe support
          - const: renesas,r9a08g045

      - description: RZ SMARC Carrier-II Evaluation Kit
        items:
          - const: renesas,smarc2-evk # RZ SMARC Carrier-II EVK
          - const: renesas,rzg3s-smarcm # RZ/G3S SMARC SoM
          - const: renesas,r9a08g045s33 # PCIe support
          - const: renesas,r9a08g045

      - description: RZ/V2M (R9A09G011)
        items:
          - enum:
              - renesas,rzv2mevk2   # RZ/V2M Eval Board v2.0
          - const: renesas,r9a09g011

      - description: RZ/G3E (R9A09G047)
        items:
          - enum:
              - renesas,smarc2-evk # RZ SMARC Carrier-II EVK
          - enum:
              - renesas,rzg3e-smarcm # RZ/G3E SMARC Module (SoM)
          - enum:
              - renesas,r9a09g047e27 # Dual Cortex-A55 + Cortex-M33 (15mm BGA)
              - renesas,r9a09g047e28 # Dual Cortex-A55 + Cortex-M33 (21mm BGA)
              - renesas,r9a09g047e37 # Dual Cortex-A55 + Cortex-M33 + Ethos-U55 (15mm BGA)
              - renesas,r9a09g047e38 # Dual Cortex-A55 + Cortex-M33 + Ethos-U55 (21mm BGA)
              - renesas,r9a09g047e47 # Quad Cortex-A55 + Cortex-M33 (15mm BGA)
              - renesas,r9a09g047e48 # Quad Cortex-A55 + Cortex-M33 (21mm BGA)
              - renesas,r9a09g047e57 # Quad Cortex-A55 + Cortex-M33 + Ethos-U55 (15mm BGA)
              - renesas,r9a09g047e58 # Quad Cortex-A55 + Cortex-M33 + Ethos-U55 (21mm BGA)
          - const: renesas,r9a09g047

      - description: RZ/V2N (R9A09G056)
        items:
          - enum:
              - renesas,rzv2n-evk # RZ/V2N EVK (RTK0EF0186C03000BJ)
          - enum:
              - renesas,r9a09g056n41 # RZ/V2N
              - renesas,r9a09g056n42 # RZ/V2N with Mali-G31 support
              - renesas,r9a09g056n43 # RZ/V2N with Mali-C55 support
              - renesas,r9a09g056n44 # RZ/V2N with Mali-G31 + Mali-C55 support
              - renesas,r9a09g056n45 # RZ/V2N with cryptographic extension support
              - renesas,r9a09g056n46 # RZ/V2N with Mali-G31 + cryptographic extension support
              - renesas,r9a09g056n47 # RZ/V2N with Mali-C55 + cryptographic extension support
              - renesas,r9a09g056n48 # RZ/V2N with Mali-G31 + Mali-C55 + cryptographic extension support
          - const: renesas,r9a09g056

      - description: RZ/V2H(P) (R9A09G057)
        items:
          - enum:
              - renesas,rzv2h-evk # RZ/V2H EVK
          - enum:
              - renesas,r9a09g057h41 # RZ/V2H
              - renesas,r9a09g057h42 # RZ/V2H with Mali-G31 support
              - renesas,r9a09g057h44 # RZ/V2HP with Mali-G31 + Mali-C55 support
              - renesas,r9a09g057h45 # RZ/V2H with cryptographic extension support
              - renesas,r9a09g057h46 # RZ/V2H with Mali-G31 + cryptographic extension support
              - renesas,r9a09g057h48 # RZ/V2HP with Mali-G31 + Mali-C55 + cryptographic extension support
          - const: renesas,r9a09g057

      - description: Yuridenki-Shokai RZ/V2H Kakip
        items:
          - const: yuridenki,kakip
          - const: renesas,r9a09g057h48
          - const: renesas,r9a09g057

      - description: RZ/T2H (R9A09G077)
        items:
          - enum:
              - renesas,rzt2h-evk # RZ/T2H Evaluation Board
          - enum:
              - renesas,r9a09g077m04 # RZ/T2H with Single Cortex-A55 + Dual Cortex-R52 - no security
              - renesas,r9a09g077m24 # RZ/T2H with Dual Cortex-A55 + Dual Cortex-R52 - no security
              - renesas,r9a09g077m44 # RZ/T2H with Quad Cortex-A55 + Dual Cortex-R52 - no security
          - const: renesas,r9a09g077

additionalProperties: true

...
