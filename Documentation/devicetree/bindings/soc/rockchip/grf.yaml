# SPDX-License-Identifier: GPL-2.0
%YAML 1.2
---
$id: http://devicetree.org/schemas/soc/rockchip/grf.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: Rockchip General Register Files (GRF)

maintainers:
  - <PERSON><PERSON> <<EMAIL>>

properties:
  compatible:
    oneOf:
      - items:
          - enum:
              - rockchip,rk3288-sgrf
              - rockchip,rk3528-ioc-grf
              - rockchip,rk3528-vo-grf
              - rockchip,rk3528-vpu-grf
              - rockchip,rk3562-ioc-grf
              - rockchip,rk3562-peri-grf
              - rockchip,rk3562-pipephy-grf
              - rockchip,rk3562-pmu-grf
              - rockchip,rk3562-sys-grf
              - rockchip,rk3562-usbphy-grf
              - rockchip,rk3566-pipe-grf
              - rockchip,rk3568-pcie3-phy-grf
              - rockchip,rk3568-pipe-grf
              - rockchip,rk3568-pipe-phy-grf
              - rockchip,rk3568-usb2phy-grf
              - rockchip,rk3576-bigcore-grf
              - rockchip,rk3576-cci-grf
              - rockchip,rk3576-gpu-grf
              - rockchip,rk3576-hdptxphy-grf
              - rockchip,rk3576-litcore-grf
              - rockchip,rk3576-npu-grf
              - rockchip,rk3576-php-grf
              - rockchip,rk3576-pipe-phy-grf
              - rockchip,rk3576-pmu1-grf
              - rockchip,rk3576-sdgmac-grf
              - rockchip,rk3576-sys-grf
              - rockchip,rk3576-usb-grf
              - rockchip,rk3576-usbdpphy-grf
              - rockchip,rk3576-vo0-grf
              - rockchip,rk3576-vo1-grf
              - rockchip,rk3576-vop-grf
              - rockchip,rk3588-bigcore0-grf
              - rockchip,rk3588-bigcore1-grf
              - rockchip,rk3588-dcphy-grf
              - rockchip,rk3588-hdptxphy-grf
              - rockchip,rk3588-ioc
              - rockchip,rk3588-php-grf
              - rockchip,rk3588-pipe-phy-grf
              - rockchip,rk3588-sys-grf
              - rockchip,rk3588-pcie3-phy-grf
              - rockchip,rk3588-pcie3-pipe-grf
              - rockchip,rk3588-usb-grf
              - rockchip,rk3588-usbdpphy-grf
              - rockchip,rk3588-vo0-grf
              - rockchip,rk3588-vo1-grf
              - rockchip,rk3588-vop-grf
              - rockchip,rv1108-usbgrf
          - const: syscon
      - items:
          - const: rockchip,rk3588-vo-grf
          - const: syscon
        deprecated: true
        description: Use rockchip,rk3588-vo{0,1}-grf instead.
      - items:
          - enum:
              - rockchip,px30-grf
              - rockchip,px30-pmugrf
              - rockchip,px30-usb2phy-grf
              - rockchip,rk3036-grf
              - rockchip,rk3066-grf
              - rockchip,rk3128-grf
              - rockchip,rk3188-grf
              - rockchip,rk3228-grf
              - rockchip,rk3288-grf
              - rockchip,rk3308-core-grf
              - rockchip,rk3308-detect-grf
              - rockchip,rk3308-grf
              - rockchip,rk3308-usb2phy-grf
              - rockchip,rk3328-grf
              - rockchip,rk3328-usb2phy-grf
              - rockchip,rk3368-grf
              - rockchip,rk3368-pmugrf
              - rockchip,rk3399-grf
              - rockchip,rk3399-pmugrf
              - rockchip,rk3562-pmu-grf
              - rockchip,rk3568-grf
              - rockchip,rk3568-pmugrf
              - rockchip,rk3576-ioc-grf
              - rockchip,rk3576-pmu0-grf
              - rockchip,rk3576-usb2phy-grf
              - rockchip,rk3588-usb2phy-grf
              - rockchip,rv1108-grf
              - rockchip,rv1108-pmugrf
              - rockchip,rv1126-grf
              - rockchip,rv1126-pmugrf
          - const: syscon
          - const: simple-mfd

  reg:
    maxItems: 1

  clocks:
    maxItems: 1

  "#address-cells":
    const: 1

  "#size-cells":
    const: 1

required:
  - compatible
  - reg

additionalProperties:
  type: object

allOf:
  - if:
      properties:
        compatible:
          contains:
            enum:
              - rockchip,px30-grf

    then:
      properties:
        lvds:
          type: object

          $ref: /schemas/display/rockchip/rockchip,lvds.yaml#

          unevaluatedProperties: false

  - if:
      properties:
        compatible:
          contains:
            const: rockchip,rk3288-grf

    then:
      properties:
        edp-phy:
          type: object
          $ref: /schemas/phy/rockchip,rk3288-dp-phy.yaml#
          unevaluatedProperties: false

  - if:
      properties:
        compatible:
          contains:
            enum:
              - rockchip,rk3066-grf
              - rockchip,rk3188-grf
              - rockchip,rk3288-grf

    then:
      properties:
        usbphy:
          type: object

          $ref: /schemas/phy/rockchip-usb-phy.yaml#

          unevaluatedProperties: false

  - if:
      properties:
        compatible:
          contains:
            const: rockchip,rk3328-grf

    then:
      properties:
        gpio:
          type: object

          $ref: /schemas/gpio/rockchip,rk3328-grf-gpio.yaml#

          unevaluatedProperties: false

        power-controller:
          type: object

          $ref: /schemas/power/rockchip,power-controller.yaml#

          unevaluatedProperties: false

  - if:
      properties:
        compatible:
          contains:
            const: rockchip,rk3399-grf

    then:
      properties:
        mipi-dphy-rx0:
          type: object

          $ref: /schemas/phy/rockchip-mipi-dphy-rx0.yaml#

          unevaluatedProperties: false

        pcie-phy:
          type: object
          $ref: /schemas/phy/rockchip,rk3399-pcie-phy.yaml#
          unevaluatedProperties: false

      patternProperties:
        "^phy@[0-9a-f]+$":
          type: object
          $ref: /schemas/phy/rockchip,rk3399-emmc-phy.yaml#
          unevaluatedProperties: false

  - if:
      properties:
        compatible:
          contains:
            enum:
              - rockchip,px30-pmugrf
              - rockchip,rk3036-grf
              - rockchip,rk3308-grf
              - rockchip,rk3368-pmugrf

    then:
      properties:
        reboot-mode:
          type: object

          $ref: /schemas/power/reset/syscon-reboot-mode.yaml#

          unevaluatedProperties: false

  - if:
      properties:
        compatible:
          contains:
            enum:
              - rockchip,px30-usb2phy-grf
              - rockchip,rk3128-grf
              - rockchip,rk3228-grf
              - rockchip,rk3308-usb2phy-grf
              - rockchip,rk3328-usb2phy-grf
              - rockchip,rk3399-grf
              - rockchip,rk3576-usb2phy-grf
              - rockchip,rk3588-usb2phy-grf
              - rockchip,rv1108-grf

    then:
      required:
        - "#address-cells"
        - "#size-cells"

      patternProperties:
        "usb2phy@[0-9a-f]+$":
          type: object

          $ref: /schemas/phy/rockchip,inno-usb2phy.yaml#

          unevaluatedProperties: false

  - if:
      properties:
        compatible:
          contains:
            enum:
              - rockchip,px30-grf
              - rockchip,px30-pmugrf
              - rockchip,rk3188-grf
              - rockchip,rk3228-grf
              - rockchip,rk3288-grf
              - rockchip,rk3328-grf
              - rockchip,rk3368-grf
              - rockchip,rk3368-pmugrf
              - rockchip,rk3399-grf
              - rockchip,rk3399-pmugrf
              - rockchip,rk3568-pmugrf
              - rockchip,rk3588-pmugrf
              - rockchip,rv1108-grf
              - rockchip,rv1108-pmugrf

    then:
      properties:
        io-domains:
          type: object

          $ref: /schemas/power/rockchip-io-domain.yaml#

          unevaluatedProperties: false

  - if:
      properties:
        compatible:
          contains:
            enum:
              - rockchip,rk3576-vo1-grf
              - rockchip,rk3588-vo-grf
              - rockchip,rk3588-vo0-grf
              - rockchip,rk3588-vo1-grf

    then:
      required:
        - clocks

    else:
      properties:
        clocks: false


examples:
  - |
    #include <dt-bindings/clock/rk3399-cru.h>
    #include <dt-bindings/interrupt-controller/arm-gic.h>
    #include <dt-bindings/power/rk3399-power.h>
    grf: syscon@ff770000 {
      compatible = "rockchip,rk3399-grf", "syscon", "simple-mfd";
      reg = <0xff770000 0x10000>;
      #address-cells = <1>;
      #size-cells = <1>;

      mipi_dphy_rx0: mipi-dphy-rx0 {
        compatible = "rockchip,rk3399-mipi-dphy-rx0";
        clocks = <&cru SCLK_MIPIDPHY_REF>,
                 <&cru SCLK_DPHY_RX0_CFG>,
                 <&cru PCLK_VIO_GRF>;
        clock-names = "dphy-ref", "dphy-cfg", "grf";
        power-domains = <&power RK3399_PD_VIO>;
        #phy-cells = <0>;
      };

      pcie-phy {
        compatible = "rockchip,rk3399-pcie-phy";
        #phy-cells = <1>;
        clocks = <&cru SCLK_PCIEPHY_REF>;
        clock-names = "refclk";
        resets = <&cru SRST_PCIEPHY>;
        reset-names = "phy";
      };

      phy@f780 {
        compatible = "rockchip,rk3399-emmc-phy";
        reg = <0xf780 0x20>;
        clocks = <&sdhci>;
        clock-names = "emmcclk";
        drive-impedance-ohm = <50>;
        #phy-cells = <0>;
      };

      u2phy0: usb2phy@e450 {
        compatible = "rockchip,rk3399-usb2phy";
        reg = <0xe450 0x10>;
        clocks = <&cru SCLK_USB2PHY0_REF>;
        clock-names = "phyclk";
        #clock-cells = <0>;
        clock-output-names = "clk_usbphy0_480m";

        u2phy0_host: host-port {
          #phy-cells = <0>;
          interrupts = <GIC_SPI 27 IRQ_TYPE_LEVEL_HIGH 0>;
          interrupt-names = "linestate";
         };

        u2phy0_otg: otg-port {
          #phy-cells = <0>;
          interrupts = <GIC_SPI 103 IRQ_TYPE_LEVEL_HIGH 0>,
                       <GIC_SPI 104 IRQ_TYPE_LEVEL_HIGH 0>,
                       <GIC_SPI 106 IRQ_TYPE_LEVEL_HIGH 0>;
          interrupt-names = "otg-bvalid", "otg-id",
                            "linestate";
        };
      };
    };
