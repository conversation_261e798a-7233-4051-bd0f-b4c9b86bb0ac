# SPDX-License-Identifier: (GPL-2.0 OR BSD-2-Clause)
%YAML 1.2
---
$id: http://devicetree.org/schemas/soc/imx/fsl,aips-bus.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: i.MX AHB to IP Bridge

maintainers:
  - Peng Fan <<EMAIL>>

description: |
  This particular peripheral is designed as the bridge between
  AHB bus and peripherals with the lower bandwidth IP Slave (IPS)
  buses.

select:
  properties:
    compatible:
      contains:
        const: fsl,aips-bus
  required:
    - compatible

allOf:
  - $ref: /schemas/simple-bus.yaml#

properties:
  compatible:
    items:
      - const: fsl,aips-bus
      - const: simple-bus

  reg:
    maxItems: 1

required:
  - compatible
  - reg

unevaluatedProperties: false

examples:
  - |
    bus@30000000 {
      compatible = "fsl,aips-bus", "simple-bus";
      reg = <0x30000000 0x400000>;
      #address-cells = <1>;
      #size-cells = <1>;
      ranges;
    };
...
