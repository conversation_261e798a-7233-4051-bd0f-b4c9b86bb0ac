# SPDX-License-Identifier: GPL-2.0
%YAML 1.2
---
$id: http://devicetree.org/schemas/soc/xilinx/xilinx.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: Xilinx Zynq Platforms

maintainers:
  - <PERSON><PERSON> <<EMAIL>>

description:
  AMD/Xilinx boards with ARM 32/64bits cores

properties:
  $nodename:
    const: '/'
  compatible:
    oneOf:
      - items:
          - enum:
              - adapteva,parallella
              - digilent,zynq-zybo
              - digilent,zynq-zybo-z7
              - ebang,ebaz4205
              - myir,zynq-zturn-v5
              - myir,zynq-zturn
              - xlnx,zynq-cc108
              - xlnx,zynq-zc702
              - xlnx,zynq-zc706
              - xlnx,zynq-zc770-xm010
              - xlnx,zynq-zc770-xm011
              - xlnx,zynq-zc770-xm012
              - xlnx,zynq-zc770-xm013
          - const: xlnx,zynq-7000

      - items:
          - const: avnet,zynq-microzed
          - const: xlnx,zynq-microzed
          - const: xlnx,zynq-7000

      - items:
          - const: avnet,zynq-zed
          - const: xlnx,zynq-zed
          - const: xlnx,zynq-7000

      - items:
          - enum:
              - xlnx,zynqmp-zc1751
          - const: xlnx,zynqmp

      - description: Xilinx internal board zc1232
        items:
          - const: xlnx,zynqmp-zc1232-revA
          - const: xlnx,zynqmp-zc1232
          - const: xlnx,zynqmp

      - description: Xilinx internal board zc1254
        items:
          - const: xlnx,zynqmp-zc1254-revA
          - const: xlnx,zynqmp-zc1254
          - const: xlnx,zynqmp

      - description: Xilinx evaluation board zcu1275
        items:
          - const: xlnx,zynqmp-zcu1275-revA
          - const: xlnx,zynqmp-zcu1275
          - const: xlnx,zynqmp

      - description: Xilinx 96boards compatible board zcu100
        items:
          - const: xlnx,zynqmp-zcu100-revC
          - const: xlnx,zynqmp-zcu100
          - const: xlnx,zynqmp

      - description: Xilinx 96boards compatible board Ultra96
        items:
          - const: avnet,ultra96-rev1
          - const: avnet,ultra96
          - const: xlnx,zynqmp-zcu100-revC
          - const: xlnx,zynqmp-zcu100
          - const: xlnx,zynqmp

      - description: Xilinx evaluation board zcu102
        items:
          - enum:
              - xlnx,zynqmp-zcu102-revA
              - xlnx,zynqmp-zcu102-revB
              - xlnx,zynqmp-zcu102-rev1.0
              - xlnx,zynqmp-zcu102-rev1.1
          - const: xlnx,zynqmp-zcu102
          - const: xlnx,zynqmp

      - description: Xilinx evaluation board zcu104
        items:
          - enum:
              - xlnx,zynqmp-zcu104-revA
              - xlnx,zynqmp-zcu104-revC
              - xlnx,zynqmp-zcu104-rev1.0
          - const: xlnx,zynqmp-zcu104
          - const: xlnx,zynqmp

      - description: Xilinx evaluation board zcu106
        items:
          - enum:
              - xlnx,zynqmp-zcu106-revA
              - xlnx,zynqmp-zcu106-rev1.0
          - const: xlnx,zynqmp-zcu106
          - const: xlnx,zynqmp

      - description: Xilinx evaluation board zcu111
        items:
          - enum:
              - xlnx,zynqmp-zcu111-revA
              - xlnx,zynqmp-zcu111-rev1.0
          - const: xlnx,zynqmp-zcu111
          - const: xlnx,zynqmp

      - description: Xilinx Kria SOMs
        minItems: 3
        items:
          enum:
            - xlnx,zynqmp-sm-k26-rev2
            - xlnx,zynqmp-sm-k26-rev1
            - xlnx,zynqmp-sm-k26-revB
            - xlnx,zynqmp-sm-k26-revA
            - xlnx,zynqmp-sm-k26
            - xlnx,zynqmp
        allOf:
          - contains:
              const: xlnx,zynqmp
          - contains:
              const: xlnx,zynqmp-sm-k26

      - description: Xilinx Kria SOMs (starter)
        minItems: 3
        items:
          enum:
            - xlnx,zynqmp-smk-k26-rev2
            - xlnx,zynqmp-smk-k26-rev1
            - xlnx,zynqmp-smk-k26-revB
            - xlnx,zynqmp-smk-k26-revA
            - xlnx,zynqmp-smk-k26
            - xlnx,zynqmp
        allOf:
          - contains:
              const: xlnx,zynqmp
          - contains:
              const: xlnx,zynqmp-smk-k26

      - description: Xilinx Kria SOM KV260 revA/Y/Z
        minItems: 3
        items:
          enum:
            - xlnx,zynqmp-sk-kv260-revA
            - xlnx,zynqmp-sk-kv260-revY
            - xlnx,zynqmp-sk-kv260-revZ
            - xlnx,zynqmp-sk-kv260
            - xlnx,zynqmp
        allOf:
          - contains:
              const: xlnx,zynqmp-sk-kv260-revA
          - contains:
              const: xlnx,zynqmp-sk-kv260
          - contains:
              const: xlnx,zynqmp

      - description: Xilinx Kria SOM KV260 rev2/1/B
        minItems: 3
        items:
          enum:
            - xlnx,zynqmp-sk-kv260-rev2
            - xlnx,zynqmp-sk-kv260-rev1
            - xlnx,zynqmp-sk-kv260-revB
            - xlnx,zynqmp-sk-kv260
            - xlnx,zynqmp
        allOf:
          - contains:
              const: xlnx,zynqmp-sk-kv260-revB
          - contains:
              const: xlnx,zynqmp-sk-kv260
          - contains:
              const: xlnx,zynqmp

      - description: AMD MicroBlaze V (QEMU)
        items:
          - const: qemu,mbv
          - const: amd,mbv

      - description: Xilinx Versal NET VN-X revA platform
        items:
          enum:
            - xlnx,versal-net-vnx-revA
            - xlnx,versal-net-vnx
            - xlnx,versal-net

additionalProperties: true

...
