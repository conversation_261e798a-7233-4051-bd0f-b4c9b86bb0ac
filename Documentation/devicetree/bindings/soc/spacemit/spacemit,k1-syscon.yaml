# SPDX-License-Identifier: (GPL-2.0-only OR BSD-2-Clause)
%YAML 1.2
---
$id: http://devicetree.org/schemas/soc/spacemit/spacemit,k1-syscon.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: SpacemiT K1 SoC System Controller

maintainers:
  - <PERSON><PERSON> <<EMAIL>>

description:
  System controllers found on SpacemiT K1 SoC, which are capable of
  clock, reset and power-management functions.

properties:
  compatible:
    enum:
      - spacemit,k1-syscon-apbc
      - spacemit,k1-syscon-apmu
      - spacemit,k1-syscon-mpmu

  reg:
    maxItems: 1

  clocks:
    maxItems: 4

  clock-names:
    items:
      - const: osc
      - const: vctcxo_1m
      - const: vctcxo_3m
      - const: vctcxo_24m

  "#clock-cells":
    const: 1
    description:
      See <dt-bindings/clock/spacemit,k1-syscon.h> for valid indices.

  "#power-domain-cells":
    const: 1

  "#reset-cells":
    const: 1

required:
  - compatible
  - reg
  - clocks
  - clock-names
  - "#clock-cells"
  - "#reset-cells"

allOf:
  - if:
      properties:
        compatible:
          contains:
            const: spacemit,k1-syscon-apbc
    then:
      properties:
        "#power-domain-cells": false
    else:
      required:
        - "#power-domain-cells"

additionalProperties: false

examples:
  - |
    system-controller@d4050000 {
        compatible = "spacemit,k1-syscon-mpmu";
        reg = <0xd4050000 0x209c>;
        clocks = <&osc>, <&vctcxo_1m>, <&vctcxo_3m>, <&vctcxo_24m>;
        clock-names = "osc", "vctcxo_1m", "vctcxo_3m", "vctcxo_24m";
        #clock-cells = <1>;
        #power-domain-cells = <1>;
        #reset-cells = <1>;
    };
