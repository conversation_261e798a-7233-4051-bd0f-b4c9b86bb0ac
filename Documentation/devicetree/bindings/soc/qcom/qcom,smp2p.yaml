# SPDX-License-Identifier: GPL-2.0-only OR BSD-2-Clause
%YAML 1.2
---
$id: http://devicetree.org/schemas/soc/qcom/qcom,smp2p.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: Qualcomm Shared Memory Point 2 Point

maintainers:
  - <PERSON> <<EMAIL>>
  - <PERSON><PERSON><PERSON> <bjorn.and<PERSON><PERSON>@linaro.org>
  - <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> <<EMAIL>>

description:
  The Shared Memory Point to Point (SMP2P) protocol facilitates communication
  of a single 32-bit value between two processors.  Each value has a single
  writer (the local side) and a single reader (the remote side).  Values are
  uniquely identified in the system by the directed edge (local processor ID to
  remote processor ID) and a string identifier.

properties:
  compatible:
    const: qcom,smp2p

  interrupts:
    maxItems: 1

  mboxes:
    maxItems: 1
    description:
      Reference to the mailbox representing the outgoing doorbell in APCS for
      this client.

  qcom,ipc:
    $ref: /schemas/types.yaml#/definitions/phandle-array
    items:
      - items:
          - description: phandle to a syscon node representing the APCS registers
          - description: u32 representing offset to the register within the syscon
          - description: u32 representing the ipc bit within the register
    description:
      Three entries specifying the outgoing ipc bit used for signaling the
      remote end of the smp2p edge.
    deprecated: true

  qcom,local-pid:
    $ref: /schemas/types.yaml#/definitions/uint32
    description:
      The identifier of the local endpoint of this edge.

  qcom,remote-pid:
    $ref: /schemas/types.yaml#/definitions/uint32
    description:
      The identifier of the remote endpoint of this edge.

  qcom,smem:
    $ref: /schemas/types.yaml#/definitions/uint32-array
    maxItems: 2
    description:
      Two identifiers of the inbound and outbound smem items used for this edge.

patternProperties:
  "^master-kernel|slave-kernel|ipa-ap-to-modem|ipa-modem-to-ap|wlan-ap-to-wpss|wlan-wpss-to-ap$":
    type: object
    description:
      Each SMP2P pair contain a set of inbound and outbound entries, these are
      described in subnodes of the smp2p device node. The node names are not
      important.

    properties:
      interrupt-controller:
        description:
          Marks the entry as inbound; the node should be specified as a two
          cell interrupt-controller.  If not specified this node will denote
          the outgoing entry.

      '#interrupt-cells':
        const: 2

      qcom,entry-name:
        $ref: /schemas/types.yaml#/definitions/string
        description:
          The name of this entry, for inbound entries this will be used to
          match against the remotely allocated entry and for outbound entries
          this name is used for allocating entries.

      '#qcom,smem-state-cells':
        $ref: /schemas/types.yaml#/definitions/uint32
        const: 1
        description:
          Required for outgoing entries.

    required:
      - qcom,entry-name

    oneOf:
      - required:
          - interrupt-controller
          - '#interrupt-cells'
      - required:
          - '#qcom,smem-state-cells'

    additionalProperties: false

required:
  - compatible
  - interrupts
  - qcom,local-pid
  - qcom,remote-pid
  - qcom,smem

oneOf:
  - required:
      - mboxes
  - required:
      - qcom,ipc

additionalProperties: false

examples:
  # The following example shows the SMP2P setup with the wireless processor,
  # defined from the 8974 apps processor's point-of-view. It encompasses one
  # inbound and one outbound entry.
  - |
    #include <dt-bindings/interrupt-controller/arm-gic.h>

    shared-memory {
        compatible = "qcom,smp2p";
        qcom,smem = <431>, <451>;
        interrupts = <GIC_SPI 143 IRQ_TYPE_EDGE_RISING>;
        mboxes = <&apcs 18>;
        qcom,local-pid = <0>;
        qcom,remote-pid = <4>;

        wcnss_smp2p_out: master-kernel {
            qcom,entry-name = "master-kernel";
            #qcom,smem-state-cells = <1>;
        };

        wcnss_smp2p_in: slave-kernel {
            qcom,entry-name = "slave-kernel";
            interrupt-controller;
            #interrupt-cells = <2>;
        };
    };
