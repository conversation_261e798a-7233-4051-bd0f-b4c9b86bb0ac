# SPDX-License-Identifier: (GPL-2.0-only OR BSD-2-Clause)
%YAML 1.2
---
$id: http://devicetree.org/schemas/soc/qcom/qcom,pmic-glink.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: Qualcomm PMIC GLINK firmware interface for battery management, USB
  Type-C and other things.

maintainers:
  - <PERSON><PERSON><PERSON> <and<PERSON><PERSON>@kernel.org>

description:
  The PMIC GLINK service, running on a coprocessor on some modern Qualcomm
  platforms and implement USB Type-C handling and battery management. This
  binding describes the component in the OS used to communicate with the
  firmware and connect it's resources to those described in the Devicetree,
  particularly the USB Type-C controllers relationship with USB and DisplayPort
  components.

properties:
  compatible:
    oneOf:
      - items:
          - enum:
              - qcom,qcm6490-pmic-glink
              - qcom,sc8180x-pmic-glink
              - qcom,sc8280xp-pmic-glink
              - qcom,sm8350-pmic-glink
              - qcom,sm8450-pmic-glink
              - qcom,sm8550-pmic-glink
          - const: qcom,pmic-glink
      - items:
          - enum:
              - qcom,sm7325-pmic-glink
          - const: qcom,qcm6490-pmic-glink
          - const: qcom,pmic-glink
      - items:
          - enum:
              - qcom,sm8650-pmic-glink
              - qcom,sm8750-pmic-glink
              - qcom,x1e80100-pmic-glink
          - const: qcom,sm8550-pmic-glink
          - const: qcom,pmic-glink

  '#address-cells':
    const: 1

  '#size-cells':
    const: 0

  orientation-gpios:
    description: Array of input gpios for the Type-C connector orientation indication.
      The GPIO indication is used to detect the orientation of the Type-C connector.
      The array should contain a gpio entry for each PMIC Glink connector, in reg order.
      It is defined that GPIO active level means "CC2" or Reversed/Flipped orientation.

patternProperties:
  '^connector@\d$':
    $ref: /schemas/connector/usb-connector.yaml#
    required:
      - reg

required:
  - compatible

additionalProperties: false

examples:
  - |+
    pmic-glink {
        compatible = "qcom,sc8280xp-pmic-glink", "qcom,pmic-glink";

        #address-cells = <1>;
        #size-cells = <0>;

        connector@0 {
            compatible = "usb-c-connector";
            reg = <0>;
            power-role = "dual";
            data-role = "dual";

            ports {
                #address-cells = <1>;
                #size-cells = <0>;

                port@0 {
                    reg = <0>;
                    endpoint {
                        remote-endpoint = <&usb_role>;
                    };
                };

                port@1 {
                    reg = <1>;
                    endpoint {
                        remote-endpoint = <&ss_phy_out>;
                    };
                };

                port@2 {
                    reg = <2>;
                    endpoint {
                        remote-endpoint = <&sbu_mux>;
                    };
                };
            };
        };
    };
...

