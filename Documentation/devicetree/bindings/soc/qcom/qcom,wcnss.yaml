# SPDX-License-Identifier: GPL-2.0-only OR BSD-2-Clause
%YAML 1.2
---
$id: http://devicetree.org/schemas/soc/qcom/qcom,wcnss.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: Qualcomm WCNSS

maintainers:
  - <PERSON> <<EMAIL>>
  - <PERSON><PERSON><PERSON> <bjorn.and<PERSON><PERSON>@linaro.org>

description:
  The Qualcomm WCNSS hardware consists of control block and a BT, WiFi and FM
  radio block, all using SMD as command channels.

properties:
  compatible:
    const: qcom,wcnss

  firmware-name:
    $ref: /schemas/types.yaml#/definitions/string
    default: wlan/prima/WCNSS_qcom_wlan_nv.bin
    description:
      Relative firmware image path for the WLAN NV blob.

  qcom,mmio:
    $ref: /schemas/types.yaml#/definitions/phandle
    description: |
      Reference to a node specifying the wcnss "ccu" and "dxe" register blocks.
      The node must be compatible with one of the following::
           - q<PERSON>,riva"
           - qcom,pronto"

  qcom,smd-channels:
    $ref: /schemas/types.yaml#/definitions/string
    const: WCNSS_CTRL
    description:
      Standard SMD property specifying the SMD channel used for communication
      with the WiFi firmware.

  bluetooth:
    type: object
    additionalProperties: false
    allOf:
      - $ref: /schemas/net/bluetooth/bluetooth-controller.yaml#
    properties:
      compatible:
        const: qcom,wcnss-bt

      local-bd-address: true

    required:
      - compatible

  wifi:
    unevaluatedProperties: false
    type: object
    properties:
      compatible:
        const: qcom,wcnss-wlan

      interrupts:
        maxItems: 2

      interrupt-names:
        items:
          - const: tx
          - const: rx

      qcom,smem-states:
        $ref: /schemas/types.yaml#/definitions/phandle-array
        maxItems: 2
        description:
          Should reference the tx-enable and tx-rings-empty SMEM states.

      qcom,smem-state-names:
        items:
          - const: tx-enable
          - const: tx-rings-empty
        description:
          Names of SMEM states.

    required:
      - compatible
      - interrupts
      - interrupt-names
      - qcom,smem-states
      - qcom,smem-state-names

    allOf:
      - $ref: /schemas/net/wireless/wireless-controller.yaml#

required:
  - compatible
  - qcom,mmio
  - qcom,smd-channels

additionalProperties: false

examples:
  - |
    #include <dt-bindings/interrupt-controller/arm-gic.h>

    smd-edge {
        interrupts = <GIC_SPI 142 IRQ_TYPE_EDGE_RISING>;

        qcom,ipc = <&apcs 8 17>;
        qcom,smd-edge = <6>;
        qcom,remote-pid = <4>;

        label = "pronto";

        wcnss {
            compatible = "qcom,wcnss";
            qcom,smd-channels = "WCNSS_CTRL";

            qcom,mmio = <&pronto>;

            bluetooth {
                compatible = "qcom,wcnss-bt";
                /* Updated by boot firmware (little-endian order) */
                local-bd-address = [ 00 00 00 00 00 00 ];
            };

            wifi {
                compatible = "qcom,wcnss-wlan";

                interrupts = <GIC_SPI 145 IRQ_TYPE_LEVEL_HIGH>,
                             <GIC_SPI 146 IRQ_TYPE_LEVEL_HIGH>;
                interrupt-names = "tx", "rx";

                qcom,smem-states = <&apps_smsm 10>, <&apps_smsm 9>;
                qcom,smem-state-names = "tx-enable", "tx-rings-empty";
            };
        };
    };
