# SPDX-License-Identifier: (GPL-2.0 OR BSD-2-Clause)
%YAML 1.2
---
$id: http://devicetree.org/schemas/soc/qcom/qcom,rpm.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: Qualcomm Resource Power Manager (RPM)

description:
  This driver is used to interface with the Resource Power Manager (RPM) found
  in various Qualcomm platforms. The RPM allows each component in the system
  to vote for state of the system resources, such as clocks, regulators and bus
  frequencies.

maintainers:
  - <PERSON><PERSON><PERSON> <and<PERSON><PERSON>@kernel.org>

properties:
  compatible:
    enum:
      - qcom,rpm-apq8064
      - qcom,rpm-msm8660
      - qcom,rpm-msm8960
      - qcom,rpm-ipq8064
      - qcom,rpm-mdm9615

  reg:
    maxItems: 1

  interrupts:
    maxItems: 3

  interrupt-names:
    items:
      - const: ack
      - const: err
      - const: wakeup

  clocks:
    maxItems: 1

  clock-names:
    items:
      - const: ram

  qcom,ipc:
    $ref: /schemas/types.yaml#/definitions/phandle-array
    items:
      - items:
          - description: phandle to a syscon node representing the APCS registers
          - description: u32 representing offset to the register within the syscon
          - description: u32 representing the ipc bit within the register
    description:
      Three entries specifying the outgoing ipc bit used for signaling the RPM.

  clock-controller:
    type: object
    additionalProperties: true
    properties:
      compatible:
        contains:
          const: qcom,rpmcc

patternProperties:
  "^regulators(-[01])?$":
    type: object
    $ref: /schemas/regulator/qcom,rpm-regulator.yaml#
    unevaluatedProperties: false

required:
  - compatible
  - reg
  - interrupts
  - interrupt-names
  - qcom,ipc

additionalProperties: false

examples:
  - |
    #include <dt-bindings/interrupt-controller/arm-gic.h>
    #include <dt-bindings/interrupt-controller/irq.h>
    #include <dt-bindings/mfd/qcom-rpm.h>

    rpm@108000 {
      compatible = "qcom,rpm-msm8960";
      reg = <0x108000 0x1000>;
      qcom,ipc = <&apcs 0x8 2>;

      interrupts = <GIC_SPI 19 IRQ_TYPE_NONE>, <GIC_SPI 21 IRQ_TYPE_NONE>, <GIC_SPI 22 IRQ_TYPE_NONE>;
      interrupt-names = "ack", "err", "wakeup";

      regulators {
        compatible = "qcom,rpm-pm8921-regulators";
        vdd_l1_l2_l12_l18-supply = <&pm8921_s4>;

        s1 {
          regulator-min-microvolt = <1225000>;
          regulator-max-microvolt = <1225000>;

          bias-pull-down;

          qcom,switch-mode-frequency = <3200000>;
        };

        pm8921_s4: s4 {
          regulator-min-microvolt = <1800000>;
          regulator-max-microvolt = <1800000>;

          qcom,switch-mode-frequency = <1600000>;
          bias-pull-down;

          qcom,force-mode = <QCOM_RPM_FORCE_MODE_AUTO>;
        };
      };
    };
