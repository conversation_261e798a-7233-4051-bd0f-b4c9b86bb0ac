# SPDX-License-Identifier: (GPL-2.0 OR BSD-2-Clause)
%YAML 1.2
---
$id: http://devicetree.org/schemas/soc/mediatek/mediatek,mt8183-dvfsrc.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: MediaTek Dynamic Voltage and Frequency Scaling Resource Collector (DVFSRC)

description:
  The Dynamic Voltage and Frequency Scaling Resource Collector (DVFSRC) is a
  Hardware module used to collect all the requests from both software and the
  various remote processors embedded into the SoC and decide about a minimum
  operating voltage and a minimum DRAM frequency to fulfill those requests in
  an effort to provide the best achievable performance per watt.
  This hardware IP is capable of transparently performing direct register R/W
  on all of the DVFSRC-controlled regulators and SoC bandwidth knobs.

maintainers:
  - AngeloGioacchino <PERSON> <<EMAIL>>
  - <PERSON> <<EMAIL>>

properties:
  compatible:
    oneOf:
      - enum:
          - mediatek,mt6893-dvfsrc
          - mediatek,mt8183-dvfsrc
          - mediatek,mt8195-dvfsrc
      - items:
          - const: mediatek,mt8192-dvfsrc
          - const: mediatek,mt8195-dvfsrc

  reg:
    maxItems: 1
    description: DVFSRC common register address and length.

  regulators:
    type: object
    $ref: /schemas/regulator/mediatek,mt6873-dvfsrc-regulator.yaml#

  interconnect:
    type: object
    $ref: /schemas/interconnect/mediatek,mt8183-emi.yaml#

required:
  - compatible
  - reg

additionalProperties: false

examples:
  - |
    soc {
        #address-cells = <2>;
        #size-cells = <2>;

        system-controller@10012000 {
            compatible = "mediatek,mt8195-dvfsrc";
            reg = <0 0x10012000 0 0x1000>;

            regulators {
                compatible = "mediatek,mt8195-dvfsrc-regulator";

                dvfsrc_vcore: dvfsrc-vcore {
                        regulator-name = "dvfsrc-vcore";
                        regulator-min-microvolt = <550000>;
                        regulator-max-microvolt = <750000>;
                        regulator-always-on;
                };

                dvfsrc_vscp: dvfsrc-vscp {
                        regulator-name = "dvfsrc-vscp";
                        regulator-min-microvolt = <550000>;
                        regulator-max-microvolt = <750000>;
                        regulator-always-on;
                };
            };

            emi_icc: interconnect {
                compatible = "mediatek,mt8195-emi";
                #interconnect-cells = <1>;
            };
        };
    };
