# SPDX-License-Identifier: (GPL-2.0-only OR BSD-2-Clause)
%YAML 1.2
---
$id: http://devicetree.org/schemas/soc/amlogic/amlogic,meson-gx-hhi-sysctrl.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: Amlogic Meson System Control registers

maintainers:
  - <PERSON> <<EMAIL>>

properties:
  compatible:
    items:
      - enum:
          - amlogic,meson-hhi-sysctrl
          - amlogic,meson-gx-hhi-sysctrl
          - amlogic,meson-gx-ao-sysctrl
          - amlogic,meson-axg-hhi-sysctrl
          - amlogic,meson-axg-ao-sysctrl
      - const: simple-mfd
      - const: syscon

  reg:
    maxItems: 1

  clock-controller:
    type: object

  power-controller:
    $ref: /schemas/power/amlogic,meson-ee-pwrc.yaml

  pinctrl:
    type: object

  phy:
    type: object

allOf:
  - if:
      properties:
        compatible:
          enum:
            - amlogic,meson-hhi-sysctrl
    then:
      properties:
        clock-controller:
          $ref: /schemas/clock/amlogic,meson8-clkc.yaml#

        pinctrl: false
        phy: false

  - if:
      properties:
        compatible:
          enum:
            - amlogic,meson-gx-hhi-sysctrl
            - amlogic,meson-axg-hhi-sysctrl
    then:
      properties:
        clock-controller:
          $ref: /schemas/clock/amlogic,gxbb-clkc.yaml#

      required:
        - power-controller

  - if:
      properties:
        compatible:
          enum:
            - amlogic,meson-gx-ao-sysctrl
            - amlogic,meson-axg-ao-sysctrl
    then:
      properties:
        clock-controller:
          $ref: /schemas/clock/amlogic,gxbb-aoclkc.yaml#

        power-controller: false
        phy: false

  - if:
      properties:
        compatible:
          enum:
            - amlogic,meson-gx-hhi-sysctrl
    then:
      properties:
        phy: false

  - if:
      properties:
        compatible:
          enum:
            - amlogic,meson-axg-hhi-sysctrl
    then:
      properties:
        phy:
          oneOf:
            - $ref: /schemas/phy/amlogic,g12a-mipi-dphy-analog.yaml
            - $ref: /schemas/phy/amlogic,meson-axg-mipi-pcie-analog.yaml

required:
  - compatible
  - reg
  - clock-controller

additionalProperties: false

examples:
  - |
    bus@c883c000 {
        compatible = "simple-bus";
        reg = <0xc883c000 0x2000>;
        #address-cells = <1>;
        #size-cells = <1>;
        ranges = <0x0 0xc883c000 0x2000>;

        sysctrl: system-controller@0 {
            compatible = "amlogic,meson-gx-hhi-sysctrl", "simple-mfd", "syscon";
            reg = <0 0x400>;

            clock-controller {
                compatible = "amlogic,gxbb-clkc";
                #clock-cells = <1>;
                clocks = <&xtal>;
                clock-names = "xtal";
            };

            power-controller {
                compatible = "amlogic,meson-gxbb-pwrc";
                #power-domain-cells = <1>;
                amlogic,ao-sysctrl = <&sysctrl_AO>;

                resets = <&reset_viu>,
                         <&reset_venc>,
                         <&reset_vcbus>,
                         <&reset_bt656>,
                         <&reset_dvin>,
                         <&reset_rdma>,
                         <&reset_venci>,
                         <&reset_vencp>,
                         <&reset_vdac>,
                         <&reset_vdi6>,
                         <&reset_vencl>,
                         <&reset_vid_lock>;
                reset-names = "viu", "venc", "vcbus", "bt656", "dvin",
                              "rdma", "venci", "vencp", "vdac", "vdi6",
                              "vencl", "vid_lock";
                clocks = <&clk_vpu>, <&clk_vapb>;
                clock-names = "vpu", "vapb";
            };
        };
    };

    bus@c8100000 {
        compatible = "simple-bus";
        reg = <0xc8100000 0x100000>;
        #address-cells = <1>;
        #size-cells = <1>;
        ranges = <0x0 0xc8100000 0x100000>;

        sysctrl_AO: system-controller@0 {
            compatible = "amlogic,meson-gx-ao-sysctrl", "simple-mfd", "syscon";
            reg = <0 0x100>;

            clock-controller {
                compatible = "amlogic,meson-gxbb-aoclkc", "amlogic,meson-gx-aoclkc";
                #clock-cells = <1>;
                #reset-cells = <1>;
                clocks = <&xtal>, <&clk81>;
                clock-names = "xtal", "mpeg-clk";
            };
        };
    };

  - |
    system-controller@ff63c000 {
        compatible = "amlogic,meson-axg-hhi-sysctrl", "simple-mfd", "syscon";
        reg = <0xff63c000 0x400>;

        clock-controller {
            compatible = "amlogic,axg-clkc";
            #clock-cells = <1>;
            clocks = <&xtal>;
            clock-names = "xtal";
        };

        power-controller {
           compatible = "amlogic,meson-axg-pwrc";
           #power-domain-cells = <1>;
           amlogic,ao-sysctrl = <&sysctrl_AO>;

           resets = <&reset_viu>,
                    <&reset_venc>,
                    <&reset_vcbus>,
                    <&reset_vencl>,
                    <&reset_vid_lock>;
           reset-names = "viu", "venc", "vcbus", "vencl", "vid_lock";
           clocks = <&clk_vpu>, <&clk_vapb>;
           clock-names = "vpu", "vapb";
        };

        phy {
           compatible = "amlogic,axg-mipi-pcie-analog-phy";
           #phy-cells = <0>;
        };
    };
