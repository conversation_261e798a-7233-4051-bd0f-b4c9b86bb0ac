# SPDX-License-Identifier: (GPL-2.0-only OR BSD-2-Clause)
# Copyright (C) 2020 Texas Instruments Incorporated - http://www.ti.com/
%YAML 1.2
---
$id: http://devicetree.org/schemas/soc/ti/ti,j721e-system-controller.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: TI J721e System Controller Registers R/W

description: |
  This represents the Control Module registers (CTRL_MMR0) on the SoC.
  System controller node represents a register region containing a set
  of miscellaneous registers. The registers are not cohesive enough to
  represent as any specific type of device. The typical use-case is
  for some other node's driver, or platform-specific code, to acquire
  a reference to the syscon node (e.g. by phandle, node path, or
  search using a specific compatible value), interrogate the node (or
  associated OS driver) to determine the location of the registers,
  and access the registers directly.

maintainers:
  - <PERSON><PERSON><PERSON> <<EMAIL>>
  - <PERSON> <<EMAIL>>

properties:
  compatible:
    items:
      - enum:
          - ti,am654-system-controller
          - ti,j7200-system-controller
          - ti,j721e-system-controller
          - ti,j721s2-system-controller
      - const: syscon
      - const: simple-mfd

  reg:
    maxItems: 1

  "#address-cells":
    const: 1

  "#size-cells":
    const: 1

  ranges: true

patternProperties:
  # Optional children
  "^mux-controller@[0-9a-f]+$":
    type: object
    description:
      This is the SERDES lane control mux.

  "^clock-controller@[0-9a-f]+$":
    type: object
    $ref: /schemas/clock/ti,am654-ehrpwm-tbclk.yaml#
    description:
      Clock provider for TI EHRPWM nodes.

  "phy@[0-9a-f]+$":
    type: object
    $ref: /schemas/phy/ti,phy-gmii-sel.yaml#
    description:
      The phy node corresponding to the ethernet MAC.

  "^chipid@[0-9a-f]+$":
    type: object
    $ref: /schemas/hwinfo/ti,k3-socinfo.yaml#
    description:
      The node corresponding to SoC chip identification.

  "^pcie-ctrl@[0-9a-f]+$":
    type: object
    description:
      The node corresponding to PCIe control register.

  "^clock@[0-9a-f]+$":
    type: object
    $ref: /schemas/soc/ti/ti,am654-serdes-ctrl.yaml#
    description:
      This is the Serdes Control region.

  "^dss-oldi-io-ctrl@[0-9a-f]+$":
    type: object
    $ref: /schemas/mfd/syscon.yaml#
    description:
      This is the DSS OLDI CTRL region.

required:
  - compatible
  - reg
  - "#address-cells"
  - "#size-cells"
  - ranges

additionalProperties: false

examples:
  - |
    scm_conf: scm-conf@100000 {
        compatible = "ti,j721e-system-controller", "syscon", "simple-mfd";
        reg = <0x00100000 0x1c000>;
        #address-cells = <1>;
        #size-cells = <1>;
        ranges;

        serdes_ln_ctrl: mux-controller@4080 {
            compatible = "mmio-mux";
            reg = <0x00004080 0x50>;

            #mux-control-cells = <1>;
            mux-reg-masks =
                <0x4080 0x3>, <0x4084 0x3>, /* SERDES0 lane0/1 select */
                <0x4090 0x3>, <0x4094 0x3>, /* SERDES1 lane0/1 select */
                <0x40a0 0x3>, <0x40a4 0x3>, /* SERDES2 lane0/1 select */
                <0x40b0 0x3>, <0x40b4 0x3>, /* SERDES3 lane0/1 select */
                <0x40c0 0x3>, <0x40c4 0x3>, <0x40c8 0x3>, <0x40cc 0x3>;
                /* SERDES4 lane0/1/2/3 select */
        };

        clock-controller@4140 {
            compatible = "ti,am654-ehrpwm-tbclk";
            reg = <0x4140 0x18>;
            #clock-cells = <1>;
        };

        chipid@14 {
            compatible = "ti,am654-chipid";
            reg = <0x14 0x4>;
        };

        pcie0_ctrl: pcie-ctrl@4070 {
            compatible = "ti,j784s4-pcie-ctrl", "syscon";
            reg = <0x4070 0x4>;
        };
    };
...
