# SPDX-License-Identifier: (GPL-2.0-only OR BSD-2-Clause)
%YAML 1.2
---
$id: http://devicetree.org/schemas/soc/altera/altr,sys-mgr.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: Altera SOCFPGA System Manager

maintainers:
  - <PERSON><PERSON> <<EMAIL>>

properties:
  compatible:
    oneOf:
      - description: Cyclone5/Arria5/Arria10
        const: altr,sys-mgr
      - description: Stratix10 SoC
        items:
          - const: altr,sys-mgr-s10
          - const: altr,sys-mgr

  reg:
    maxItems: 1

  cpu1-start-addr:
    $ref: /schemas/types.yaml#/definitions/uint32
    description: CPU1 start address in hex

required:
  - compatible
  - reg

allOf:
  - if:
      properties:
        compatible:
          contains:
            const: altr,sys-mgr-s10
    then:
      properties:
        cpu1-start-addr: false

additionalProperties: false

examples:
  - |
    sysmgr@ffd08000 {
      compatible = "altr,sys-mgr";
      reg = <0xffd08000 0x1000>;
      cpu1-start-addr = <0xffd080c4>;
    };
