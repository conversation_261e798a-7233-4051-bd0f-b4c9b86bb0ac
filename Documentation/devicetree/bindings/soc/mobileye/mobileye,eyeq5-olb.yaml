# SPDX-License-Identifier: (GPL-2.0 OR BSD-2-Clause)
%YAML 1.2
---
$id: http://devicetree.org/schemas/soc/mobileye/mobileye,eyeq5-olb.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: Mobileye EyeQ SoC system controller

maintainers:
  - <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>
  - <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>
  - <PERSON> <<EMAIL>>

description:
  OLB ("Other Logic Block") is a hardware block grouping smaller blocks. Clocks,
  resets, pinctrl are being handled from here. EyeQ5 and EyeQ6L host a single
  instance. EyeQ6H hosts seven instances.

properties:
  compatible:
    items:
      - enum:
          - mobileye,eyeq5-olb
          - mobileye,eyeq6l-olb
          - mobileye,eyeq6h-acc-olb
          - mobileye,eyeq6h-central-olb
          - mobileye,eyeq6h-east-olb
          - mobileye,eyeq6h-west-olb
          - mobileye,eyeq6h-south-olb
          - mobileye,eyeq6h-ddr0-olb
          - mobileye,eyeq6h-ddr1-olb
      - const: syscon

  reg:
    maxItems: 1

  '#reset-cells':
    description:
      First cell is domain and optional if compatible has a single reset domain.
      Second cell is reset index inside that domain.
    enum: [ 1, 2 ]

  '#clock-cells':
    const: 1

  clocks:
    maxItems: 1
    description:
      Input parent clock to all PLLs. Expected to be the main crystal.

  clock-names:
    const: ref

patternProperties:
  '-pins?$':
    type: object
    description: Pin muxing configuration.
    $ref: /schemas/pinctrl/pinmux-node.yaml#
    additionalProperties: false
    properties:
      pins: true
      function:
        enum: [gpio,
               # Bank A
               timer0, timer1, timer2, timer5, uart0, uart1, can0, can1, spi0,
               spi1, refclk0,
               # Bank B
               timer3, timer4, timer6, uart2, can2, spi2, spi3, mclk0]
      bias-disable: true
      bias-pull-down: true
      bias-pull-up: true
      drive-strength: true
    required:
      - pins
      - function
    allOf:
      - if:
          properties:
            function:
              const: gpio
        then:
          properties:
            pins:
              items: # PA0 - PA28, PB0 - PB22
                pattern: '^(P(A|B)1?[0-9]|PA2[0-8]|PB2[0-2])$'
      - if:
          properties:
            function:
              const: timer0
        then:
          properties:
            pins:
              items:
                enum: [PA0, PA1]
      - if:
          properties:
            function:
              const: timer1
        then:
          properties:
            pins:
              items:
                enum: [PA2, PA3]
      - if:
          properties:
            function:
              const: timer2
        then:
          properties:
            pins:
              items:
                enum: [PA4, PA5]
      - if:
          properties:
            function:
              const: timer5
        then:
          properties:
            pins:
              items:
                enum: [PA6, PA7, PA8, PA9]
      - if:
          properties:
            function:
              const: uart0
        then:
          properties:
            pins:
              items:
                enum: [PA10, PA11]
      - if:
          properties:
            function:
              const: uart1
        then:
          properties:
            pins:
              items:
                enum: [PA12, PA13]
      - if:
          properties:
            function:
              const: can0
        then:
          properties:
            pins:
              items:
                enum: [PA14, PA15]
      - if:
          properties:
            function:
              const: can1
        then:
          properties:
            pins:
              items:
                enum: [PA16, PA17]
      - if:
          properties:
            function:
              const: spi0
        then:
          properties:
            pins:
              items:
                enum: [PA18, PA19, PA20, PA21, PA22]
      - if:
          properties:
            function:
              const: spi1
        then:
          properties:
            pins:
              items:
                enum: [PA23, PA24, PA25, PA26, PA27]
      - if:
          properties:
            function:
              const: refclk0
        then:
          properties:
            pins:
              items:
                enum: [PA28]
      - if:
          properties:
            function:
              const: timer3
        then:
          properties:
            pins:
              items:
                enum: [PB0, PB1]
      - if:
          properties:
            function:
              const: timer4
        then:
          properties:
            pins:
              items:
                enum: [PB2, PB3]
      - if:
          properties:
            function:
              const: timer6
        then:
          properties:
            pins:
              items:
                enum: [PB4, PB5, PB6, PB7]
      - if:
          properties:
            function:
              const: uart2
        then:
          properties:
            pins:
              items:
                enum: [PB8, PB9]
      - if:
          properties:
            function:
              const: can2
        then:
          properties:
            pins:
              items:
                enum: [PB10, PB11]
      - if:
          properties:
            function:
              const: spi2
        then:
          properties:
            pins:
              items:
                enum: [PB12, PB13, PB14, PB15, PB16]
      - if:
          properties:
            function:
              const: spi3
        then:
          properties:
            pins:
              items:
                enum: [PB17, PB18, PB19, PB20, PB21]
      - if:
          properties:
            function:
              const: mclk0
        then:
          properties:
            pins:
              items:
                enum: [PB22]

required:
  - compatible
  - reg
  - '#clock-cells'
  - clocks
  - clock-names

additionalProperties: false

allOf:
    # Compatibles exposing a single reset domain.
  - if:
      properties:
        compatible:
          contains:
            enum:
              - mobileye,eyeq6h-acc-olb
              - mobileye,eyeq6h-east-olb
              - mobileye,eyeq6h-west-olb
    then:
      properties:
        '#reset-cells':
          const: 1
      required:
        - '#reset-cells'

    # Compatibles exposing two reset domains.
  - if:
      properties:
        compatible:
          contains:
            enum:
              - mobileye,eyeq5-olb
              - mobileye,eyeq6l-olb
    then:
      properties:
        '#reset-cells':
          const: 2
      required:
        - '#reset-cells'

    # Compatibles not exposing resets.
  - if:
      properties:
        compatible:
          contains:
            enum:
              - mobileye,eyeq6h-central-olb
              - mobileye,eyeq6h-south-olb
              - mobileye,eyeq6h-ddr0-olb
              - mobileye,eyeq6h-ddr1-olb
    then:
      properties:
        '#reset-cells': false

    # Only EyeQ5 has pinctrl in OLB.
  - if:
      not:
        properties:
          compatible:
            contains:
              const: mobileye,eyeq5-olb
    then:
      patternProperties:
        '-pins?$': false

examples:
  - |
    soc {
      #address-cells = <2>;
      #size-cells = <2>;

      system-controller@e00000 {
        compatible = "mobileye,eyeq5-olb", "syscon";
        reg = <0 0xe00000 0x0 0x400>;
        #reset-cells = <2>;
        #clock-cells = <1>;
        clocks = <&xtal>;
        clock-names = "ref";
      };
    };
  - |
    soc {
      #address-cells = <2>;
      #size-cells = <2>;

      system-controller@d2003000 {
        compatible = "mobileye,eyeq6h-acc-olb", "syscon";
        reg = <0x0 0xd2003000 0x0 0x1000>;
        #reset-cells = <1>;
        #clock-cells = <1>;
        clocks = <&xtal>;
        clock-names = "ref";
      };
    };
