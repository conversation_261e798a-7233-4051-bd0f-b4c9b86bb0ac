# SPDX-License-Identifier: (GPL-2.0 OR BSD-2-Clause)
%YAML 1.2
---
$id: http://devicetree.org/schemas/soc/sophgo/sophgo,sg2044-top-syscon.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: Sophgo SG2044 SoC TOP system controller

maintainers:
  - <PERSON><PERSON><PERSON> <<EMAIL>>

description:
  The Sophgo SG2044 TOP system controller is a hardware block grouping
  multiple small functions, such as clocks and some other internal
  function.

properties:
  compatible:
    items:
      - const: sophgo,sg2044-top-syscon
      - const: syscon

  reg:
    maxItems: 1

  clocks:
    maxItems: 1

  '#clock-cells':
    const: 1
    description:
      See <dt-bindings/clock/sophgo,sg2044-pll.h> for valid clock.

required:
  - compatible
  - reg
  - clocks
  - '#clock-cells'

additionalProperties: false

examples:
  - |
    syscon@50000000 {
      compatible = "sophgo,sg2044-top-syscon", "syscon";
      reg = <0x50000000 0x1000>;
      #clock-cells = <1>;
      clocks = <&osc>;
    };
