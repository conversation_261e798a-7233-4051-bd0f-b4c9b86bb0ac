# SPDX-License-Identifier: (GPL-2.0-only OR BSD-2-Clause)
%YAML 1.2
---
$id: http://devicetree.org/schemas/sophgo/sophgo,cv1800b-rtc.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: Real Time Clock of the Sophgo CV1800 SoC

description:
  The RTC (Real Time Clock) is an independently powered module in the chip. It
  contains a 32KHz oscillator and a Power-On-Reset (POR) sub-module, which can
  be used for time display and scheduled alarm produce. In addition, the
  hardware state machine provides triggering and timing control for chip
  power-on, power-off and reset.

  Furthermore, the 8051 subsystem is located within RTCSYS and is independently
  powered. System software can use the 8051 to manage wake conditions and wake
  the system while the system is asleep, and communicate with external devices
  through peripheral controllers.

  Technical Reference Manual available at
    https://github.com/sophgo/sophgo-doc/tree/main/SG200X/TRM

maintainers:
  - <EMAIL>

allOf:
  - $ref: /schemas/rtc/rtc.yaml#

properties:
  compatible:
    items:
      - const: sophgo,cv1800b-rtc
      - const: syscon

  reg:
    maxItems: 1

  interrupts:
    items:
      - description: RTC Alarm
      - description: RTC Longpress
      - description: VBAT DET

  interrupt-names:
    items:
      - const: alarm
      - const: longpress
      - const: vbat

  clocks:
    items:
      - description: RTC clock source
      - description: DW8051 MCU clock source

  clock-names:
    items:
      - const: rtc
      - const: mcu

required:
  - compatible
  - reg
  - interrupts
  - interrupt-names
  - clocks
  - clock-names

unevaluatedProperties: false

examples:
  - |
    #include <dt-bindings/clock/sophgo,cv1800.h>
    #include <dt-bindings/interrupt-controller/irq.h>

    rtc@5025000 {
        compatible = "sophgo,cv1800b-rtc", "syscon";
        reg = <0x5025000 0x2000>;
        interrupts = <17 IRQ_TYPE_LEVEL_HIGH>,
                     <18 IRQ_TYPE_LEVEL_HIGH>,
                     <19 IRQ_TYPE_LEVEL_HIGH>;
        interrupt-names = "alarm", "longpress", "vbat";
        clocks = <&clk CLK_RTC_25M>,
                 <&clk CLK_SRC_RTC_SYS_0>;
        clock-names = "rtc", "mcu";
    };
