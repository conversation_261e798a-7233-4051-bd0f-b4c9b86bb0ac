# SPDX-License-Identifier: (GPL-2.0 OR BSD-2-Clause)
%YAML 1.2
---
$id: http://devicetree.org/schemas/soc/google/google,gs101-pmu-intr-gen.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: Google Power Management Unit (PMU) Interrupt Generation

description: |
  PMU interrupt generator for handshaking between PMU through interrupts.

maintainers:
  - <PERSON> <<EMAIL>>

properties:
  compatible:
    items:
      - const: google,gs101-pmu-intr-gen
      - const: syscon

  reg:
    maxItems: 1

required:
  - compatible
  - reg

additionalProperties: false

examples:
  - |
    pmu_intr_gen: syscon@17470000 {
      compatible = "google,gs101-pmu-intr-gen", "syscon";
      reg = <0x17470000 0x10000>;
    };
