# SPDX-License-Identifier: GPL-2.0
%YAML 1.2
---
$id: http://devicetree.org/schemas/soc/samsung/exynos-pmu.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: Samsung Exynos SoC series Power Management Unit (PMU)

maintainers:
  - <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> <<EMAIL>>

# Custom select to avoid matching all nodes with 'syscon'
select:
  properties:
    compatible:
      contains:
        enum:
          - google,gs101-pmu
          - samsung,exynos3250-pmu
          - samsung,exynos4210-pmu
          - samsung,exynos4212-pmu
          - samsung,exynos4412-pmu
          - samsung,exynos5250-pmu
          - samsung,exynos5260-pmu
          - samsung,exynos5410-pmu
          - samsung,exynos5420-pmu
          - samsung,exynos5433-pmu
          - samsung,exynos7-pmu
          - samsung,exynos850-pmu
          - samsung-s5pv210-pmu
  required:
    - compatible

properties:
  compatible:
    oneOf:
      - items:
          - enum:
              - google,gs101-pmu
              - samsung,exynos3250-pmu
              - samsung,exynos4210-pmu
              - samsung,exynos4212-pmu
              - samsung,exynos4412-pmu
              - samsung,exynos5250-pmu
              - samsung,exynos5260-pmu
              - samsung,exynos5410-pmu
              - samsung,exynos5420-pmu
              - samsung,exynos5433-pmu
              - samsung,exynos7-pmu
              - samsung,exynos850-pmu
              - samsung-s5pv210-pmu
          - const: syscon
      - items:
          - enum:
              - samsung,exynos2200-pmu
              - samsung,exynos7870-pmu
              - samsung,exynos7885-pmu
              - samsung,exynos8895-pmu
              - samsung,exynos9810-pmu
              - samsung,exynos990-pmu
              - samsung,exynosautov9-pmu
              - samsung,exynosautov920-pmu
              - tesla,fsd-pmu
          - const: samsung,exynos7-pmu
          - const: syscon
      - items:
          - enum:
              - samsung,exynos3250-pmu
              - samsung,exynos4210-pmu
              - samsung,exynos4212-pmu
              - samsung,exynos4412-pmu
              - samsung,exynos5250-pmu
              - samsung,exynos5420-pmu
              - samsung,exynos5433-pmu
          - const: simple-mfd
          - const: syscon

  reg:
    maxItems: 1

  '#clock-cells':
    const: 1

  clock-names:
    description:
      List of clock names for particular CLKOUT mux inputs
    minItems: 1
    maxItems: 32
    items:
      pattern: '^clkout([0-9]|[12][0-9]|3[0-1])$'

  clocks:
    minItems: 1
    maxItems: 32

  dp-phy:
    $ref: /schemas/phy/samsung,dp-video-phy.yaml
    unevaluatedProperties: false

  interrupt-controller:
    description:
      Some PMUs are capable of behaving as an interrupt controller (mostly
      to wake up a suspended PMU).

  '#interrupt-cells':
    description:
      Must be identical to the that of the parent interrupt controller.
    const: 3

  mipi-phy:
    $ref: /schemas/phy/samsung,mipi-video-phy.yaml
    unevaluatedProperties: false

  reboot-mode:
    $ref: /schemas/power/reset/syscon-reboot-mode.yaml
    type: object
    description:
      Reboot mode to alter bootloader behavior for the next boot

  syscon-poweroff:
    $ref: /schemas/power/reset/syscon-poweroff.yaml#
    type: object
    description:
      Node for power off method

  syscon-reboot:
    $ref: /schemas/power/reset/syscon-reboot.yaml#
    type: object
    description:
      Node for reboot method

  google,pmu-intr-gen-syscon:
    $ref: /schemas/types.yaml#/definitions/phandle
    description:
      Phandle to PMU interrupt generation interface.

required:
  - compatible
  - reg

additionalProperties: false

allOf:
  - if:
      properties:
        compatible:
          contains:
            enum:
              - samsung,exynos3250-pmu
              - samsung,exynos4210-pmu
              - samsung,exynos4212-pmu
              - samsung,exynos4412-pmu
              - samsung,exynos5250-pmu
              - samsung,exynos5410-pmu
              - samsung,exynos5420-pmu
              - samsung,exynos5433-pmu
    then:
      required:
        - '#clock-cells'
        - clock-names
        - clocks

  - if:
      properties:
        compatible:
          contains:
            enum:
              - samsung,exynos3250-pmu
              - samsung,exynos4210-pmu
              - samsung,exynos4212-pmu
              - samsung,exynos4412-pmu
              - samsung,exynos5250-pmu
              - samsung,exynos5420-pmu
              - samsung,exynos5433-pmu
    then:
      properties:
        mipi-phy: true
    else:
      properties:
        mipi-phy: false

  - if:
      properties:
        compatible:
          contains:
            enum:
              - samsung,exynos5250-pmu
              - samsung,exynos5420-pmu
              - samsung,exynos5433-pmu
    then:
      properties:
        dp-phy: true
    else:
      properties:
        dp-phy: false

  - if:
      properties:
        compatible:
          contains:
            enum:
              - google,gs101-pmu
    then:
      required:
        - google,pmu-intr-gen-syscon

examples:
  - |
    #include <dt-bindings/clock/exynos5250.h>

    pmu_system_controller: system-controller@10040000 {
        compatible = "samsung,exynos5250-pmu", "syscon";
        reg = <0x10040000 0x5000>;
        interrupt-controller;
        #interrupt-cells = <3>;
        interrupt-parent = <&gic>;
        #clock-cells = <1>;
        clock-names = "clkout16";
        clocks = <&clock CLK_FIN_PLL>;

        dp-phy {
            compatible = "samsung,exynos5250-dp-video-phy";
            #phy-cells = <0>;
        };

        mipi-phy {
            compatible = "samsung,s5pv210-mipi-video-phy";
            #phy-cells = <1>;
        };
    };
