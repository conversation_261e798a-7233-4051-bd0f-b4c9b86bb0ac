# SPDX-License-Identifier: (GPL-2.0-only OR BSD-2-Clause)
%YAML 1.2
---
$id: http://devicetree.org/schemas/soc/samsung/exynos-usi.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: Samsung's Exynos USI (Universal Serial Interface)

maintainers:
  - <PERSON> <<EMAIL>>
  - <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> <<EMAIL>>

description: |
  The USI IP-core provides configurable support for serial protocols, enabling
  different serial communication modes depending on the version.

  In USIv1, configurations are available to enable either one or two protocols
  simultaneously in select combinations - High-Speed I2C0, High-Speed
  I2C1, SPI, UART, High-Speed I2C0 and I2C1 or both High-Speed
  I2C1 and UART.

  In USIv2, only one protocol can be active at a time, either UART, SPI, or
  High-Speed I2C.

  The USI core shares internal circuits across protocols, meaning only the
  selected configuration is active at any given time. USI is modeled as a node
  with zero or more child nodes, each representing a serial sub-node device. The
  mode setting selects which particular function will be used.

properties:
  $nodename:
    pattern: "^usi@[0-9a-f]+$"

  compatible:
    oneOf:
      - items:
          - enum:
              - google,gs101-usi
              - samsung,exynosautov9-usi
              - samsung,exynosautov920-usi
          - const: samsung,exynos850-usi
      - enum:
          - samsung,exynos850-usi
          - samsung,exynos8895-usi

  reg:
    maxItems: 1

  clocks:
    maxItems: 2

  clock-names:
    items:
      - const: pclk
      - const: ipclk

  ranges: true

  "#address-cells":
    const: 1

  "#size-cells":
    const: 1

  samsung,sysreg:
    $ref: /schemas/types.yaml#/definitions/phandle-array
    items:
      - items:
          - description: phandle to System Register syscon node
          - description: offset of SW_CONF register for this USI controller
    description:
      Should be phandle/offset pair. The phandle to System Register syscon node
      (for the same domain where this USI controller resides) and the offset
      of SW_CONF register for this USI controller.

  samsung,mode:
    $ref: /schemas/types.yaml#/definitions/uint32
    enum: [0, 1, 2, 3, 4, 5, 6]
    description:
      Selects USI function (which serial protocol to use). Refer to
      <include/dt-bindings/soc/samsung,exynos-usi.h> for valid USI mode values.

  samsung,clkreq-on:
    type: boolean
    description:
      Enable this property if underlying protocol requires the clock to be
      continuously provided without automatic gating. As suggested by SoC
      manual, it should be set in case of SPI/I2C slave, UART Rx and I2C
      multi-master mode. Usually this property is needed if USI mode is set
      to "UART".

      This property is optional.

patternProperties:
  "^i2c@[0-9a-f]+$":
    $ref: /schemas/i2c/i2c-exynos5.yaml
    description: Child node describing underlying I2C

  "^serial@[0-9a-f]+$":
    $ref: /schemas/serial/samsung_uart.yaml
    description: Child node describing underlying UART/serial

  "^spi@[0-9a-f]+$":
    $ref: /schemas/spi/samsung,spi.yaml
    description: Child node describing underlying SPI

required:
  - compatible
  - ranges
  - "#address-cells"
  - "#size-cells"
  - samsung,sysreg
  - samsung,mode

allOf:
  - if:
      properties:
        compatible:
          contains:
            enum:
              - samsung,exynos850-usi

    then:
      properties:
        reg:
          maxItems: 1

        clocks:
          items:
            - description: Bus (APB) clock
            - description: Operating clock for UART/SPI/I2C protocol

        clock-names:
          maxItems: 2

        samsung,mode:
          enum: [0, 1, 2, 3]

      required:
        - reg
        - clocks
        - clock-names

  - if:
      properties:
        compatible:
          contains:
            enum:
              - samsung,exynos8895-usi

    then:
      properties:
        reg: false

        clocks:
          items:
            - description: Bus (APB) clock
            - description: Operating clock for UART/SPI protocol

        clock-names:
          maxItems: 2

        samsung,clkreq-on: false

      required:
        - clocks
        - clock-names

additionalProperties: false

examples:
  - |
    #include <dt-bindings/interrupt-controller/arm-gic.h>
    #include <dt-bindings/soc/samsung,exynos-usi.h>

    usi0: usi@138200c0 {
        compatible = "samsung,exynos850-usi";
        reg = <0x138200c0 0x20>;
        samsung,sysreg = <&sysreg_peri 0x1010>;
        samsung,mode = <USI_MODE_UART>;
        samsung,clkreq-on; /* needed for UART mode */
        #address-cells = <1>;
        #size-cells = <1>;
        ranges;
        clocks = <&cmu_peri 32>, <&cmu_peri 31>;
        clock-names = "pclk", "ipclk";

        serial_0: serial@13820000 {
            compatible = "samsung,exynos850-uart";
            reg = <0x13820000 0xc0>;
            interrupts = <GIC_SPI 227 IRQ_TYPE_LEVEL_HIGH>;
            clocks = <&cmu_peri 32>, <&cmu_peri 31>;
            clock-names = "uart", "clk_uart_baud0";
        };

        hsi2c_0: i2c@13820000 {
            compatible = "samsung,exynos850-hsi2c", "samsung,exynosautov9-hsi2c";
            reg = <0x13820000 0xc0>;
            interrupts = <GIC_SPI 227 IRQ_TYPE_LEVEL_HIGH>;
            #address-cells = <1>;
            #size-cells = <0>;
            clocks = <&cmu_peri 31>, <&cmu_peri 32>;
            clock-names = "hsi2c", "hsi2c_pclk";
            status = "disabled";
        };
    };
