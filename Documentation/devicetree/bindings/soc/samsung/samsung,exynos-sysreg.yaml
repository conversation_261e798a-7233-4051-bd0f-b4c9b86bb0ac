# SPDX-License-Identifier: GPL-2.0-only OR BSD-2-Clause
%YAML 1.2
---
$id: http://devicetree.org/schemas/soc/samsung/samsung,exynos-sysreg.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: Samsung Exynos SoC series System Registers (SYSREG)

maintainers:
  - <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> <<EMAIL>>

properties:
  compatible:
    oneOf:
      - items:
          - enum:
              - google,gs101-apm-sysreg
              - google,gs101-hsi2-sysreg
              - google,gs101-peric0-sysreg
              - google,gs101-peric1-sysreg
              - samsung,exynos2200-cmgp-sysreg
              - samsung,exynos2200-peric0-sysreg
              - samsung,exynos2200-peric1-sysreg
              - samsung,exynos2200-peric2-sysreg
              - samsung,exynos2200-ufs-sysreg
              - samsung,exynos3-sysreg
              - samsung,exynos4-sysreg
              - samsung,exynos5-sysreg
              - samsung,exynos8895-fsys0-sysreg
              - samsung,exynos8895-fsys1-sysreg
              - samsung,exynos8895-peric0-sysreg
              - samsung,exynos8895-peric1-sysreg
              - samsung,exynosautov920-peric0-sysreg
              - samsung,exynosautov920-peric1-sysreg
              - tesla,fsd-cam-sysreg
              - tesla,fsd-fsys0-sysreg
              - tesla,fsd-fsys1-sysreg
              - tesla,fsd-peric-sysreg
          - const: syscon
      - items:
          - enum:
              - samsung,exynos5433-cam0-sysreg
              - samsung,exynos5433-cam1-sysreg
              - samsung,exynos5433-disp-sysreg
              - samsung,exynos5433-fsys-sysreg
          - const: samsung,exynos5433-sysreg
          - const: syscon
      - items:
          - enum:
              - samsung,exynos5433-sysreg
              - samsung,exynos850-sysreg
              - samsung,exynosautov9-sysreg
          - const: syscon
        deprecated: true
      - items:
          - enum:
              - samsung,exynos850-cmgp-sysreg
              - samsung,exynos850-peri-sysreg
          - const: samsung,exynos850-sysreg
          - const: syscon
      - items:
          - enum:
              - samsung,exynosautov9-fsys2-sysreg
              - samsung,exynosautov9-peric0-sysreg
              - samsung,exynosautov9-peric1-sysreg
          - const: samsung,exynosautov9-sysreg
          - const: syscon

  reg:
    maxItems: 1

  clocks:
    maxItems: 1

required:
  - compatible
  - reg

allOf:
  - if:
      properties:
        compatible:
          contains:
            enum:
              - google,gs101-hsi2-sysreg
              - google,gs101-peric0-sysreg
              - google,gs101-peric1-sysreg
              - samsung,exynos850-cmgp-sysreg
              - samsung,exynos850-peri-sysreg
              - samsung,exynos850-sysreg
              - samsung,exynos8895-fsys0-sysreg
              - samsung,exynos8895-fsys1-sysreg
              - samsung,exynos8895-peric0-sysreg
              - samsung,exynos8895-peric1-sysreg
    then:
      required:
        - clocks
    else:
      properties:
        clocks: false

additionalProperties: false

examples:
  - |
    system-controller@10010000 {
        compatible = "samsung,exynos4-sysreg", "syscon";
        reg = <0x10010000 0x400>;
    };
