# SPDX-License-Identifier: (GPL-2.0 OR BSD-2-Clause)
%YAML 1.2
---
$id: http://devicetree.org/schemas/arm/stm32/st,stm32-syscon.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: STMicroelectronics STM32 Platforms System Controller

maintainers:
  - <PERSON> <<EMAIL>>
  - <PERSON> <<EMAIL>>

properties:
  compatible:
    oneOf:
      - items:
          - enum:
              - st,stm32-power-config
              - st,stm32-syscfg
              - st,stm32-tamp
              - st,stm32f4-gcan
              - st,stm32mp151-pwr-mcu
              - st,stm32mp157-syscfg
              - st,stm32mp21-syscfg
              - st,stm32mp23-syscfg
              - st,stm32mp25-syscfg
          - const: syscon
      - items:
          - const: st,stm32-tamp
          - const: syscon
          - const: simple-mfd

  reg:
    maxItems: 1

  clocks:
    maxItems: 1

required:
  - compatible
  - reg

if:
  properties:
    compatible:
      contains:
        enum:
          - st,stm32mp157-syscfg
          - st,stm32f4-gcan
then:
  required:
    - clocks

additionalProperties: false

examples:
  - |
    #include <dt-bindings/clock/stm32mp1-clks.h>
    syscfg: syscon@50020000 {
        compatible = "st,stm32mp157-syscfg", "syscon";
        reg = <0x50020000 0x400>;
        clocks = <&rcc SYSCFG>;
    };

...
