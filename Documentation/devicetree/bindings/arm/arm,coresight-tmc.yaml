# SPDX-License-Identifier: GPL-2.0-only OR BSD-2-Clause
%YAML 1.2
---
$id: http://devicetree.org/schemas/arm/arm,coresight-tmc.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: Arm CoreSight Trace Memory Controller

maintainers:
  - <PERSON><PERSON> <<EMAIL>>
  - <PERSON> <<EMAIL>>
  - <PERSON> <<EMAIL>>
  - <PERSON> K Poulose <<EMAIL>>

description: |
  CoreSight components are compliant with the ARM CoreSight architecture
  specification and can be connected in various topologies to suit a particular
  SoCs tracing needs. These trace components can generally be classified as
  sinks, links and sources. Trace data produced by one or more sources flows
  through the intermediate links connecting the source to the currently selected
  sink.

  Trace Memory Controller is used for Embedded Trace Buffer(ETB), Embedded Trace
  FIFO(ETF) and Embedded Trace Router(ETR) configurations. The configuration
  mode (ETB, ETF, ETR) is discovered at boot time when the device is probed.

# Need a custom select here or 'arm,primecell' will match on lots of nodes
select:
  properties:
    compatible:
      contains:
        const: arm,coresight-tmc
  required:
    - compatible

allOf:
  - $ref: /schemas/arm/primecell.yaml#

properties:
  compatible:
    items:
      - const: arm,coresight-tmc
      - const: arm,primecell

  reg:
    maxItems: 1

  clocks:
    minItems: 1
    maxItems: 2

  clock-names:
    minItems: 1
    items:
      - const: apb_pclk
      - const: atclk

  iommus:
    maxItems: 1

  power-domains:
    maxItems: 1

  arm,buffer-size:
    $ref: /schemas/types.yaml#/definitions/uint32
    deprecated: true
    description:
      Size of contiguous buffer space for TMC ETR (embedded trace router). The
      buffer size can be configured dynamically via buffer_size property in
      sysfs instead.

  arm,scatter-gather:
    type: boolean
    description:
      Indicates that the TMC-ETR can safely use the SG mode on this system.

  arm,max-burst-size:
    description:
      The maximum burst size initiated by TMC on the AXI master interface. The
      burst size can be in the range [0..15], the setting supports one data
      transfer per burst up to a maximum of 16 data transfers per burst.
    $ref: /schemas/types.yaml#/definitions/uint32
    maximum: 15

  in-ports:
    $ref: /schemas/graph.yaml#/properties/ports
    additionalProperties: false

    properties:
      port:
        description: Input connection from the CoreSight Trace bus.
        $ref: /schemas/graph.yaml#/properties/port

  out-ports:
    $ref: /schemas/graph.yaml#/properties/ports
    additionalProperties: false

    properties:
      port:
        description: AXI or ATB Master output connection. Used for ETR
          and ETF configurations.
        $ref: /schemas/graph.yaml#/properties/port

  memory-region:
    items:
      - description: Reserved trace buffer memory for ETR and ETF sinks.
          For ETR, this reserved memory region is used for trace data capture.
          Same region is used for trace data retention as well after a panic
          or watchdog reset.
          This reserved memory region is used as trace buffer or used for trace
          data retention only if specifically selected by the user in sysfs
          interface.
          The default memory usage models for ETR in sysfs/perf modes are
          otherwise unaltered.

          For ETF, this reserved memory region is used by default for
          retention of trace data synced from internal SRAM after a panic
          or watchdog reset.
      - description: Reserved meta data memory. Used for ETR and ETF sinks
          for storing metadata.

  memory-region-names:
    items:
      - const: tracedata
      - const: metadata

required:
  - compatible
  - reg
  - clocks
  - clock-names
  - in-ports

unevaluatedProperties: false

examples:
  - |
    etr@20070000 {
        compatible = "arm,coresight-tmc", "arm,primecell";
        reg = <0x20070000 0x1000>;
        memory-region = <&etr_trace_mem_reserved>,
                       <&etr_mdata_mem_reserved>;
        memory-region-names = "tracedata", "metadata";

        clocks = <&oscclk6a>;
        clock-names = "apb_pclk";
        in-ports {
            port {
                etr_in_port: endpoint {
                    remote-endpoint = <&replicator2_out_port0>;
                };
            };
        };

        out-ports {
            port {
                etr_out_port: endpoint {
                    remote-endpoint = <&catu_in_port>;
                };
            };
        };
    };
...
