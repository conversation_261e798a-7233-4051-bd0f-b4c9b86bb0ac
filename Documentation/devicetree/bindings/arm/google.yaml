# SPDX-License-Identifier: GPL-2.0-only OR BSD-2-Clause
%YAML 1.2
---
$id: http://devicetree.org/schemas/arm/google.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: Google Tensor platforms

maintainers:
  - <PERSON> <<EMAIL>>

description: |
  ARM platforms using SoCs designed by Google branded "Tensor" used in Pixel
  devices.

  Currently upstream this is devices using "gs101" SoC which is found in Pixel
  6, Pixel 6 Pro and Pixel 6a.

  Google have a few different names for the SoC:
  - Marketing name ("Tensor")
  - Codename ("Whitechapel")
  - SoC ID ("gs101")
  - Die ID ("S5P9845")

  Likewise there are a couple of names for the actual device
  - Marketing name ("Pixel 6")
  - Codename ("Oriole")

  Devicetrees should use the lowercased SoC ID and lowercased board codename,
  e.g. gs101 and gs101-oriole.

properties:
  $nodename:
    const: '/'
  compatible:
    oneOf:
      - description: Google Pixel 6 or 6 Pro (Oriole or Raven)
        items:
          - enum:
              - google,gs101-oriole
              - google,gs101-raven
          - const: google,gs101

  # Bootloader requires empty ect node to be present
  ect:
    type: object
    additionalProperties: false

required:
  - ect

additionalProperties: true

...
