# SPDX-License-Identifier: GPL-2.0
%YAML 1.2
---
$id: http://devicetree.org/schemas/arm/qcom.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: QCOM

maintainers:
  - <PERSON><PERSON><PERSON> <bjorn.and<PERSON><PERSON>@linaro.org>

description: |
  For devices using the Qualcomm SoC the "compatible" properties consists of
  one or several "manufacturer,model" strings, describing the device itself,
  followed by one or several "qcom,<SoC>" strings, describing the SoC used in
  the device.

  The 'SoC' element must be one of the following strings:

        apq8016
        apq8026
        apq8064
        apq8074
        apq8084
        apq8094
        apq8096
        ipq4018
        ipq4019
        ipq5018
        ipq5332
        ipq5424
        ipq6018
        ipq8064
        ipq8074
        ipq9574
        mdm9615
        msm8226
        msm8660
        msm8916
        msm8917
        msm8926
        msm8929
        msm8939
        msm8953
        msm8956
        msm8960
        msm8974
        msm8974pro
        msm8976
        msm8992
        msm8994
        msm8996
        msm8996pro
        msm8998
        qcs404
        qcs615
        qcs8300
        qcs8550
        qcm2290
        qcm6490
        qcs9100
        qdu1000
        qrb2210
        qrb4210
        qru1000
        sa8155p
        sa8540p
        sa8775p
        sar2130p
        sc7180
        sc7280
        sc8180x
        sc8280xp
        sda660
        sdm450
        sdm630
        sdm632
        sdm636
        sdm660
        sdm670
        sdm845
        sdx55
        sdx65
        sdx75
        sm4250
        sm4450
        sm6115
        sm6115p
        sm6125
        sm6350
        sm6375
        sm7125
        sm7150
        sm7225
        sm7325
        sm8150
        sm8250
        sm8350
        sm8450
        sm8550
        sm8650
        sm8750
        x1e78100
        x1e80100
        x1p42100

  There are many devices in the list below that run the standard ChromeOS
  bootloader setup and use the open source depthcharge bootloader to boot the
  OS. These devices use the bootflow explained at
  https://docs.kernel.org/arch/arm/google/chromebook-boot-flow.html

properties:
  $nodename:
    const: "/"
  compatible:
    oneOf:
      - items:
          - enum:
              - qcom,apq8016-sbc
              - schneider,apq8016-hmibsc
          - const: qcom,apq8016

      - items:
          - enum:
              - asus,sparrow
              - huawei,sturgeon
              - lg,lenok
              - samsung,matisse-wifi
              - samsung,milletwifi
          - const: qcom,apq8026

      - items:
          - enum:
              - asus,nexus7-flo
              - lg,nexus4-mako
              - sony,xperia-yuga
              - qcom,apq8064-cm-qs600
              - qcom,apq8064-ifc6410
          - const: qcom,apq8064

      - items:
          - enum:
              - qcom,apq8074-dragonboard
          - const: qcom,apq8074

      - items:
          - enum:
              - qcom,apq8060-dragonboard
              - qcom,msm8660-surf
          - const: qcom,msm8660

      - items:
          - enum:
              - qcom,apq8084-mtp
              - qcom,apq8084-sbc
          - const: qcom,apq8084

      - items:
          - enum:
              - microsoft,dempsey
              - microsoft,makepeace
              - microsoft,moneypenny
              - motorola,falcon
              - samsung,ms013g
              - samsung,s3ve3g
          - const: qcom,msm8226

      - items:
          - enum:
              - htc,memul
              - microsoft,superman-lte
              - microsoft,tesla
              - motorola,peregrine
              - samsung,matisselte
          - const: qcom,msm8926
          - const: qcom,msm8226

      - items:
          - enum:
              - wingtech,wt82918hd
          - const: qcom,msm8929

      - items:
          - enum:
              - huawei,kiwi
              - longcheer,l9100
              - samsung,a7
              - sony,kanuti-tulip
              - square,apq8039-t2
              - wingtech,wt82918
              - wingtech,wt82918hdhw39
          - const: qcom,msm8939

      - items:
          - enum:
              - sony,kugo-row
              - sony,suzu-row
          - const: qcom,msm8956

      - items:
          - enum:
              - qcom,msm8960-cdp
              - samsung,expressatt
          - const: qcom,msm8960

      - items:
          - enum:
              - lge,hammerhead
              - samsung,hlte
              - sony,xperia-amami
              - sony,xperia-honami
          - const: qcom,msm8974

      - items:
          - enum:
              - fairphone,fp2
              - htc,m8
              - oneplus,bacon
              - samsung,klte
              - sony,xperia-aries
              - sony,xperia-castor
              - sony,xperia-leo
          - const: qcom,msm8974pro
          - const: qcom,msm8974

      - items:
          - enum:
              - samsung,kltechn
          - const: samsung,klte
          - const: qcom,msm8974pro
          - const: qcom,msm8974

      - items:
          - enum:
              - acer,a1-724
              - alcatel,idol347
              - asus,z00l
              - gplus,fl8005a
              - huawei,g7
              - lg,c50
              - lg,m216
              - longcheer,l8910
              - longcheer,l8150
              - motorola,harpia
              - motorola,osprey
              - motorola,surnia
              - qcom,msm8916-mtp
              - samsung,a3u-eur
              - samsung,a5u-eur
              - samsung,e5
              - samsung,e7
              - samsung,fortuna3g
              - samsung,gprimeltecan
              - samsung,grandmax
              - samsung,grandprimelte
              - samsung,gt510
              - samsung,gt58
              - samsung,j3ltetw
              - samsung,j5
              - samsung,j5x
              - samsung,rossa
              - samsung,serranove
              - thwc,uf896
              - thwc,ufi001c
              - wingtech,wt86518
              - wingtech,wt86528
              - wingtech,wt88047
              - yiming,uz801-v3
          - const: qcom,msm8916

      - items:
          - enum:
              - xiaomi,riva
          - const: qcom,msm8917

      - items:
          - enum:
              - motorola,potter
              - xiaomi,daisy
              - xiaomi,mido
              - xiaomi,tissot
              - xiaomi,vince
          - const: qcom,msm8953

      - items:
          - enum:
              - lg,bullhead
              - lg,h815
              - microsoft,talkman
              - xiaomi,libra
          - const: qcom,msm8992

      - items:
          - enum:
              - sony,karin_windy
          - const: qcom,apq8094

      - items:
          - enum:
              - huawei,angler
              - microsoft,cityman
              - sony,ivy-row
              - sony,karin-row
              - sony,satsuki-row
              - sony,sumire-row
              - sony,suzuran-row
          - const: qcom,msm8994

      - items:
          - enum:
              - arrow,apq8096-db820c
              - inforce,ifc6640
          - const: qcom,apq8096-sbc
          - const: qcom,apq8096

      - items:
          - enum:
              - oneplus,oneplus3
              - oneplus,oneplus3t
              - qcom,msm8996-mtp
              - sony,dora-row
              - sony,kagura-row
              - sony,keyaki-row
              - xiaomi,gemini
          - const: qcom,msm8996

      - items:
          - enum:
              - xiaomi,natrium
              - xiaomi,scorpio
          - const: qcom,msm8996pro
          - const: qcom,msm8996

      - items:
          - enum:
              - asus,novago-tp370ql
              - fxtec,pro1
              - hp,envy-x2
              - lenovo,miix-630
              - oneplus,cheeseburger
              - oneplus,dumpling
              - qcom,msm8998-mtp
              - sony,xperia-lilac
              - sony,xperia-maple
              - sony,xperia-poplar
              - xiaomi,sagit
          - const: qcom,msm8998

      - items:
          - enum:
              - 8dev,jalapeno
              - alfa-network,ap120c-ac
          - const: qcom,ipq4018

      - items:
          - enum:
              - qcom,ipq4019-ap-dk01.1-c1
              - qcom,ipq4019-ap-dk04.1-c3
              - qcom,ipq4019-ap-dk07.1-c1
              - qcom,ipq4019-ap-dk07.1-c2
              - qcom,ipq4019-dk04.1-c1
          - const: qcom,ipq4019

      - items:
          - enum:
              - qcom,ipq5018-rdp432-c2
              - tplink,archer-ax55-v1
          - const: qcom,ipq5018

      - items:
          - enum:
              - qcom,ipq5332-ap-mi01.2
              - qcom,ipq5332-ap-mi01.3
              - qcom,ipq5332-ap-mi01.6
              - qcom,ipq5332-ap-mi01.9
          - const: qcom,ipq5332

      - items:
          - enum:
              - qcom,ipq5424-rdp466
          - const: qcom,ipq5424

      - items:
          - enum:
              - mikrotik,rb3011
              - qcom,ipq8064-ap148
          - const: qcom,ipq8064

      - items:
          - enum:
              - qcom,ipq8074-hk01
              - qcom,ipq8074-hk10-c1
              - qcom,ipq8074-hk10-c2
          - const: qcom,ipq8074

      - items:
          - enum:
              - qcom,ipq9574-ap-al02-c2
              - qcom,ipq9574-ap-al02-c6
              - qcom,ipq9574-ap-al02-c7
              - qcom,ipq9574-ap-al02-c8
              - qcom,ipq9574-ap-al02-c9
          - const: qcom,ipq9574

      - description: Sierra Wireless MangOH Green with WP8548 Module
        items:
          - const: swir,mangoh-green-wp8548
          - const: swir,wp8548
          - const: qcom,mdm9615

      - description: Qualcomm Technologies, Inc. Robotics RB1
        items:
          - enum:
              - qcom,qrb2210-rb1
          - const: qcom,qrb2210
          - const: qcom,qcm2290

      - items:
          - enum:
              - fairphone,fp5
              - qcom,qcm6490-idp
              - qcom,qcs6490-rb3gen2
              - shift,otter
          - const: qcom,qcm6490

      - description: Qualcomm Technologies, Inc. Distributed Unit 1000 platform
        items:
          - enum:
              - qcom,qdu1000-idp
              - qcom,qdu1000-x100
          - const: qcom,qdu1000

      - description: Qualcomm Technologies, Inc. Radio Unit 1000 platform
        items:
          - enum:
              - qcom,qru1000-idp
          - const: qcom,qru1000

      - description: Qualcomm AR2 Gen1 platform
        items:
          - enum:
              - qcom,qar2130p
          - const: qcom,sar2130p

      - items:
          - enum:
              - acer,aspire1
              - qcom,sc7180-idp
          - const: qcom,sc7180

      - description: HP Chromebook x2 11c (rev1 - 2)
        items:
          - const: google,coachz-rev1
          - const: google,coachz-rev2
          - const: qcom,sc7180

      - description: HP Chromebook x2 11c (newest rev)
        items:
          - const: google,coachz
          - const: qcom,sc7180

      - description: HP Chromebook x2 11c with LTE (rev1 - 2)
        items:
          - const: google,coachz-rev1-sku0
          - const: google,coachz-rev2-sku0
          - const: qcom,sc7180

      - description: HP Chromebook x2 11c with LTE (newest rev)
        items:
          - const: google,coachz-sku0
          - const: qcom,sc7180

      - description: Lenovo Chromebook Duet 5 13 (rev2)
        items:
          - const: google,homestar-rev2
          - const: google,homestar-rev23
          - const: qcom,sc7180

      - description: Lenovo Chromebook Duet 5 13 (rev3)
        items:
          - const: google,homestar-rev3
          - const: qcom,sc7180

      - description: Lenovo Chromebook Duet 5 13 (newest rev)
        items:
          - const: google,homestar
          - const: qcom,sc7180

      - description: Google Kingoftown (rev0)
        items:
          - const: google,kingoftown-rev0
          - const: qcom,sc7180

      - description: Google Kingoftown (newest rev)
        items:
          - const: google,kingoftown
          - const: qcom,sc7180

      - description: Acer Chromebook Spin 513 (rev0)
        items:
          - const: google,lazor-rev0
          - const: qcom,sc7180

      - description: Acer Chromebook Spin 513 (rev1 - 2)
        items:
          - const: google,lazor-rev1
          - const: google,lazor-rev2
          - const: qcom,sc7180

      - description: Acer Chromebook Spin 513 (rev3 - 8)
        items:
          - const: google,lazor-rev3
          - const: google,lazor-rev4
          - const: google,lazor-rev5
          - const: google,lazor-rev6
          - const: google,lazor-rev7
          - const: google,lazor-rev8
          - const: qcom,sc7180

      - description: Acer Chromebook Spin 513 (rev9)
        items:
          - const: google,lazor-rev9
          - const: qcom,sc7180

      - description: Acer Chromebook Spin 513 (newest rev)
        items:
          - const: google,lazor
          - const: qcom,sc7180

      - description: Acer Chromebook Spin 513 with KB Backlight (rev1 - 2)
        items:
          - const: google,lazor-rev1-sku2
          - const: google,lazor-rev2-sku2
          - const: qcom,sc7180

      - description: Acer Chromebook Spin 513 with KB Backlight (rev3 - 8)
        items:
          - const: google,lazor-rev3-sku2
          - const: google,lazor-rev4-sku2
          - const: google,lazor-rev5-sku2
          - const: google,lazor-rev6-sku2
          - const: google,lazor-rev7-sku2
          - const: google,lazor-rev8-sku2
          - const: qcom,sc7180

      - description: Acer Chromebook Spin 513 with KB Backlight (rev9)
        items:
          - const: google,lazor-rev9-sku2
          - const: qcom,sc7180

      - description: Acer Chromebook Spin 513 with KB Backlight (newest rev)
        items:
          - const: google,lazor-sku2
          - const: qcom,sc7180

      - description: Acer Chromebook Spin 513 with LTE (rev1 - 2)
        items:
          - const: google,lazor-rev1-sku0
          - const: google,lazor-rev2-sku0
          - const: qcom,sc7180

      - description: Acer Chromebook Spin 513 with LTE (rev3 - 8)
        items:
          - const: google,lazor-rev3-sku0
          - const: google,lazor-rev4-sku0
          - const: google,lazor-rev5-sku0
          - const: google,lazor-rev6-sku0
          - const: google,lazor-rev7-sku0
          - const: google,lazor-rev8-sku0
          - const: qcom,sc7180

      - description: Acer Chromebook Spin 513 with LTE (rev9)
        items:
          - const: google,lazor-rev9-sku0
          - const: google,lazor-rev9-sku10
          - const: qcom,sc7180

      - description: Acer Chromebook Spin 513 with LTE (newest rev)
        items:
          - const: google,lazor-sku0
          - const: google,lazor-sku10
          - const: qcom,sc7180

      - description: Acer Chromebook 511 (rev4 - rev8)
        items:
          - const: google,lazor-rev4-sku4
          - const: google,lazor-rev5-sku4
          - const: google,lazor-rev6-sku4
          - const: google,lazor-rev7-sku4
          - const: google,lazor-rev8-sku4
          - const: qcom,sc7180

      - description: Acer Chromebook 511 (rev9)
        items:
          - const: google,lazor-rev9-sku4
          - const: google,lazor-rev9-sku15
          - const: qcom,sc7180

      - description: Acer Chromebook 511 (newest rev)
        items:
          - const: google,lazor-sku4
          - const: google,lazor-sku15
          - const: qcom,sc7180

      - description: Acer Chromebook 511 without Touchscreen (rev4)
        items:
          - const: google,lazor-rev4-sku5
          - const: qcom,sc7180

      - description: Acer Chromebook 511 without Touchscreen (rev5 - rev8)
        items:
          - const: google,lazor-rev5-sku5
          - const: google,lazor-rev5-sku6
          - const: google,lazor-rev6-sku6
          - const: google,lazor-rev7-sku6
          - const: google,lazor-rev8-sku6
          - const: qcom,sc7180

      - description: Acer Chromebook 511 without Touchscreen (rev9)
        items:
          - const: google,lazor-rev9-sku6
          - const: google,lazor-rev9-sku18
          - const: qcom,sc7180

      - description: Acer Chromebook 511 without Touchscreen (newest rev)
        items:
          - const: google,lazor-sku6
          - const: google,lazor-sku18
          - const: qcom,sc7180

      - description: Google Mrbland with AUO panel (rev0)
        items:
          - const: google,mrbland-rev0-sku0
          - const: qcom,sc7180

      - description: Google Mrbland with AUO panel (newest rev)
        items:
          - const: google,mrbland-sku1536
          - const: qcom,sc7180

      - description: Google Mrbland with BOE panel (rev0)
        items:
          - const: google,mrbland-rev0-sku16
          - const: qcom,sc7180

      - description: Google Mrbland with BOE panel (newest rev)
        items:
          - const: google,mrbland-sku1024
          - const: google,mrbland-sku768
          - const: qcom,sc7180

      - description: Google Pazquel with Parade (newest rev)
        items:
          - const: google,pazquel-sku5
          - const: qcom,sc7180

      - description: Google Pazquel with TI (newest rev)
        items:
          - const: google,pazquel-sku1
          - const: qcom,sc7180

      - description: Google Pazquel with LTE and Parade (newest rev)
        items:
          - const: google,pazquel-sku6
          - const: google,pazquel-sku4
          - const: qcom,sc7180

      - description: Google Pazquel with LTE and TI (newest rev)
        items:
          - const: google,pazquel-sku0
          - const: google,pazquel-sku2
          - const: qcom,sc7180

      - description: Google Pazquel360 with LTE (newest rev)
        items:
          - const: google,pazquel-sku22
          - const: google,pazquel-sku20
          - const: qcom,sc7180

      - description: Google Pazquel360 with WiFi (newest rev)
        items:
          - const: google,pazquel-sku21
          - const: qcom,sc7180

      - description: Sharp Dynabook Chromebook C1 (rev1)
        items:
          - const: google,pompom-rev1
          - const: qcom,sc7180

      - description: Sharp Dynabook Chromebook C1 (rev2)
        items:
          - const: google,pompom-rev2
          - const: qcom,sc7180

      - description: Sharp Dynabook Chromebook C1 (newest rev)
        items:
          - const: google,pompom
          - const: qcom,sc7180

      - description: Sharp Dynabook Chromebook C1 with LTE (rev1)
        items:
          - const: google,pompom-rev1-sku0
          - const: qcom,sc7180

      - description: Sharp Dynabook Chromebook C1 with LTE (rev2)
        items:
          - const: google,pompom-rev2-sku0
          - const: qcom,sc7180

      - description: Sharp Dynabook Chromebook C1 with LTE (newest rev)
        items:
          - const: google,pompom-sku0
          - const: qcom,sc7180

      - description: Google Quackingstick (newest rev)
        items:
          - const: google,quackingstick-sku1537
          - const: qcom,sc7180

      - description: Google Quackingstick with LTE (newest rev)
        items:
          - const: google,quackingstick-sku1536
          - const: qcom,sc7180

      - description: Google Trogdor (newest rev)
        items:
          - const: google,trogdor
          - const: qcom,sc7180

      - description: Google Trogdor with LTE (newest rev)
        items:
          - const: google,trogdor-sku0
          - const: qcom,sc7180

      - description: Lenovo IdeaPad Chromebook Duet 3 with BOE panel (rev0)
        items:
          - const: google,wormdingler-rev0-sku16
          - const: qcom,sc7180

      - description: Lenovo IdeaPad Chromebook Duet 3 with BOE panel (newest rev)
        items:
          - const: google,wormdingler-sku1024
          - const: qcom,sc7180

      - description: Lenovo IdeaPad Chromebook Duet 3 with BOE panel and rt5682s (newest rev)
        items:
          - const: google,wormdingler-sku1025
          - const: qcom,sc7180

      - description: Lenovo IdeaPad Chromebook Duet 3 with INX panel (rev0)
        items:
          - const: google,wormdingler-rev0-sku0
          - const: qcom,sc7180

      - description: Lenovo IdeaPad Chromebook Duet 3 with INX panel (newest rev)
        items:
          - const: google,wormdingler-sku0
          - const: qcom,sc7180

      - description: Lenovo IdeaPad Chromebook Duet 3 with INX panel and rt5682s (newest rev)
        items:
          - const: google,wormdingler-sku1
          - const: qcom,sc7180

      - description: Qualcomm Technologies, Inc. sc7280 CRD platform (rev3 - 4)
        items:
          - const: qcom,sc7280-crd
          - const: google,hoglin-rev3
          - const: google,hoglin-rev4
          - const: google,piglin-rev3
          - const: google,piglin-rev4
          - const: qcom,sc7280

      - description: Qualcomm Technologies, Inc. sc7280 CRD platform (newest rev)
        items:
          - const: google,zoglin
          - const: google,hoglin
          - const: qcom,sc7280

      - description: Qualcomm Technologies, Inc. sc7280 CRD Pro platform (newest rev)
        items:
          - const: google,zoglin-sku1536
          - const: google,hoglin-sku1536
          - const: qcom,sc7280

      - description: Qualcomm Technologies, Inc. sc7280 IDP SKU1 platform
        items:
          - const: qcom,sc7280-idp
          - const: google,senor
          - const: qcom,sc7280

      - description: Qualcomm Technologies, Inc. sc7280 IDP SKU2 platform
        items:
          - const: qcom,sc7280-idp2
          - const: google,piglin
          - const: qcom,sc7280

      - description: Google Evoker (newest rev)
        items:
          - const: google,evoker
          - const: qcom,sc7280

      - description: Google Evoker with LTE (newest rev)
        items:
          - const: google,evoker-sku512
          - const: qcom,sc7280

      - description: Google Herobrine (newest rev)
        items:
          - const: google,herobrine
          - const: qcom,sc7280

      - description: Google Villager (rev0)
        items:
          - const: google,villager-rev0
          - const: qcom,sc7280

      - description: Google Villager (newest rev)
        items:
          - const: google,villager
          - const: qcom,sc7280

      - description: Google Villager with LTE (newest rev)
        items:
          - const: google,villager-sku512
          - const: qcom,sc7280

      - description: Google Zombie (newest rev)
        items:
          - const: google,zombie
          - const: qcom,sc7280

      - description: Google Zombie with LTE (newest rev)
        items:
          - const: google,zombie-sku512
          - const: qcom,sc7280

      - description: Google Zombie with NVMe (newest rev)
        items:
          - const: google,zombie-sku2
          - const: google,zombie-sku3
          - const: google,zombie-sku515
          - const: qcom,sc7280

      - description: Google Zombie with LTE and NVMe (newest rev)
        items:
          - const: google,zombie-sku514
          - const: qcom,sc7280

      - items:
          - enum:
              - lenovo,flex-5g
              - microsoft,surface-prox
              - qcom,sc8180x-primus
          - const: qcom,sc8180x

      - items:
          - enum:
              - huawei,gaokun3
              - lenovo,thinkpad-x13s
              - microsoft,arcata
              - microsoft,blackrock
              - qcom,sc8280xp-crd
              - qcom,sc8280xp-qrd
          - const: qcom,sc8280xp

      - items:
          - enum:
              - lenovo,tbx605f
              - motorola,ali
          - const: qcom,sdm450

      - items:
          - enum:
              - sony,discovery-row
              - sony,kirin-row
              - sony,pioneer-row
              - sony,voyager-row
          - const: qcom,sdm630

      - items:
          - enum:
              - inforce,ifc6560
          - const: qcom,sda660

      - items:
          - enum:
              - fairphone,fp3
              - motorola,ocean
          - const: qcom,sdm632

      - items:
          - enum:
              - sony,mermaid-row
          - const: qcom,sdm636

      - items:
          - enum:
              - xiaomi,lavender
          - const: qcom,sdm660

      - items:
          - enum:
              - google,sargo
          - const: qcom,sdm670

      - items:
          - enum:
              - qcom,sdx55-mtp
              - qcom,sdx55-telit-fn980-tlb
              - qcom,sdx55-t55
          - const: qcom,sdx55

      - items:
          - enum:
              - qcom,sdx65-mtp
          - const: qcom,sdx65

      - items:
          - enum:
              - qcom,sdx75-idp
          - const: qcom,sdx75

      - items:
          - enum:
              - qcom,ipq6018-cp01
              - qcom,ipq6018-cp01-c1
          - const: qcom,ipq6018

      - items:
          - enum:
              - qcom,qcs404-evb-1000
              - qcom,qcs404-evb-4000
          - const: qcom,qcs404-evb
          - const: qcom,qcs404

      - items:
          - enum:
              - qcom,qcs8300-ride
          - const: qcom,qcs8300

      - items:
          - enum:
              - qcom,qcs615-ride
          - const: qcom,qcs615

      - items:
          - enum:
              - qcom,sa8155p-adp
          - const: qcom,sa8155p

      - items:
          - enum:
              - qcom,sa8295p-adp
              - qcom,sa8540p-ride
          - const: qcom,sa8540p

      - items:
          - enum:
              - qcom,sa8775p-ride
              - qcom,sa8775p-ride-r3
          - const: qcom,sa8775p

      - items:
          - enum:
              - qcom,qcs9100-ride
              - qcom,qcs9100-ride-r3
          - const: qcom,qcs9100
          - const: qcom,sa8775p

      - items:
          - enum:
              - google,cheza
              - google,cheza-rev1
              - google,cheza-rev2
              - lenovo,yoga-c630
              - lg,judyln
              - lg,judyp
              - oneplus,enchilada
              - oneplus,fajita
              - qcom,sdm845-mtp
              - shift,axolotl
              - samsung,starqltechn
              - samsung,w737
              - sony,akari-row
              - sony,akatsuki-row
              - sony,apollo-row
              - thundercomm,db845c
              - xiaomi,beryllium
              - xiaomi,beryllium-ebbg
              - xiaomi,polaris
          - const: qcom,sdm845

      - items:
          - enum:
              - oneplus,billie2
          - const: qcom,sm4250

      - items:
          - enum:
              - qcom,qrb4210-rb2
          - const: qcom,qrb4210
          - const: qcom,sm4250

      - items:
          - enum:
              - qcom,sm4450-qrd
          - const: qcom,sm4450

      - items:
          - enum:
              - fxtec,pro1x
          - const: qcom,sm6115

      - items:
          - enum:
              - lenovo,j606f
          - const: qcom,sm6115p
          - const: qcom,sm6115

      - items:
          - enum:
              - sony,pdx201
              - xiaomi,ginkgo
              - xiaomi,laurel-sprout
          - const: qcom,sm6125

      - items:
          - enum:
              - sony,pdx213
          - const: qcom,sm6350

      - items:
          - enum:
              - sony,pdx225
          - const: qcom,sm6375

      - items:
          - enum:
              - xiaomi,curtana
              - xiaomi,joyeuse
          - const: qcom,sm7125

      - items:
          - enum:
              - google,sunfish
          - const: qcom,sm7150

      - items:
          - enum:
              - fairphone,fp4
          - const: qcom,sm7225

      - items:
          - enum:
              - nothing,spacewar
          - const: qcom,sm7325

      - items:
          - enum:
              - microsoft,surface-duo
              - qcom,sm8150-hdk
              - qcom,sm8150-mtp
              - sony,bahamut-generic
              - sony,griffin-generic
          - const: qcom,sm8150

      - items:
          - enum:
              - qcom,qrb5165-rb5
              - qcom,sm8250-hdk
              - qcom,sm8250-mtp
              - sony,pdx203-generic
              - sony,pdx206-generic
              - xiaomi,elish
              - xiaomi,pipa
          - const: qcom,sm8250

      - items:
          - enum:
              - microsoft,surface-duo2
              - qcom,sm8350-hdk
              - qcom,sm8350-mtp
              - sony,pdx214-generic
              - sony,pdx215-generic
          - const: qcom,sm8350

      - items:
          - enum:
              - qcom,sm8450-hdk
              - qcom,sm8450-qrd
              - sony,pdx223
              - sony,pdx224
          - const: qcom,sm8450

      - items:
          - enum:
              - qcom,sm8550-hdk
              - qcom,sm8550-mtp
              - qcom,sm8550-qrd
              - samsung,q5q
              - sony,pdx234
          - const: qcom,sm8550

      - items:
          - enum:
              - qcom,qcs8550-aim300-aiot
          - const: qcom,qcs8550-aim300
          - const: qcom,qcs8550
          - const: qcom,sm8550

      - items:
          - enum:
              - qcom,sm8650-hdk
              - qcom,sm8650-mtp
              - qcom,sm8650-qrd
          - const: qcom,sm8650

      - items:
          - enum:
              - qcom,sm8750-mtp
              - qcom,sm8750-qrd
          - const: qcom,sm8750

      - items:
          - enum:
              - qcom,x1e001de-devkit
          - const: qcom,x1e001de
          - const: qcom,x1e80100

      - items:
          - enum:
              - lenovo,thinkpad-t14s-lcd
              - lenovo,thinkpad-t14s-oled
          - const: lenovo,thinkpad-t14s
          - const: qcom,x1e78100
          - const: qcom,x1e80100

      - items:
          - enum:
              - asus,vivobook-s15
              - asus,zenbook-a14-ux3407ra
              - dell,xps13-9345
              - hp,elitebook-ultra-g1q
              - hp,omnibook-x14
              - lenovo,yoga-slim7x
              - microsoft,romulus13
              - microsoft,romulus15
              - qcom,x1e80100-crd
              - qcom,x1e80100-qcp
          - const: qcom,x1e80100

      - items:
          - enum:
              - asus,zenbook-a14-ux3407qa
              - qcom,x1p42100-crd
          - const: qcom,x1p42100

  # Board compatibles go above

  qcom,msm-id:
    $ref: /schemas/types.yaml#/definitions/uint32-matrix
    minItems: 1
    maxItems: 8
    items:
      items:
        - description: |
            MSM chipset ID - an exact match value consisting of two bitfields::
             - bits 0-15  - The unique MSM chipset ID
             - bits 16-31 - Reserved; should be 0
        - description: |
            Hardware revision ID - a chipset specific 32-bit ID representing
            the version of the chipset.  It is best a match value - the
            bootloader will look for the closest possible match.
    deprecated: true
    description:
      The MSM chipset and hardware revision used Qualcomm bootloaders.  It
      can optionally be an array of these to indicate multiple hardware that
      use the same device tree.  It is expected that the bootloader will use
      this information at boot-up to decide which device tree to use when given
      multiple device trees, some of which may not be compatible with the
      actual hardware.  It is the bootloader's responsibility to pass the
      correct device tree to the kernel.
      The property is deprecated.

  qcom,board-id:
    $ref: /schemas/types.yaml#/definitions/uint32-matrix
    minItems: 1
    maxItems: 8
    oneOf:
      - items:
          - items:
              - description: |
                  Board ID consisting of three bitfields::
                    - bits 31-24 - Unused
                    - bits 23-16 - Platform Version Major
                    - bits 15-8  - Platform Version Minor
                    - bits 7-0   - Platform Type
                  Platform Type field is an exact match value.  The
                  Platform Major/Minor field is a best match.  The bootloader will
                  look for the closest possible match.
              - description: |
                  Subtype ID unique to a Platform Type/Chipset ID.  For a given
                  Platform Type, there will typically only be a single board and the
                  subtype_id will be 0.  However in some cases board variants may
                  need to be distinguished by different subtype_id values.
      - items:
          # OnePlus uses a variant of board-id with four elements:
          - items:
              - const: 8
              - const: 0
              - description: OnePlus board ID
              - description: OnePlus subtype ID
    deprecated: true
    description:
      The board type and revision information.  It can optionally be an array
      of these to indicate multiple boards that use the same device tree.  It
      is expected that the bootloader will use this information at boot-up to
      decide which device tree to use when given multiple device trees, some of
      which may not be compatible with the actual hardware.  It is the
      bootloader's responsibility to pass the correct device tree to the
      kernel
      The property is deprecated.

allOf:
  # Explicit allow-list for older SoCs. The legacy properties are not allowed
  # on newer SoCs.
  - if:
      properties:
        compatible:
          contains:
            enum:
              - qcom,apq8026
              - qcom,apq8094
              - qcom,apq8096
              - qcom,msm8917
              - qcom,msm8939
              - qcom,msm8953
              - qcom,msm8956
              - qcom,msm8992
              - qcom,msm8994
              - qcom,msm8996
              - qcom,msm8998
              - qcom,sdm450
              - qcom,sdm630
              - qcom,sdm632
              - qcom,sdm636
              - qcom,sdm845
              - qcom,sdx55
              - qcom,sdx65
              - qcom,sdx75
              - qcom,sm4250
              - qcom,sm6115
              - qcom,sm6125
              - qcom,sm6350
              - qcom,sm7125
              - qcom,sm7225
              - qcom,sm8150
              - qcom,sm8250
    then:
      properties:
        qcom,board-id: true
        qcom,msm-id: true
    else:
      properties:
        qcom,board-id: false
        qcom,msm-id: false

  - if:
      properties:
        compatible:
          contains:
            enum:
              - oneplus,cheeseburger
              - oneplus,dumpling
              - oneplus,enchilada
              - oneplus,fajita
              - oneplus,oneplus3
              - oneplus,oneplus3t
    then:
      properties:
        qcom,board-id:
          items:
            minItems: 4
    else:
      properties:
        qcom,board-id:
          items:
            maxItems: 2

additionalProperties: true

...
