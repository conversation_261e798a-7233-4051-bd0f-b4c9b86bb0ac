# SPDX-License-Identifier: GPL-2.0-only OR BSD-2-Clause
%YAML 1.2
---
$id: http://devicetree.org/schemas/arm/qcom-soc.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: Qualcomm SoC compatibles naming convention

maintainers:
  - <PERSON><PERSON><PERSON> <and<PERSON><PERSON>@kernel.org>

description: |
  Guidelines for new compatibles for SoC blocks/components.
  When adding new compatibles in new bindings, use the format::
    qcom,SoC-IP

  For example::
   qcom,sdm845-llcc-bwmon

  When adding new compatibles to existing bindings, use the format in the
  existing binding, even if it contradicts the above.

select:
  properties:
    compatible:
      pattern: "^qcom,.*(apq|ipq|mdm|msm|qcm|qcs|q[dr]u|sa|sar|sc|sd[amx]|sm|x1[ep])[0-9]+.*$"
  required:
    - compatible

properties:
  compatible:
    oneOf:
      # Preferred naming style for compatibles of SoC components:
      - pattern: "^qcom,(apq|ipq|mdm|msm|qcm|qcs|q[dr]u|sa|sc|sd[amx]|sm|x1[ep])[0-9]+(pro)?-.*$"
      - pattern: "^qcom,sar[0-9]+[a-z]?-.*$"
      - pattern: "^qcom,(sa|sc)8[0-9]+[a-z][a-z]?-.*$"

      # Legacy namings - variations of existing patterns/compatibles are OK,
      # but do not add completely new entries to these:
      - pattern: "^qcom,[ak]pss-wdt-(apq|ipq|mdm|msm|qcm|qcs|q[dr]u|sa|sc|sd[amx]|sm)[0-9]+.*$"
      - pattern: "^qcom,gcc-(apq|ipq|mdm|msm|qcm|qcs|q[dr]u|sa|sc|sd[amx]|sm)[0-9]+.*$"
      - pattern: "^qcom,mmcc-(apq|ipq|mdm|msm|qcm|qcs|q[dr]u|sa|sc|sd[amx]|sm)[0-9]+.*$"
      - pattern: "^qcom,pcie-(apq|ipq|mdm|msm|qcm|qcs|q[dr]u|sa|sc|sd[amx]|sm|x1[ep])[0-9]+.*$"
      - pattern: "^qcom,rpm-(apq|ipq|mdm|msm|qcm|qcs|q[dr]u|sa|sc|sd[amx]|sm)[0-9]+.*$"
      - pattern: "^qcom,scm-(apq|ipq|mdm|msm|qcm|qcs|q[dr]u|sa|sc|sd[amx]|sm|x1[ep])[0-9]+.*$"
      - enum:
          - qcom,dsi-ctrl-6g-qcm2290
          - qcom,gpucc-sdm630
          - qcom,gpucc-sdm660
          - qcom,lcc-apq8064
          - qcom,lcc-ipq8064
          - qcom,lcc-mdm9615
          - qcom,lcc-msm8960
          - qcom,lpass-cpu-apq8016
          - qcom,usb-ss-ipq4019-phy
          - qcom,usb-hs-ipq4019-phy
          - qcom,vqmmc-ipq4019-regulator

      # Legacy compatibles with wild-cards - list cannot grow with new bindings:
      - enum:
          - qcom,ipq806x-gmac
          - qcom,ipq806x-nand
          - qcom,ipq806x-sata-phy
          - qcom,ipq806x-usb-phy-ss
          - qcom,ipq806x-usb-phy-hs

additionalProperties: true
