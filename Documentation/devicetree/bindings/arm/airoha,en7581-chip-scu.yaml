# SPDX-License-Identifier: (GPL-2.0 OR BSD-2-Clause)
%YAML 1.2
---
$id: http://devicetree.org/schemas/arm/airoha,en7581-chip-scu.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: Airoha Chip SCU Controller for EN7581 SoC

maintainers:
  - <PERSON> <<EMAIL>>

description:
  The airoha chip-scu block provides a configuration interface for clock,
  io-muxing and other functionalities used by multiple controllers (e.g. clock,
  pinctrl, ecc) on EN7581 SoC.

properties:
  compatible:
    items:
      - enum:
          - airoha,en7581-chip-scu
      - const: syscon

  reg:
    maxItems: 1

required:
  - compatible
  - reg

additionalProperties: false

examples:
  - |
    soc {
      #address-cells = <2>;
      #size-cells = <2>;
      syscon@1fa20000 {
        compatible = "airoha,en7581-chip-scu", "syscon";
        reg = <0x0 0x1fa20000 0x0 0x388>;
      };
    };
