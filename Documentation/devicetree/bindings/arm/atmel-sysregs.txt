Atmel system registers

Chipid required properties:
- compatible: Should be "atmel,sama5d2-chipid" or "microchip,sama7g5-chipid"
			"microchip,sama7d65-chipid"
- reg : Should contain registers location and length

PIT Timer required properties:
- compatible: Should be "atmel,at91sam9260-pit"
- reg: Should contain registers location and length
- interrupts: Should contain interrupt for the PIT which is the IRQ line
  shared across all System Controller members.

PIT64B Timer required properties:
- compatible: Should be "microchip,sam9x60-pit64b" or
			"microchip,sam9x7-pit64b", "microchip,sam9x60-pit64b"
			"microchip,sama7d65-pit64b", "microchip,sam9x60-pit64b"
- reg: Should contain registers location and length
- interrupts: Should contain interrupt for PIT64B timer
- clocks: Should contain the available clock sources for PIT64B timer.

System Timer (ST) required properties:
- compatible: Should be "atmel,at91rm9200-st", "syscon", "simple-mfd"
- reg: Should contain registers location and length
- interrupts: Should contain interrupt for the ST which is the IRQ line
  shared across all System Controller members.
- clocks: phandle to input clock.
Its subnodes can be:
- watchdog: compatible should be "atmel,at91rm9200-wdt"

RAMC SDRAM/DDR Controller required properties:
- compatible: Should be "atmel,at91rm9200-sdramc", "syscon" or
			"atmel,at91sam9260-sdramc" or
			"atmel,at91sam9g45-ddramc" or
			"atmel,sama5d3-ddramc" or
			"microchip,sam9x60-ddramc" or
			"microchip,sama7g5-uddrc" or
			"microchip,sama7d65-uddrc", "microchip,sama7g5-uddrc" or
			"microchip,sam9x7-ddramc", "atmel,sama5d3-ddramc".
- reg: Should contain registers location and length

Examples:

	ramc0: ramc@ffffe800 {
		compatible = "atmel,at91sam9g45-ddramc";
		reg = <0xffffe800 0x200>;
	};

