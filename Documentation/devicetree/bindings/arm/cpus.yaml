# SPDX-License-Identifier: GPL-2.0
%YAML 1.2
---
$id: http://devicetree.org/schemas/arm/cpus.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: ARM CPUs

maintainers:
  - <PERSON> <<EMAIL>>

description: |+
  The device tree allows to describe the layout of CPUs in a system through the
  "cpus" node, which in turn contains a number of subnodes (ie "cpu") defining
  properties for every cpu.

  Bindings for CPU nodes follow the Devicetree Specification, available from:

  https://www.devicetree.org/specifications/

  with updates for 32-bit and 64-bit ARM systems provided in this document.

  ================================
  Convention used in this document
  ================================

  This document follows the conventions described in the Devicetree
  Specification, with the addition:

  - square brackets define bitfields, eg reg[7:0] value of the bitfield in
    the reg property contained in bits 7 down to 0

  =====================================
  cpus and cpu node bindings definition
  =====================================

  The ARM architecture, in accordance with the Devicetree Specification,
  requires the cpus and cpu nodes to be present and contain the properties
  described below.

properties:
  reg:
    maxItems: 1
    description: >
      Usage and definition depend on ARM architecture version and configuration:

      On uniprocessor ARM architectures previous to v7 this property is required
      and must be set to 0.

      On ARM 11 MPcore based systems this property is required and matches the
      CPUID[11:0] register bits.

        Bits [11:0] in the reg cell must be set to bits [11:0] in CPU ID register.

        All other bits in the reg cell must be set to 0.

      On 32-bit ARM v7 or later systems this property is required and matches
      the CPU MPIDR[23:0] register bits.

        Bits [23:0] in the reg cell must be set to bits [23:0] in MPIDR.

        All other bits in the reg cell must be set to 0.

      On ARM v8 64-bit systems this property is required and matches the
      MPIDR_EL1 register affinity bits.

        * If cpus node's #address-cells property is set to 2

          The first reg cell bits [7:0] must be set to bits [39:32] of
          MPIDR_EL1.

          The second reg cell bits [23:0] must be set to bits [23:0] of
          MPIDR_EL1.

        * If cpus node's #address-cells property is set to 1

          The reg cell bits [23:0] must be set to bits [23:0] of MPIDR_EL1.

      All other bits in the reg cells must be set to 0.

  compatible:
    enum:
      - apple,avalanche
      - apple,blizzard
      - apple,cyclone
      - apple,firestorm
      - apple,hurricane-zephyr
      - apple,icestorm
      - apple,mistral
      - apple,monsoon
      - apple,twister
      - apple,typhoon
      - arm,arm710t
      - arm,arm720t
      - arm,arm740t
      - arm,arm7ej-s
      - arm,arm7tdmi
      - arm,arm7tdmi-s
      - arm,arm9es
      - arm,arm9ej-s
      - arm,arm920t
      - arm,arm922t
      - arm,arm925
      - arm,arm926e-s
      - arm,arm926ej-s
      - arm,arm940t
      - arm,arm946e-s
      - arm,arm966e-s
      - arm,arm968e-s
      - arm,arm9tdmi
      - arm,arm1020e
      - arm,arm1020t
      - arm,arm1022e
      - arm,arm1026ej-s
      - arm,arm1136j-s
      - arm,arm1136jf-s
      - arm,arm1156t2-s
      - arm,arm1156t2f-s
      - arm,arm1176jzf
      - arm,arm1176jz-s
      - arm,arm1176jzf-s
      - arm,arm11mpcore
      - arm,armv8 # Only for s/w models
      - arm,cortex-a5
      - arm,cortex-a7
      - arm,cortex-a8
      - arm,cortex-a9
      - arm,cortex-a12
      - arm,cortex-a15
      - arm,cortex-a17
      - arm,cortex-a32
      - arm,cortex-a34
      - arm,cortex-a35
      - arm,cortex-a53
      - arm,cortex-a55
      - arm,cortex-a57
      - arm,cortex-a65
      - arm,cortex-a72
      - arm,cortex-a73
      - arm,cortex-a75
      - arm,cortex-a76
      - arm,cortex-a77
      - arm,cortex-a78
      - arm,cortex-a78ae
      - arm,cortex-a78c
      - arm,cortex-a510
      - arm,cortex-a520
      - arm,cortex-a710
      - arm,cortex-a715
      - arm,cortex-a720
      - arm,cortex-a725
      - arm,cortex-m0
      - arm,cortex-m0+
      - arm,cortex-m1
      - arm,cortex-m3
      - arm,cortex-m4
      - arm,cortex-r4
      - arm,cortex-r5
      - arm,cortex-r7
      - arm,cortex-r52
      - arm,cortex-x1
      - arm,cortex-x1c
      - arm,cortex-x2
      - arm,cortex-x3
      - arm,cortex-x4
      - arm,cortex-x925
      - arm,neoverse-e1
      - arm,neoverse-n1
      - arm,neoverse-n2
      - arm,neoverse-n3
      - arm,neoverse-v1
      - arm,neoverse-v2
      - arm,neoverse-v3
      - arm,neoverse-v3ae
      - arm,rainier
      - brcm,brahma-b15
      - brcm,brahma-b53
      - brcm,vulcan
      - cavium,thunder
      - cavium,thunder2
      - faraday,fa526
      - intel,sa110
      - intel,sa1100
      - marvell,feroceon
      - marvell,mohawk
      - marvell,pj4a
      - marvell,pj4b
      - marvell,sheeva-v5
      - marvell,sheeva-v7
      - nvidia,tegra132-denver
      - nvidia,tegra186-denver
      - nvidia,tegra194-carmel
      - qcom,krait
      - qcom,kryo
      - qcom,kryo240
      - qcom,kryo250
      - qcom,kryo260
      - qcom,kryo280
      - qcom,kryo360
      - qcom,kryo385
      - qcom,kryo465
      - qcom,kryo468
      - qcom,kryo485
      - qcom,kryo560
      - qcom,kryo570
      - qcom,kryo660
      - qcom,kryo670
      - qcom,kryo685
      - qcom,kryo780
      - qcom,oryon
      - qcom,scorpion
      - samsung,mongoose-m2
      - samsung,mongoose-m3
      - samsung,mongoose-m5

  enable-method:
    $ref: /schemas/types.yaml#/definitions/string
    oneOf:
      # On ARM v8 64-bit this property is required
      - enum:
          - psci
          - spin-table
      # On ARM 32-bit systems this property is optional
      - enum:
          - actions,s500-smp
          - allwinner,sun6i-a31
          - allwinner,sun8i-a23
          - allwinner,sun9i-a80-smp
          - allwinner,sun8i-a83t-smp
          - amlogic,meson8-smp
          - amlogic,meson8b-smp
          - arm,realview-smp
          - aspeed,ast2600-smp
          - brcm,bcm11351-cpu-method
          - brcm,bcm23550
          - brcm,bcm2836-smp
          - brcm,bcm63138
          - brcm,bcm-nsp-smp
          - brcm,brahma-b15
          - marvell,armada-375-smp
          - marvell,armada-380-smp
          - marvell,armada-390-smp
          - marvell,armada-xp-smp
          - marvell,98dx3236-smp
          - marvell,mmp3-smp
          - mediatek,mt6589-smp
          - mediatek,mt81xx-tz-smp
          - qcom,gcc-msm8660
          - qcom,kpss-acc-v1
          - qcom,kpss-acc-v2
          - qcom,msm8226-smp
          - qcom,msm8909-smp
          # Only valid on ARM 32-bit, see above for ARM v8 64-bit
          - qcom,msm8916-smp
          - renesas,apmu
          - renesas,r9a06g032-smp
          - rockchip,rk3036-smp
          - rockchip,rk3066-smp
          - socionext,milbeaut-m10v-smp
          - ste,dbx500-smp
          - ti,am3352
          - ti,am4372

  cpu-release-addr:
    oneOf:
      - $ref: /schemas/types.yaml#/definitions/uint32
      - $ref: /schemas/types.yaml#/definitions/uint64
    description:
      The DT specification defines this as 64-bit always, but some 32-bit Arm
      systems have used a 32-bit value which must be supported.

  cpu-idle-states:
    $ref: /schemas/types.yaml#/definitions/phandle-array
    items:
      maxItems: 1
    description:
      List of phandles to idle state nodes supported by this cpu (see
      ./idle-states.yaml).

  capacity-dmips-mhz:
    description:
      u32 value representing CPU capacity (see ../cpu/cpu-capacity.txt) in
      DMIPS/MHz, relative to highest capacity-dmips-mhz in the system.

  cci-control-port: true

  dynamic-power-coefficient:
    $ref: /schemas/types.yaml#/definitions/uint32
    description: >
      A u32 value that represents the running time dynamic power coefficient in
      units of uW/MHz/V^2. The coefficient can either be calculated from power
      measurements or derived by analysis.

      The dynamic power consumption of the CPU  is proportional to the square of
      the Voltage (V) and the clock frequency (f). The coefficient is used to
      calculate the dynamic power as below -

      Pdyn = dynamic-power-coefficient * V^2 * f

      where voltage is in V, frequency is in MHz.

  interconnects:
    minItems: 1
    maxItems: 3

  nvmem-cells:
    maxItems: 1

  nvmem-cell-names:
    const: speed_grade

  performance-domains:
    maxItems: 1

  power-domains:
    minItems: 1
    maxItems: 2

  power-domain-names:
    description:
      For PSCI based platforms, the name corresponding to the index of the PSCI
      PM domain provider, must be "psci". For SCMI based platforms, the name
      corresponding to the index of an SCMI performance domain provider, must be
      "perf".
    minItems: 1
    maxItems: 2
    items:
      enum: [ psci, perf, cpr ]

  resets:
    maxItems: 1

  arm-supply:
    deprecated: true
    description: Use 'cpu-supply' instead

  cpu0-supply:
    deprecated: true
    description: Use 'cpu-supply' instead

  mem-supply: true

  proc-supply:
    deprecated: true
    description: Use 'cpu-supply' instead

  sram-supply:
    deprecated: true
    description: Use 'mem-supply' instead

  mediatek,cci:
    $ref: /schemas/types.yaml#/definitions/phandle
    description: Link to Mediatek Cache Coherent Interconnect

  qcom,saw:
    $ref: /schemas/types.yaml#/definitions/phandle
    description:
      Specifies the SAW node associated with this CPU.

  qcom,acc:
    $ref: /schemas/types.yaml#/definitions/phandle
    description:
      Specifies the ACC node associated with this CPU.

  qcom,freq-domain:
    description: Specifies the QCom CPUFREQ HW associated with the CPU.
    $ref: /schemas/types.yaml#/definitions/phandle-array
    maxItems: 1

  rockchip,pmu:
    $ref: /schemas/types.yaml#/definitions/phandle
    description: >
      Specifies the syscon node controlling the cpu core power domains.

      Optional for systems that have an "enable-method" property value of
      "rockchip,rk3066-smp". While optional, it is the preferred way to get
      access to the cpu-core power-domains.

  secondary-boot-reg:
    $ref: /schemas/types.yaml#/definitions/uint32
    description: >
      Required for systems that have an "enable-method" property value of
      "brcm,bcm11351-cpu-method", "brcm,bcm23550" or "brcm,bcm-nsp-smp".

      This includes the following SoCs:
      BCM11130, BCM11140, BCM11351, BCM28145, BCM28155, BCM21664, BCM23550,
      BCM58522, BCM58525, BCM58535, BCM58622, BCM58623, BCM58625, BCM88312

      The secondary-boot-reg property is a u32 value that specifies the
      physical address of the register used to request the ROM holding pen
      code release a secondary CPU. The value written to the register is
      formed by encoding the target CPU id into the low bits of the
      physical start address it should jump to.

  thermal-idle:
    type: object

allOf:
  - $ref: /schemas/cpu.yaml#
  - $ref: /schemas/opp/opp-v1.yaml#
  - if:
      # If the enable-method property contains one of those values
      properties:
        enable-method:
          contains:
            enum:
              - brcm,bcm11351-cpu-method
              - brcm,bcm23550
              - brcm,bcm-nsp-smp
      # and if enable-method is present
      required:
        - enable-method
    then:
      required:
        - secondary-boot-reg
  - if:
      properties:
        enable-method:
          enum:
            - spin-table
            - renesas,r9a06g032-smp
      required:
        - enable-method
    then:
      required:
        - cpu-release-addr
  - if:
      properties:
        enable-method:
          enum:
            - qcom,kpss-acc-v1
            - qcom,kpss-acc-v2
            - qcom,msm8226-smp
            - qcom,msm8916-smp
      required:
        - enable-method
    then:
      required:
        - qcom,acc
        - qcom,saw
    else:
      if:
        # 2 Qualcomm platforms bootloaders need qcom,acc and qcom,saw yet use
        # "spin-table" or "psci" enable-methods. Disallowing the properties for
        # all other CPUs is the best we can do as there's not any way to
        # distinguish these Qualcomm platforms.
        not:
          properties:
            compatible:
              const: arm,cortex-a53
      then:
        properties:
          qcom,acc: false
          qcom,saw: false

required:
  - device_type
  - reg
  - compatible

dependencies:
  rockchip,pmu: [enable-method]

unevaluatedProperties: false

examples:
  - |
    cpus {
      #size-cells = <0>;
      #address-cells = <1>;

      cpu@0 {
        device_type = "cpu";
        compatible = "arm,cortex-a15";
        reg = <0x0>;
      };

      cpu@1 {
        device_type = "cpu";
        compatible = "arm,cortex-a15";
        reg = <0x1>;
      };

      cpu@100 {
        device_type = "cpu";
        compatible = "arm,cortex-a7";
        reg = <0x100>;
      };

      cpu@101 {
        device_type = "cpu";
        compatible = "arm,cortex-a7";
        reg = <0x101>;
      };
    };

  - |
    // Example 2 (Cortex-A8 uniprocessor 32-bit system):
    cpus {
      #size-cells = <0>;
      #address-cells = <1>;

      cpu@0 {
        device_type = "cpu";
        compatible = "arm,cortex-a8";
        reg = <0x0>;
      };
    };

  - |
    // Example 3 (ARM 926EJ-S uniprocessor 32-bit system):
    cpus {
      #size-cells = <0>;
      #address-cells = <1>;

      cpu@0 {
        device_type = "cpu";
        compatible = "arm,arm926ej-s";
        reg = <0x0>;
      };
    };

  - |
    //  Example 4 (ARM Cortex-A57 64-bit system):
    cpus {
      #size-cells = <0>;
      #address-cells = <2>;

      cpu@0 {
        device_type = "cpu";
        compatible = "arm,cortex-a57";
        reg = <0x0 0x0>;
        enable-method = "spin-table";
        cpu-release-addr = <0 0x20000000>;
      };

      cpu@1 {
        device_type = "cpu";
        compatible = "arm,cortex-a57";
        reg = <0x0 0x1>;
        enable-method = "spin-table";
        cpu-release-addr = <0 0x20000000>;
      };

      cpu@100 {
        device_type = "cpu";
        compatible = "arm,cortex-a57";
        reg = <0x0 0x100>;
        enable-method = "spin-table";
        cpu-release-addr = <0 0x20000000>;
      };

      cpu@101 {
        device_type = "cpu";
        compatible = "arm,cortex-a57";
        reg = <0x0 0x101>;
        enable-method = "spin-table";
        cpu-release-addr = <0 0x20000000>;
      };

      cpu@10000 {
        device_type = "cpu";
        compatible = "arm,cortex-a57";
        reg = <0x0 0x10000>;
        enable-method = "spin-table";
        cpu-release-addr = <0 0x20000000>;
      };

      cpu@10001 {
        device_type = "cpu";
        compatible = "arm,cortex-a57";
        reg = <0x0 0x10001>;
        enable-method = "spin-table";
        cpu-release-addr = <0 0x20000000>;
      };

      cpu@10100 {
        device_type = "cpu";
        compatible = "arm,cortex-a57";
        reg = <0x0 0x10100>;
        enable-method = "spin-table";
        cpu-release-addr = <0 0x20000000>;
      };

      cpu@10101 {
        device_type = "cpu";
        compatible = "arm,cortex-a57";
        reg = <0x0 0x10101>;
        enable-method = "spin-table";
        cpu-release-addr = <0 0x20000000>;
      };

      cpu@100000000 {
        device_type = "cpu";
        compatible = "arm,cortex-a57";
        reg = <0x1 0x0>;
        enable-method = "spin-table";
        cpu-release-addr = <0 0x20000000>;
      };

      cpu@100000001 {
        device_type = "cpu";
        compatible = "arm,cortex-a57";
        reg = <0x1 0x1>;
        enable-method = "spin-table";
        cpu-release-addr = <0 0x20000000>;
      };

      cpu@100000100 {
        device_type = "cpu";
        compatible = "arm,cortex-a57";
        reg = <0x1 0x100>;
        enable-method = "spin-table";
        cpu-release-addr = <0 0x20000000>;
      };

      cpu@100000101 {
        device_type = "cpu";
        compatible = "arm,cortex-a57";
        reg = <0x1 0x101>;
        enable-method = "spin-table";
        cpu-release-addr = <0 0x20000000>;
      };

      cpu@100010000 {
        device_type = "cpu";
        compatible = "arm,cortex-a57";
        reg = <0x1 0x10000>;
        enable-method = "spin-table";
        cpu-release-addr = <0 0x20000000>;
      };

      cpu@100010001 {
        device_type = "cpu";
        compatible = "arm,cortex-a57";
        reg = <0x1 0x10001>;
        enable-method = "spin-table";
        cpu-release-addr = <0 0x20000000>;
      };

      cpu@100010100 {
        device_type = "cpu";
        compatible = "arm,cortex-a57";
        reg = <0x1 0x10100>;
        enable-method = "spin-table";
        cpu-release-addr = <0 0x20000000>;
      };

      cpu@100010101 {
        device_type = "cpu";
        compatible = "arm,cortex-a57";
        reg = <0x1 0x10101>;
        enable-method = "spin-table";
        cpu-release-addr = <0 0x20000000>;
      };
    };
...
