# SPDX-License-Identifier: GPL-2.0
%YAML 1.2
---
$id: http://devicetree.org/schemas/arm/fsl.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: Freescale i.MX Platforms

maintainers:
  - <PERSON> <<EMAIL>>

properties:
  $nodename:
    const: '/'
  compatible:
    oneOf:
      - description: i.MX1 based Boards
        items:
          - enum:
              - armadeus,imx1-apf9328
              - fsl,imx1ads
          - const: fsl,imx1

      - description: i.MX23 based Boards
        items:
          - enum:
              - creative,x-fi3
              - fsl,imx23-evk
              - fsl,stmp378x-devb
              - olimex,imx23-olinuxino
              - sandisk,sansa_fuze_plus
          - const: fsl,imx23

      - description: i.MX25 Product Development Kit
        items:
          - enum:
              - fsl,imx25-pdk
              - karo,imx25-tx25
          - const: fsl,imx25

      - description: i.MX25 Eukrea CPUIMX25 Boards
        items:
          - enum:
              - eukrea,mbimxsd25-baseboard   # Eukrea MBIMXSD25
          - const: eukrea,cpuimx25
          - const: fsl,imx25

      - description: i.MX25 Eukrea MBIMXSD25 Boards
        items:
          - enum:
              - eukrea,mbimxsd25-baseboard-cmo-qvga
              - eukrea,mbimxsd25-baseboard-dvi-svga
              - eukrea,mbimxsd25-baseboard-dvi-vga
          - const: eukrea,mbimxsd25-baseboard
          - const: eukrea,cpuimx25
          - const: fsl,imx25

      - description: i.MX27 based Boards
        items:
          - enum:
              - armadeus,imx27-apf27      # APF27 SoM
              - fsl,imx27-pdk
          - const: fsl,imx27

      - description: i.MX27 APF27 SoM Board
        items:
          - const: armadeus,imx27-apf27dev
          - const: armadeus,imx27-apf27
          - const: fsl,imx27

      - description: i.MX27 Eukrea CPUIMX27 SoM Board
        items:
          - const: eukrea,mbimxsd27-baseboard
          - const: eukrea,cpuimx27
          - const: fsl,imx27

      - description: i.MX27 Phytec pca100 Board
        items:
          - const: phytec,imx27-pca100-rdk
          - const: phytec,imx27-pca100
          - const: fsl,imx27

      - description: i.MX27 Phytec pcm970 Board
        items:
          - const: phytec,imx27-pcm970
          - const: phytec,imx27-pcm038
          - const: fsl,imx27

      - description: i.MX28 based Boards
        items:
          - enum:
              - armadeus,imx28-apf28      # APF28 SoM
              - bluegiga,apx4devkit       # Bluegiga APx4 SoM on dev board
              - crystalfontz,cfa10036     # Crystalfontz CFA-10036 SoM
              - eukrea,mbmx28lc
              - fsl,imx28-evk
              - i2se,duckbill
              - i2se,duckbill-2
              - karo,tx28                 # Ka-Ro electronics TX28 module
              - lwn,imx28-btt3
              - lwn,imx28-xea
              - msr,m28cu3                # M28 SoM with custom base board
              - schulercontrol,imx28-sps1
              - technologic,imx28-ts4600
          - const: fsl,imx28

      - description: i.MX28 Aries M28 SoM Board
        items:
          - const: aries,m28
          - const: denx,m28
          - const: fsl,imx28

      - description: i.MX28 Aries M28EVK Board
        items:
          - const: aries,m28evk
          - const: denx,m28evk
          - const: fsl,imx28

      - description: i.MX28 Armadeus Systems APF28Dev Board
        items:
          - const: armadeus,imx28-apf28dev
          - const: armadeus,imx28-apf28
          - const: fsl,imx28

      - description: i.MX28 Crystalfontz CFA-10036 based Boards
        items:
          - enum:
              - crystalfontz,cfa10037
              - crystalfontz,cfa10049
              - crystalfontz,cfa10057
              - crystalfontz,cfa10058
          - const: crystalfontz,cfa10036
          - const: fsl,imx28

      - description: i.MX28 Crystalfontz CFA-10037 based Boards
        items:
          - enum:
              - crystalfontz,cfa10055
              - crystalfontz,cfa10056
          - const: crystalfontz,cfa10037
          - const: crystalfontz,cfa10036
          - const: fsl,imx28

      - description: i.MX28 Duckbill 2 based Boards
        items:
          - enum:
              - i2se,duckbill-2-485
              - i2se,duckbill-2-enocean
              - i2se,duckbill-2-spi
          - const: i2se,duckbill-2
          - const: fsl,imx28

      - description: i.MX28 Eukrea Electromatique MBMX283LC Board
        items:
          - const: eukrea,mbmx283lc
          - const: eukrea,mbmx28lc
          - const: fsl,imx28

      - description: i.MX28 Eukrea Electromatique MBMX287LC Board
        items:
          - const: eukrea,mbmx287lc
          - const: eukrea,mbmx283lc
          - const: eukrea,mbmx28lc
          - const: fsl,imx28

      - description: i.MX31 based Boards
        items:
          - enum:
              - buglabs,imx31-bug
              - logicpd,imx31-lite
          - const: fsl,imx31

      - description: i.MX35 based Boards
        items:
          - enum:
              - fsl,imx35-pdk
          - const: fsl,imx35

      - description: i.MX35 Eukrea CPUIMX35 Board
        items:
          - const: eukrea,mbimxsd35-baseboard
          - const: eukrea,cpuimx35
          - const: fsl,imx35

      - description: i.MX50 based Boards
        items:
          - enum:
              - fsl,imx50-evk
              - kobo,aura
          - const: fsl,imx50

      - description: i.MX51 based Boards
        items:
          - enum:
              - armadeus,imx51-apf51      # Armadeus Systems APF51 module
              - fsl,imx51-babbage
              - technologic,imx51-ts4800
              - zii,imx51-scu3-esb
              - zii,imx51-scu2-mezz
              - zii,imx51-rdu1
          - const: fsl,imx51

      - description: i.MX51 based Armadeus Systems APF51Dev Board
        items:
          - const: armadeus,imx51-apf51dev
          - const: armadeus,imx51-apf51
          - const: fsl,imx51

      - description: i.MX51 based Digi ConnectCore CC(W)-MX51 JSK Board
        items:
          - const: digi,connectcore-ccxmx51-jsk
          - const: digi,connectcore-ccxmx51-som
          - const: fsl,imx51

      - description: i.MX51 based Eukrea CPUIMX51 Board
        items:
          - const: eukrea,mbimxsd51
          - const: eukrea,cpuimx51
          - const: fsl,imx51

      - description: i.MX53 based Boards
        items:
          - enum:
              - bhf,cx9020
              - fsl,imx53-ard
              - fsl,imx53-evk
              - fsl,imx53-qsb
              - fsl,imx53-qsrb                # Freescale i.MX53 Quick Start-R Board
              - fsl,imx53-smd
              - ge,imx53-cpuvo                # General Electric CS ONE
              - inversepath,imx53-usbarmory   # Inverse Path USB armory
              - karo,tx53                     # Ka-Ro electronics TX53 module
              - kiebackpeter,imx53-ddc        # K+P imx53 DDC
              - kiebackpeter,imx53-hsc        # K+P imx53 HSC
              - menlo,m53menlo                # i.MX53 Menlo board
              - starterkit,sk-imx53
              - voipac,imx53-dmm-668          # Voipac i.MX53 X53-DMM-668
          - const: fsl,imx53

      - description: i.MX53 based Aries/DENX M53EVK Board
        items:
          - const: aries,imx53-m53evk
          - const: denx,imx53-m53evk
          - const: fsl,imx53

      - description: i.MX53 based TQ MBa53 Board
        items:
          - const: tq,mba53
          - const: tq,tqma53
          - const: fsl,imx53

      - description: i.MX6Q based Boards
        items:
          - enum:
              - auvidea,h100              # Auvidea H100
              - bosch,imx6q-acc           # Bosch ACC i.MX6 Dual
              - boundary,imx6q-nitrogen6_max
              - boundary,imx6q-nitrogen6_som2
              - boundary,imx6q-nitrogen6x
              - compulab,cm-fx6           # CompuLab CM-FX6
              - dmo,imx6q-edmqmx6         # Data Modul eDM-QMX6 Board
              - ds,imx6q-sbc              # Da Sheng COM-9XX Modules
              - embest,imx6q-marsboard    # Embest MarS Board i.MX6Dual
              - emtrion,emcon-mx6         # emCON-MX6D or emCON-MX6Q SoM
              - emtrion,emcon-mx6-avari   # emCON-MX6D or emCON-MX6Q SoM on Avari Base
              - engicam,imx6-icore        # Engicam i.CoreM6 Starter Kit
              - engicam,imx6-icore-rqs    # Engicam i.CoreM6 RQS Starter Kit
              - fsl,imx6q-arm2
              - fsl,imx6q-sabreauto
              - fsl,imx6q-sabrelite
              - fsl,imx6q-sabresd
              - karo,imx6q-tx6q           # Ka-Ro electronics TX6Q Modules
              - kiebackpeter,imx6q-tpc    # K+P i.MX6 Quad TPC Board
              - kontron,imx6q-samx6i      # Kontron i.MX6 Dual/Quad SMARC Module
              - kosagi,imx6q-novena       # Kosagi Novena Dual/Quad
              - kvg,vicut1q               # Kverneland UT1Q board
              - logicpd,imx6q-logicpd
              - lwn,display5              # Liebherr Display5 i.MX6 Quad Board
              - lwn,mccmon6               # Liebherr Monitor6 i.MX6 Quad Board
              - nutsboard,imx6q-pistachio # NutsBoard i.MX6 Quad Pistachio
              - microsys,sbc6x            # MicroSys sbc6x board
              - poslab,imx6q-savageboard  # Poslab SavageBoard Quad
              - prt,prti6q                # Protonic PRTI6Q board
              - prt,prtwd2                # Protonic WD2 board
              - rex,imx6q-rex-pro         # Rex Pro i.MX6 Quad Board
              - skov,imx6q-skov-revc-lt2  # SKOV IMX6 CPU QuadCore lt2
              - skov,imx6q-skov-revc-lt6  # SKOV IMX6 CPU QuadCore lt6
              - skov,imx6q-skov-reve-mi1010ait-1cp1 # SKOV IMX6 CPU QuadCore mi1010ait-1cp1
              - solidrun,cubox-i/q        # SolidRun Cubox-i Dual/Quad
              - solidrun,hummingboard/q
              - solidrun,hummingboard2/q
              - solidrun,solidsense/q     # SolidRun SolidSense Dual/Quad
              - tbs,imx6q-tbs2910         # TBS2910 Matrix ARM mini PC
              - technexion,imx6q-pico-dwarf   # TechNexion i.MX6Q Pico-Dwarf
              - technexion,imx6q-pico-hobbit  # TechNexion i.MX6Q Pico-Hobbit
              - technexion,imx6q-pico-nymph   # TechNexion i.MX6Q Pico-Nymph
              - technexion,imx6q-pico-pi      # TechNexion i.MX6Q Pico-Pi
              - technologic,imx6q-ts4900
              - technologic,imx6q-ts7970
              - udoo,imx6q-udoo           # Udoo i.MX6 Quad Board
              - uniwest,imx6q-evi         # Uniwest Evi
              - variscite,dt6customboard
              - wand,imx6q-wandboard      # Wandboard i.MX6 Quad Board
              - ysoft,imx6q-yapp4-crux    # i.MX6 Quad Y Soft IOTA Crux board
              - ysoft,imx6q-yapp4-pegasus # i.MX6 Quad Y Soft IOTA Pegasus board
              - zealz,imx6q-gk802         # Zealz GK802
              - zii,imx6q-zii-rdu2        # ZII RDU2 Board
          - const: fsl,imx6q

      - description: i.MX6Q Advantech DMS-BA16 Boards
        items:
          - enum:
              - advantech,imx6q-dms-ba16  # Advantech DMS-BA16
              - ge,imx6q-b450v3           # General Electric B450v3
              - ge,imx6q-b650v3           # General Electric B650v3
              - ge,imx6q-b850v3           # General Electric B850v3
          - const: advantech,imx6q-ba16
          - const: fsl,imx6q

      - description: i.MX6Q Armadeus APF6 Boards
        items:
          - const: armadeus,imx6q-apf6dev
          - const: armadeus,imx6q-apf6
          - const: fsl,imx6q

      - description: i.MX6Q CompuLab Utilite Pro Board
        items:
          - const: compulab,utilite-pro
          - const: compulab,cm-fx6
          - const: fsl,imx6q

      - description: i.MX6Q DFI FS700-M60-6QD Board
        items:
          - const: dfi,fs700-m60-6qd
          - const: dfi,fs700e-m60
          - const: fsl,imx6q

      - description: i.MX6Q DHCOM Premium Developer Kit Board
        items:
          - const: dh,imx6q-dhcom-pdk2
          - const: dh,imx6q-dhcom-som
          - const: fsl,imx6q

      - description: i.MX6Q Gateworks Ventana Boards
        items:
          - enum:
              - gw,imx6q-gw51xx
              - gw,imx6q-gw52xx
              - gw,imx6q-gw53xx
              - gw,imx6q-gw5400-a
              - gw,imx6q-gw54xx
              - gw,imx6q-gw551x
              - gw,imx6q-gw552x
              - gw,imx6q-gw553x
              - gw,imx6q-gw560x
              - gw,imx6q-gw5903
              - gw,imx6q-gw5904
              - gw,imx6q-gw5907
              - gw,imx6q-gw5910
              - gw,imx6q-gw5912
              - gw,imx6q-gw5913
          - const: gw,ventana
          - const: fsl,imx6q

      - description: i.MX6Q Kontron SMARC-sAMX6i on SMARC Eval Carrier 2.0
        items:
          - const: kontron,imx6q-samx6i-ads2
          - const: kontron,imx6q-samx6i
          - const: fsl,imx6q

      - description: i.MX6Q PHYTEC phyBOARD-Mira
        items:
          - enum:
              - phytec,imx6q-pbac06-emmc  # PHYTEC phyBOARD-Mira eMMC RDK
              - phytec,imx6q-pbac06-nand  # PHYTEC phyBOARD-Mira NAND RDK
          - const: phytec,imx6q-pbac06    # PHYTEC phyBOARD-Mira
          - const: phytec,imx6qdl-pcm058  # PHYTEC phyCORE-i.MX6
          - const: fsl,imx6q

      - description: i.MX6Q PHYTEC phyFLEX-i.MX6
        items:
          - enum:
              - comvetia,imx6q-lxr        # Comvetia LXR board
              - phytec,imx6q-pbab01       # PHYTEC phyFLEX carrier board
          - const: phytec,imx6q-pfla02    # PHYTEC phyFLEX-i.MX6 Quad
          - const: fsl,imx6q

      - description: i.MX6Q Boards with Toradex Apalis iMX6Q/D Modules
        items:
          - enum:
              - toradex,apalis_imx6q-ixora      # Apalis iMX6Q/D Module on Ixora Carrier Board
              - toradex,apalis_imx6q-ixora-v1.1 # Apalis iMX6Q/D Module on Ixora V1.1 Carrier Board
              - toradex,apalis_imx6q-ixora-v1.2 # Apalis iMX6Q/D Module on Ixora V1.2 Carrier Board
              - toradex,apalis_imx6q-eval       # Apalis iMX6Q/D Module on Apalis Evaluation Board v1.0/v1.1
              - toradex,apalis_imx6q-eval-v1.2  # Apalis iMX6Q/D Module on Apalis Evaluation Board v1.2
          - const: toradex,apalis_imx6q
          - const: fsl,imx6q

      - description: i.MX6Q Variscite VAR-SOM-MX6 Boards
        items:
          - const: variscite,mx6customboard
          - const: variscite,var-som-imx6q
          - const: fsl,imx6q

      - description: TQ-Systems TQMa6Q SoM (variant A) on MBa6x
        items:
          - const: tq,imx6q-mba6x-a
          - const: tq,mba6a               # Expected by bootloader, to be removed in the future
          - const: tq,imx6q-tqma6q-a
          - const: fsl,imx6q

      - description: TQ-Systems TQMa6Q SoM (variant B) on MBa6x
        items:
          - const: tq,imx6q-mba6x-b
          - const: tq,mba6b               # Expected by bootloader, to be removed in the future
          - const: tq,imx6q-tqma6q-b
          - const: fsl,imx6q

      - description: i.MX6QP based Boards
        items:
          - enum:
              - boundary,imx6qp-nitrogen6_max
              - boundary,imx6qp-nitrogen6_som2
              - fsl,imx6qp-sabreauto      # i.MX6 Quad Plus SABRE Automotive Board
              - fsl,imx6qp-sabresd        # i.MX6 Quad Plus SABRE Smart Device Board
              - karo,imx6qp-tx6qp         # Ka-Ro electronics TX6QP-8037 Module
              - kvg,vicutp                # Kverneland UT1P board
              - prt,prtwd3                # Protonic WD3 board
              - wand,imx6qp-wandboard     # Wandboard i.MX6 QuadPlus Board
              - ysoft,imx6qp-yapp4-crux-plus  # i.MX6 Quad Plus Y Soft IOTA Crux+ board
              - ysoft,imx6qp-yapp4-pegasus-plus # i.MX6 Quad Plus Y Soft IOTA Pegasus+ board
              - zii,imx6qp-zii-rdu2       # ZII RDU2+ Board
          - const: fsl,imx6qp

      - description: i.MX6QP PHYTEC phyBOARD-Mira
        items:
          - const: phytec,imx6qp-pbac06-nand
          - const: phytec,imx6qp-pbac06   # PHYTEC phyBOARD-Mira
          - const: phytec,imx6qdl-pcm058  # PHYTEC phyCORE-i.MX6
          - const: fsl,imx6qp

      - description: TQ-Systems TQMa6QP SoM on MBa6x
        items:
          - const: tq,imx6qp-mba6x-b
          - const: tq,mba6b               # Expected by bootloader, to be removed in the future
          - const: tq,imx6qp-tqma6qp-b
          - const: fsl,imx6qp

      - description: i.MX6DL based Boards
        items:
          - enum:
              - abb,aristainetos-imx6dl-4     # aristainetos i.MX6 Dual Lite Board 4
              - abb,aristainetos-imx6dl-7     # aristainetos i.MX6 Dual Lite Board 7
              - abb,aristainetos2-imx6dl-4    # aristainetos2 i.MX6 Dual Lite Board 4
              - abb,aristainetos2-imx6dl-7    # aristainetos2 i.MX6 Dual Lite Board 7
              - alt,alti6p                    # Altesco I6P Board
              - boundary,imx6dl-nit6xlite     # Boundary Devices Nitrogen6 Lite
              - boundary,imx6dl-nitrogen6x    # Boundary Devices Nitrogen6x
              - bticino,imx6dl-mamoj      # BTicino i.MX6DL Mamoj
              - eckelmann,imx6dl-ci4x10
              - emtrion,emcon-mx6         # emCON-MX6S or emCON-MX6DL SoM
              - emtrion,emcon-mx6-avari   # emCON-MX6S or emCON-MX6DL SoM on Avari Base
              - engicam,imx6-icore        # Engicam i.CoreM6 Starter Kit
              - engicam,imx6-icore-rqs    # Engicam i.CoreM6 RQS Starter Kit
              - fsl,imx6dl-sabreauto      # i.MX6 DualLite/Solo SABRE Automotive Board
              - fsl,imx6dl-sabrelite      # i.MX6 DualLite SABRE Lite Board
              - fsl,imx6dl-sabresd        # i.MX6 DualLite SABRE Smart Device Board
              - karo,imx6dl-tx6dl         # Ka-Ro electronics TX6U Modules
              - kontron,imx6dl-samx6i     # Kontron i.MX6 Solo SMARC Module
              - kvg,victgo                # Kverneland TGO
              - kvg,vicut1                # Kverneland UT1 board
              - ply,plybas                # Plymovent BAS board
              - ply,plym2m                # Plymovent M2M board
              - poslab,imx6dl-savageboard # Poslab SavageBoard Dual
              - prt,prtmvt                # Protonic MVT board
              - prt,prtrvt                # Protonic RVT board
              - prt,prtvt7                # Protonic VT7 board
              - rex,imx6dl-rex-basic      # Rex Basic i.MX6 Dual Lite Board
              - riot,imx6s-riotboard      # RIoTboard i.MX6S
              - sielaff,imx6dl-board      # Sielaff i.MX6 Solo Board
              - skov,imx6dl-skov-revc-lt2 # SKOV IMX6 CPU SoloCore lt2
              - skov,imx6dl-skov-revc-lt6 # SKOV IMX6 CPU SoloCore lt6
              - solidrun,cubox-i/dl            # SolidRun Cubox-i Solo/DualLite
              - solidrun,hummingboard/dl
              - solidrun,hummingboard2/dl      # SolidRun HummingBoard2 Solo/DualLite
              - solidrun,solidsense/dl         # SolidRun SolidSense Solo/DualLite
              - technexion,imx6dl-pico-dwarf   # TechNexion i.MX6DL Pico-Dwarf
              - technexion,imx6dl-pico-hobbit  # TechNexion i.MX6DL Pico-Hobbit
              - technexion,imx6dl-pico-nymph   # TechNexion i.MX6DL Pico-Nymph
              - technexion,imx6dl-pico-pi      # TechNexion i.MX6DL Pico-Pi
              - technologic,imx6dl-ts4900
              - technologic,imx6dl-ts7970
              - udoo,imx6dl-udoo          # Udoo i.MX6 Dual-lite Board
              - vdl,lanmcu                # Van der Laan LANMCU board
              - wand,imx6dl-wandboard     # Wandboard i.MX6 Dual Lite Board
              - ysoft,imx6dl-yapp4-draco  # i.MX6 Solo Y Soft IOTA Draco board
              - ysoft,imx6dl-yapp4-hydra  # i.MX6 DualLite Y Soft IOTA Hydra board
              - ysoft,imx6dl-yapp4-lynx   # i.MX6 DualLite Y Soft IOTA Lynx board
              - ysoft,imx6dl-yapp4-orion  # i.MX6 DualLite Y Soft IOTA Orion board
              - ysoft,imx6dl-yapp4-phoenix  # i.MX6 DualLite Y Soft IOTA Phoenix board
              - ysoft,imx6dl-yapp4-ursa   # i.MX6 Solo Y Soft IOTA Ursa board
          - const: fsl,imx6dl

      - description: i.MX6DL based Armadeus AFP6 Board
        items:
          - const: armadeus,imx6dl-apf6dev
          - const: armadeus,imx6dl-apf6         # APF6 (Solo) SoM
          - const: fsl,imx6dl

      - description: i.MX6DL based congatec QMX6 Boards
        items:
          - enum:
              - ge,imx6dl-b105v2          # General Electric B105v2
              - ge,imx6dl-b105pv2         # General Electric B105Pv2
              - ge,imx6dl-b125v2          # General Electric B125v2
              - ge,imx6dl-b125pv2         # General Electric B125Pv2
              - ge,imx6dl-b155v2          # General Electric B155v2
          - const: congatec,qmx6
          - const: fsl,imx6dl

      - description: i.MX6DL based DFI FS700-M60-6DL Board
        items:
          - const: dfi,fs700-m60-6dl
          - const: dfi,fs700e-m60
          - const: fsl,imx6dl

      - description: i.MX6DL DHCOM based Boards
        items:
          - enum:
              - dh,imx6dl-dhcom-pdk2         # i.MX6DL DHCOM SoM on PDK2 board
              - dh,imx6dl-dhcom-picoitx      # i.MX6DL DHCOM SoM on PicoITX board
          - const: dh,imx6dl-dhcom-som
          - const: fsl,imx6dl

      - description: i.MX6DL Gateworks Ventana Boards
        items:
          - enum:
              - gw,imx6dl-gw51xx
              - gw,imx6dl-gw52xx
              - gw,imx6dl-gw53xx
              - gw,imx6dl-gw54xx
              - gw,imx6dl-gw551x
              - gw,imx6dl-gw552x
              - gw,imx6dl-gw553x
              - gw,imx6dl-gw560x
              - gw,imx6dl-gw5903
              - gw,imx6dl-gw5904
              - gw,imx6dl-gw5907
              - gw,imx6dl-gw5910
              - gw,imx6dl-gw5912
              - gw,imx6dl-gw5913
          - const: gw,ventana
          - const: fsl,imx6dl

      - description: i.MX6DL Kontron SMARC-sAMX6i on SMARC Eval Carrier 2.0
        items:
          - const: kontron,imx6dl-samx6i-ads2
          - const: kontron,imx6dl-samx6i
          - const: fsl,imx6dl

      - description: i.MX6DL PHYTEC phyBOARD-Mira
        items:
          - enum:
              - phytec,imx6dl-pbac06-emmc # PHYTEC phyBOARD-Mira eMMC RDK
              - phytec,imx6dl-pbac06-nand # PHYTEC phyBOARD-Mira NAND RDK
          - const: phytec,imx6dl-pbac06   # PHYTEC phyBOARD-Mira
          - const: phytec,imx6qdl-pcm058  # PHYTEC phyCORE-i.MX6
          - const: fsl,imx6dl

      - description: i.MX6DL PHYTEC phyFLEX-i.MX6
        items:
          - const: phytec,imx6dl-pbab01   # PHYTEC phyFLEX carrier board
          - const: phytec,imx6dl-pfla02   # PHYTEC phyFLEX-i.MX6 Quad
          - const: fsl,imx6dl

      - description: i.MX6DL Boards with Toradex Colibri iMX6DL/S Modules
        items:
          - enum:
              - toradex,colibri_imx6dl-aster        # Colibri iMX6DL/S Module on Aster Board
              - toradex,colibri_imx6dl-eval-v3      # Colibri iMX6DL/S Module on Colibri Evaluation Board V3
              - toradex,colibri_imx6dl-iris         # Colibri iMX6DL/S Module on Iris Board
              - toradex,colibri_imx6dl-iris-v2      # Colibri iMX6DL/S Module on Iris Board V2
          - const: toradex,colibri_imx6dl           # Colibri iMX6DL/S Module
          - const: fsl,imx6dl

      - description: i.MX6S DHCOM DRC02 Board
        items:
          - const: dh,imx6s-dhcom-drc02
          - const: dh,imx6s-dhcom-som
          - const: fsl,imx6dl

      - description: TQ-Systems TQMa6DL SoM (variant A) on MBa6x
        items:
          - const: tq,imx6dl-mba6x-a
          - const: tq,mba6a               # Expected by bootloader, to be removed in the future
          - const: tq,imx6dl-tqma6dl-a
          - const: fsl,imx6dl

      - description: TQ-Systems TQMa6DL SoM (variant B) on MBa6x
        items:
          - const: tq,imx6dl-mba6x-b
          - const: tq,mba6b               # Expected by bootloader, to be removed in the future
          - const: tq,imx6dl-tqma6dl-b
          - const: fsl,imx6dl

      - description: i.MX6SL based Boards
        items:
          - enum:
              - fsl,imx6sl-evk            # i.MX6 SoloLite EVK Board
              - kobo,aura2
              - kobo,tolino-shine2hd
              - kobo,tolino-shine3
              - kobo,tolino-vision
              - kobo,tolino-vision5
              - revotics,imx6sl-warp      # Revotics WaRP Board
          - const: fsl,imx6sl

      - description: i.MX6SLL based Boards
        items:
          - enum:
              - fsl,imx6sll-evk
              - kobo,clarahd
              - kobo,librah2o
          - const: fsl,imx6sll

      - description: i.MX6SLL Kobo Clara 2e Rev. A/B
        items:
          - enum:
              - kobo,clara2e-a
              - kobo,clara2e-b
          - const: kobo,clara2e
          - const: fsl,imx6sll

      - description: i.MX6SX based Boards
        items:
          - enum:
              - boundary,imx6sx-nitrogen6sx
              - fsl,imx6sx-sabreauto      # i.MX6 SoloX Sabre Auto Board
              - fsl,imx6sx-sdb            # i.MX6 SoloX SDB Board
              - fsl,imx6sx-sdb-reva       # i.MX6 SoloX SDB Rev-A Board
              - samtec,imx6sx-vining-2000 # Softing VIN|ING 2000 Board
              - udoo,neobasic             # UDOO Neo Basic Board
              - udoo,neoextended          # UDOO Neo Extended
              - udoo,neofull              # UDOO Neo Full
          - const: fsl,imx6sx

      - description: i.MX6UL based Boards
        items:
          - enum:
              - engicam,imx6ul-geam       # Engicam GEAM6UL Starter Kit
              - engicam,imx6ul-isiot      # Engicam Is.IoT MX6UL eMMC/NAND Starter kit
              - fsl,imx6ul-14x14-evk      # i.MX6 UltraLite 14x14 EVK Board
              - karo,imx6ul-tx6ul         # Ka-Ro electronics TXUL-0010 Module
              - kontron,sl-imx6ul         # Kontron SL i.MX6UL SoM
              - prt,prti6g                # Protonic PRTI6G Board
              - technexion,imx6ul-pico-dwarf   # TechNexion i.MX6UL Pico-Dwarf
              - technexion,imx6ul-pico-hobbit  # TechNexion i.MX6UL Pico-Hobbit
              - technexion,imx6ul-pico-pi      # TechNexion i.MX6UL Pico-Pi
          - const: fsl,imx6ul

      - description: i.MX6UL Armadeus Systems OPOS6UL SoM Board
        items:
          - const: armadeus,imx6ul-opos6uldev   # OPOS6UL (i.MX6UL) SoM on OPOS6ULDev board
          - const: armadeus,imx6ul-opos6ul      # OPOS6UL (i.MX6UL) SoM
          - const: fsl,imx6ul

      - description: i.MX6UL Digi International ConnectCore 6UL Boards
        items:
          - enum:
              - digi,ccimx6ulsbcexpress   # Digi International ConnectCore 6UL SBC Express
              - digi,ccimx6ulsbcpro       # Digi International ConnectCore 6UL SBC Pro
          - const: digi,ccimx6ulsom
          - const: fsl,imx6ul

      - description: i.MX6UL Grinn liteBoard
        items:
          - const: grinn,imx6ul-liteboard
          - const: grinn,imx6ul-litesom
          - const: fsl,imx6ul

      - description: i.MX6UL PHYTEC phyBOARD-Segin
        items:
          - enum:
              - phytec,imx6ul-pbacd10-emmc
              - phytec,imx6ul-pbacd10-nand
          - const: phytec,imx6ul-pbacd10  # PHYTEC phyBOARD-Segin with i.MX6 UL
          - const: phytec,imx6ul-pcl063   # PHYTEC phyCORE-i.MX 6UL
          - const: fsl,imx6ul

      - description: i.MX6UL Variscite VAR-SOM-MX6 Boards
        items:
          - const: variscite,mx6ulconcerto
          - const: variscite,var-som-imx6ul
          - const: fsl,imx6ul

      - description: Kontron BL i.MX6UL (N631X S) Board
        items:
          - const: kontron,bl-imx6ul       # Kontron BL i.MX6UL Carrier Board
          - const: kontron,sl-imx6ul       # Kontron SL i.MX6UL SoM
          - const: fsl,imx6ul

      - description: Kontron BL i.MX6UL 43 (N631X S 43) Board
        items:
          - const: kontron,bl-imx6ul-43    # Kontron BL i.MX6UL Carrier Board with 4.3" Display
          - const: kontron,bl-imx6ul       # Kontron BL i.MX6UL Carrier Board
          - const: kontron,sl-imx6ul       # Kontron SL i.MX6UL SoM
          - const: fsl,imx6ul

      - description: TQ-Systems TQMa6UL1 SoM on MBa6ULx board
        items:
          - enum:
              - tq,imx6ul-tqma6ul1-mba6ulx
          - const: tq,imx6ul-tqma6ul1      # MCIMX6G1
          - const: fsl,imx6ul

      - description: TQ-Systems TQMa6UL2 SoM on MBa6ULx board
        items:
          - enum:
              - tq,imx6ul-tqma6ul2-mba6ulx
          - const: tq,imx6ul-tqma6ul2      # MCIMX6G2
          - const: fsl,imx6ul

      - description: TQ-Systems TQMa6ULxL SoM on MBa6ULx[L] board
        items:
          - enum:
              - tq,imx6ul-tqma6ul2l-mba6ulx # using LGA adapter
              - tq,imx6ul-tqma6ul2l-mba6ulxl
          - const: tq,imx6ul-tqma6ul2l      # MCIMX6G2, LGA SoM variant
          - const: fsl,imx6ul

      - description: i.MX6ULL based Boards
        items:
          - enum:
              - fsl,imx6ull-14x14-evk     # i.MX6 UltraLiteLite 14x14 EVK Board
              - joz,jozacp                # JOZ Access Point
              - kontron,sl-imx6ull        # Kontron SL i.MX6ULL SoM
              - myir,imx6ull-mys-6ulx-eval # MYiR Tech iMX6ULL Evaluation Board
              - uni-t,uti260b             # UNI-T UTi260B Thermal Camera
          - const: fsl,imx6ull

      - description: i.MX6ULL Armadeus Systems OPOS6ULDev Board
        items:
          - const: armadeus,imx6ull-opos6uldev  # OPOS6UL (i.MX6ULL) SoM on OPOS6ULDev board
          - const: armadeus,imx6ull-opos6ul     # OPOS6UL (i.MX6ULL) SoM
          - const: fsl,imx6ull

      - description: i.MX6ULL chargebyte Tarragon Boards
        items:
          - enum:
              - chargebyte,imx6ull-tarragon-master
              - chargebyte,imx6ull-tarragon-micro
              - chargebyte,imx6ull-tarragon-slave
              - chargebyte,imx6ull-tarragon-slavext
          - const: fsl,imx6ull

      - description: i.MX6ULL DHCOM SoM based Boards
        items:
          - enum:
              - dh,imx6ull-dhcom-drc02
              - dh,imx6ull-dhcom-pdk2
              - dh,imx6ull-dhcom-picoitx
          - const: dh,imx6ull-dhcom-som # The DHCOR is soldered on the DHCOM
          - const: dh,imx6ull-dhcor-som
          - const: fsl,imx6ull

      - description: i.MX6ULL DHCOR SoM based Boards
        items:
          - const: marantec,imx6ull-dhcor-maveo-box
          - const: dh,imx6ull-dhcor-som
          - const: fsl,imx6ull

      - description: i.MX6ULL PHYTEC phyBOARD-Segin
        items:
          - enum:
              - phytec,imx6ull-pbacd10-emmc
              - phytec,imx6ull-pbacd10-nand
          - const: phytec,imx6ull-pbacd10 # PHYTEC phyBOARD-Segin with i.MX6 ULL
          - const: phytec,imx6ull-pcl063  # PHYTEC phyCORE-i.MX 6ULL
          - const: fsl,imx6ull

      - description: i.MX6ULL PHYTEC phyGATE-Tauri
        items:
          - enum:
              - phytec,imx6ull-phygate-tauri-emmc
              - phytec,imx6ull-phygate-tauri-nand
          - const: phytec,imx6ull-phygate-tauri # PHYTEC phyGATE-Tauri with i.MX6 ULL
          - const: phytec,imx6ull-pcl063        # PHYTEC phyCORE-i.MX 6ULL
          - const: fsl,imx6ull

      - description: i.MX6ULL Boards with Toradex Colibri iMX6ULL Modules
        items:
          - enum:
              - toradex,colibri-imx6ull-aster     # Aster Carrier Board
              - toradex,colibri-imx6ull-eval      # Colibri Evaluation Board V3
              - toradex,colibri-imx6ull-iris      # Iris Carrier Board
              - toradex,colibri-imx6ull-iris-v2   # Iris V2 Carrier Board
          - const: toradex,colibri-imx6ull        # Colibri iMX6ULL Module
          - const: fsl,imx6ull

      - description: i.MX6ULL Boards with Toradex Colibri iMX6ULL 1GB (eMMC) Module
        items:
          - enum:
              - toradex,colibri-imx6ull-emmc-aster     # Aster Carrier Board
              - toradex,colibri-imx6ull-emmc-eval      # Colibri Evaluation B. V3
              - toradex,colibri-imx6ull-emmc-iris      # Iris Carrier Board
              - toradex,colibri-imx6ull-emmc-iris-v2   # Iris V2 Carrier Board
          - const: toradex,colibri-imx6ull-emmc        # Colibri iMX6ULL 1GB (eMMC) Module
          - const: fsl,imx6ull

      - description: i.MX6ULL Boards with Toradex Colibri iMX6ULL Wi-Fi / BT Modules
        items:
          - enum:
              - toradex,colibri-imx6ull-wifi-eval     # Colibri Eval. B. V3
              - toradex,colibri-imx6ull-wifi-aster    # Aster Carrier Board
              - toradex,colibri-imx6ull-wifi-iris     # Iris Carrier Board
              - toradex,colibri-imx6ull-wifi-iris-v2  # Iris V2 Carrier Board
          - const: toradex,colibri-imx6ull-wifi       # Colibri iMX6ULL Wi-Fi / BT Module
          - const: fsl,imx6ull

      - description: Kontron BL i.MX6ULL (N6411 S) Board
        items:
          - const: kontron,bl-imx6ull   # Kontron BL i.MX6ULL Carrier Board
          - const: kontron,sl-imx6ull   # Kontron SL i.MX6ULL SoM
          - const: fsl,imx6ull

      - description: TQ-Systems TQMa6ULLx SoM on MBa6ULx board
        items:
          - enum:
              - tq,imx6ull-tqma6ull2-mba6ulx # TQMa6ULL socketable SoM with MCIMX6Y2 on MBa6ULx EVK
          - const: tq,imx6ull-tqma6ull2      # TQMa6ULL socketable SoM with MCIMX6Y2
          - const: fsl,imx6ull

      - description: TQ-Systems TQMa6ULLxL SoM on MBa6ULx[L] board
        items:
          - enum:
              - tq,imx6ull-tqma6ull2l-mba6ulx  # TQMa6ULLxL LGA SoM with socketable Adapter on MBa6ULx EVK
              - tq,imx6ull-tqma6ull2l-mba6ulxl # TQMa6ULLxL LGA SoM on MBa6ULxL gateway board
          - const: tq,imx6ull-tqma6ull2l       # TQMa6ULLxL LGA SoM with MCIMX6Y2
          - const: fsl,imx6ull

      - description: Seeed Stuido i.MX6ULL SoM on dev boards
        items:
          - enum:
              - seeed,imx6ull-seeed-npi-emmc
              - seeed,imx6ull-seeed-npi-nand
          - const: seeed,imx6ull-seeed-npi
          - const: fsl,imx6ull

      - description: i.MX6ULZ based Boards
        items:
          - enum:
              - bsh,imx6ulz-bsh-smm-m2    # i.MX6 ULZ BSH SystemMaster
              - fsl,imx6ulz-14x14-evk     # i.MX6 ULZ 14x14 EVK Board
          - const: fsl,imx6ull # This seems odd. Should be last?
          - const: fsl,imx6ulz

      - description: i.MX7S based Boards
        items:
          - enum:
              - element14,imx7s-warp      # Element14 Warp i.MX7 Board
              - toradex,colibri-imx7s     # Colibri iMX7S Module
          - const: fsl,imx7s

      - description: i.MX7S Boards with Toradex Colibri iMX7S Module
        items:
          - enum:
              - toradex,colibri-imx7s-aster     # Module on Aster Carrier Board
              - toradex,colibri-imx7s-eval-v3   # Module on Colibri Evaluation Board V3
              - toradex,colibri-imx7s-iris      # Module on Iris Carrier Board
              - toradex,colibri-imx7s-iris-v2   # Module on Iris Carrier Board V2
          - const: toradex,colibri-imx7s
          - const: fsl,imx7s

      - description: TQ-Systems TQMa7S SoM on MBa7x board
        items:
          - const: tq,imx7s-mba7
          - const: tq,imx7s-tqma7
          - const: fsl,imx7s

      - description: i.MX7D based Boards
        items:
          - enum:
              - boundary,imx7d-nitrogen7
              - compulab,cl-som-imx7      # CompuLab CL-SOM-iMX7
              - fsl,imx7d-sdb             # i.MX7 SabreSD Board
              - fsl,imx7d-sdb-reva        # i.MX7 SabreSD Rev-A Board
              - kam,imx7d-flex-concentrator       # Kamstrup OMNIA Flex Concentrator
              - kam,imx7d-flex-concentrator-mfg   # Kamstrup OMNIA Flex Concentrator in manufacturing mode
              - novtech,imx7d-meerkat96   # i.MX7 Meerkat96 Board
              - remarkable,imx7d-remarkable2  # i.MX7D ReMarkable 2 E-Ink Tablet
              - storopack,imx7d-smegw01       # Storopack i.MX7D SMEGW01
              - technexion,imx7d-pico-dwarf   # TechNexion i.MX7D Pico-Dwarf
              - technexion,imx7d-pico-hobbit  # TechNexion i.MX7D Pico-Hobbit
              - technexion,imx7d-pico-nymph   # TechNexion i.MX7D Pico-Nymph
              - technexion,imx7d-pico-pi      # TechNexion i.MX7D Pico-Pi
              - zii,imx7d-rmu2            # ZII RMU2 Board
              - zii,imx7d-rpu2            # ZII RPU2 Board
          - const: fsl,imx7d

      - description: TQ-Systems TQMa7D SoM on MBa7x board
        items:
          - const: tq,imx7d-mba7
          - const: tq,imx7d-tqma7
          - const: fsl,imx7d

      - description:
          Compulab SBC-iMX7 is a single board computer based on the
          Freescale i.MX7 system-on-chip. SBC-iMX7 is implemented with
          the CL-SOM-iMX7 System-on-Module providing most of the functions,
          and SB-SOM-iMX7 carrier board providing additional peripheral
          functions and connectors.
        items:
          - const: compulab,sbc-imx7
          - const: compulab,cl-som-imx7
          - const: fsl,imx7d

      - description: i.MX7D Boards with Toradex Colibri i.MX7D Module
        items:
          - enum:
              - toradex,colibri-imx7d-aster   # Aster Carrier Board
              - toradex,colibri-imx7d-eval-v3 # Colibri Evaluation Board V3
              - toradex,colibri-imx7d-iris    # Iris Carrier Board
              - toradex,colibri-imx7d-iris-v2 # Iris Carrier Board V2
          - const: toradex,colibri-imx7d
          - const: fsl,imx7d

      - description: i.MX7D Boards with Toradex Colibri i.MX7D 1GB (eMMC) Module
        items:
          - enum:
              - toradex,colibri-imx7d-emmc-aster    # Module on Aster Carrier Board
              - toradex,colibri-imx7d-emmc-eval-v3  # Module on Colibri Evaluation Board V3
              - toradex,colibri-imx7d-emmc-iris     # Module on Iris Carrier Board
              - toradex,colibri-imx7d-emmc-iris-v2  # Module on Iris Carrier Board V2
          - const: toradex,colibri-imx7d-emmc
          - const: fsl,imx7d

      - description: i.MX7ULP based Boards
        items:
          - enum:
              - ea,imx7ulp-com           # i.MX7ULP Embedded Artists COM Board
              - fsl,imx7ulp-evk           # i.MX7ULP Evaluation Kit
          - const: fsl,imx7ulp

      - description: i.MX8MM based Boards
        items:
          - enum:
              - beacon,imx8mm-beacon-kit  # i.MX8MM Beacon Development Kit
              - boundary,imx8mm-nitrogen8mm  # i.MX8MM Nitrogen Board
              - dmo,imx8mm-data-modul-edm-sbc # i.MX8MM eDM SBC
              - emtrion,emcon-mx8mm-avari # emCON-MX8MM SoM on Avari Base
              - fsl,imx8mm-ddr4-evk       # i.MX8MM DDR4 EVK Board
              - fsl,imx8mm-evk            # i.MX8MM EVK Board
              - fsl,imx8mm-evkb           # i.MX8MM EVKB Board
              - gateworks,imx8mm-gw75xx-0x # i.MX8MM Gateworks Board
              - gateworks,imx8mm-gw7904
              - gw,imx8mm-gw71xx-0x       # i.MX8MM Gateworks Development Kit
              - gw,imx8mm-gw72xx-0x       # i.MX8MM Gateworks Development Kit
              - gw,imx8mm-gw73xx-0x       # i.MX8MM Gateworks Development Kit
              - gw,imx8mm-gw7901          # i.MX8MM Gateworks Board
              - gw,imx8mm-gw7902          # i.MX8MM Gateworks Board
              - gw,imx8mm-gw7903          # i.MX8MM Gateworks Board
              - innocomm,wb15-evk         # i.MX8MM Innocomm EVK board with WB15 SoM
              - kontron,imx8mm-sl         # i.MX8MM Kontron SL (N801X) SOM
              - kontron,imx8mm-osm-s      # i.MX8MM Kontron OSM-S (N802X) SOM
              - prt,prt8mm                # i.MX8MM Protonic PRT8MM Board
          - const: fsl,imx8mm

      - description: Compulab i.MX8MM UCM SoM based boards
        items:
          - enum:
              - compulab,imx8mm-iot-gateway     # i.MX8MM Compulab IoT-Gateway
          - const: compulab,imx8mm-ucm-som      # i.MX8MM Compulab UCM SoM
          - const: fsl,imx8mm

      - description: Emtop i.MX8MM based Boards
        items:
          - const: ees,imx8mm-emtop-baseboard      # i.MX8MM Emtop SoM on i.MX8M Mini Baseboard V1
          - const: ees,imx8mm-emtop-som            # i.MX8MM Emtop SOM-IMX8MMLPD4 module
          - const: fsl,imx8mm

      - description: Engicam i.Core MX8M Mini SoM based boards
        items:
          - enum:
              - engicam,icore-mx8mm-ctouch2        # i.MX8MM Engicam i.Core MX8M Mini C.TOUCH 2.0
              - engicam,icore-mx8mm-edimm2.2       # i.MX8MM Engicam i.Core MX8M Mini EDIMM2.2 Starter Kit
          - const: engicam,icore-mx8mm             # i.MX8MM Engicam i.Core MX8M Mini SoM
          - const: fsl,imx8mm

      - description: Kontron BL i.MX8MM (N801X S) Board
        items:
          - const: kontron,imx8mm-bl
          - const: kontron,imx8mm-sl
          - const: fsl,imx8mm

      - description: Kontron BL i.MX8MM OSM-S (N802X S) Board
        items:
          - const: kontron,imx8mm-bl-osm-s
          - const: kontron,imx8mm-osm-s
          - const: fsl,imx8mm

      - description: Toradex Boards with Verdin iMX8M Mini Modules
        items:
          - enum:
              - menlo,mx8menlo                       # Verdin iMX8M Mini Module on i.MX8MM Menlo board
              - toradex,verdin-imx8mm-nonwifi-dahlia # Verdin iMX8M Mini Module on Dahlia
              - toradex,verdin-imx8mm-nonwifi-dev    # Verdin iMX8M Mini Module on Verdin Development Board
              - toradex,verdin-imx8mm-nonwifi-ivy    # Verdin iMX8M Mini Module on Ivy
              - toradex,verdin-imx8mm-nonwifi-mallow # Verdin iMX8M Mini Module on Mallow
              - toradex,verdin-imx8mm-nonwifi-yavia  # Verdin iMX8M Mini Module on Yavia
          - const: toradex,verdin-imx8mm-nonwifi     # Verdin iMX8M Mini Module without Wi-Fi / BT
          - const: toradex,verdin-imx8mm             # Verdin iMX8M Mini Module
          - const: fsl,imx8mm

      - description: Toradex Boards with Verdin iMX8M Mini Wi-Fi / BT Modules
        items:
          - enum:
              - toradex,verdin-imx8mm-wifi-dahlia # Verdin iMX8M Mini Wi-Fi / BT Module on Dahlia
              - toradex,verdin-imx8mm-wifi-dev    # Verdin iMX8M Mini Wi-Fi / BT M. on Verdin Development B.
              - toradex,verdin-imx8mm-wifi-ivy    # Verdin iMX8M Mini Wi-Fi / BT Module on Ivy
              - toradex,verdin-imx8mm-wifi-mallow # Verdin iMX8M Mini Wi-Fi / BT Module on Mallow
              - toradex,verdin-imx8mm-wifi-yavia  # Verdin iMX8M Mini Wi-Fi / BT Module on Yavia
          - const: toradex,verdin-imx8mm-wifi     # Verdin iMX8M Mini Wi-Fi / BT Module
          - const: toradex,verdin-imx8mm          # Verdin iMX8M Mini Module
          - const: fsl,imx8mm

      - description: PHYTEC phyCORE-i.MX8MM SoM based boards
        items:
          - enum:
              - phytec,imx8mm-phyboard-polis-rdk # phyBOARD-Polis RDK
              - phytec,imx8mm-phygate-tauri-l    # phyGATE-Tauri-L Gateway
          - const: phytec,imx8mm-phycore-som        # phyCORE-i.MX8MM SoM
          - const: fsl,imx8mm

      - description: Variscite VAR-SOM-MX8MM based boards
        items:
          - const: variscite,var-som-mx8mm-symphony
          - const: variscite,var-som-mx8mm
          - const: fsl,imx8mm

      - description:
          TQMa8MxML is a series of SOM featuring NXP i.MX8MM system-on-chip
          variants. It is designed to be soldered on different carrier boards.
          All variants (TQMa8M[Q,D,S][L]ML) use the same device tree, hence only
          one compatible is needed.
        items:
          - enum:
              - cloos,imx8mm-phg           # i.MX8MM Cloos PHG Board
              - tq,imx8mm-tqma8mqml-mba8mx # TQ-Systems GmbH i.MX8MM TQMa8MQML SOM on MBa8Mx
          - const: tq,imx8mm-tqma8mqml     # TQ-Systems GmbH i.MX8MM TQMa8MQML SOM
          - const: fsl,imx8mm

      - description: i.MX8MN based Boards
        items:
          - enum:
              - beacon,imx8mn-beacon-kit  # i.MX8MN Beacon Development Kit
              - bsh,imx8mn-bsh-smm-s2     # i.MX8MN BSH SystemMaster S2
              - bsh,imx8mn-bsh-smm-s2pro  # i.MX8MN BSH SystemMaster S2 PRO
              - fsl,imx8mn-ddr3l-evk      # i.MX8MN DDR3L EVK Board
              - fsl,imx8mn-ddr4-evk       # i.MX8MN DDR4 EVK Board
              - fsl,imx8mn-evk            # i.MX8MN LPDDR4 EVK Board
              - gw,imx8mn-gw7902          # i.MX8MM Gateworks Board
          - const: fsl,imx8mn

      - description: Variscite VAR-SOM-MX8MN based boards
        items:
          - enum:
              - dimonoff,gateway-evk # i.MX8MN Dimonoff Gateway EVK Board
              - rve,gateway # i.MX8MN RVE Gateway Board
              - variscite,var-som-mx8mn-symphony
          - const: variscite,var-som-mx8mn
          - const: fsl,imx8mn

      - description:
          TQMa8MxNL is a series of SOM featuring NXP i.MX8MN system-on-chip
          variants. It is designed to be soldered on different carrier boards.
          All variants (TQMa8M[Q,D,S][L]NL) use the same device tree, hence only
          one compatible is needed.
        items:
          - enum:
              - tq,imx8mn-tqma8mqnl-mba8mx # TQ-Systems GmbH i.MX8MN TQMa8MQNL SOM on MBa8Mx
          - const: tq,imx8mn-tqma8mqnl     # TQ-Systems GmbH i.MX8MN TQMa8MQNL SOM
          - const: fsl,imx8mn

      - description: i.MX8MP based Boards
        items:
          - enum:
              - beacon,imx8mp-beacon-kit  # i.MX8MP Beacon Development Kit
              - dmo,imx8mp-data-modul-edm-sbc # i.MX8MP eDM SBC
              - emcraft,imx8mp-navqp      # i.MX8MP Emcraft Systems NavQ+ Kit
              - fsl,imx8mp-evk            # i.MX8MP EVK Board
              - fsl,imx8mp-evk-revb4      # i.MX8MP EVK Rev B4 Board
              - gateworks,imx8mp-gw71xx-2x # i.MX8MP Gateworks Board
              - gateworks,imx8mp-gw72xx-2x # i.MX8MP Gateworks Board
              - gateworks,imx8mp-gw73xx-2x # i.MX8MP Gateworks Board
              - gateworks,imx8mp-gw74xx   # i.MX8MP Gateworks Board
              - gateworks,imx8mp-gw75xx-2x # i.MX8MP Gateworks Board
              - gateworks,imx8mp-gw82xx-2x # i.MX8MP Gateworks Board
              - skov,imx8mp-skov-basic # SKOV i.MX8MP baseboard without frontplate
              - skov,imx8mp-skov-revb-hdmi # SKOV i.MX8MP climate control without panel
              - skov,imx8mp-skov-revb-lt6 # SKOV i.MX8MP climate control with 7” panel
              - skov,imx8mp-skov-revb-mi1010ait-1cp1 # SKOV i.MX8MP climate control with 10.1" panel
              - skov,imx8mp-skov-revc-bd500 # SKOV i.MX8MP climate control with LED frontplate
              - skov,imx8mp-skov-revc-tian-g07017 # SKOV i.MX8MP climate control with 7" panel
              - ysoft,imx8mp-iota2-lumpy  # Y Soft i.MX8MP IOTA2 Lumpy Board
          - const: fsl,imx8mp

      - description: ABB Boards with i.MX8M Plus Modules from ADLink
        items:
          - enum:
              - abb,imx8mp-aristanetos3-adpismarc # i.MX8MP ABB SoM on PI SMARC Board
              - abb,imx8mp-aristanetos3-helios    # i.MX8MP ABB SoM on helios Board
              - abb,imx8mp-aristanetos3-proton2s  # i.MX8MP ABB SoM on proton2s Board
          - const: abb,imx8mp-aristanetos3-som    # i.MX8MP ABB SoM
          - const: fsl,imx8mp

      - description: Avnet (MSC Branded) Boards with SM2S i.MX8M Plus Modules
        items:
          - const: avnet,sm2s-imx8mp-14N0600E-ep1 # SM2S-IMX8PLUS-14N0600E on SM2-MB-EP1 Carrier Board
          - const: avnet,sm2s-imx8mp-14N0600E     # 14N0600E variant of SM2S-IMX8PLUS SoM
          - const: avnet,sm2s-imx8mp              # SM2S-IMX8PLUS SoM
          - const: fsl,imx8mp

      - description: Boundary Devices Nitrogen8M Plus ENC Carrier Board
        items:
          - const: boundary,imx8mp-nitrogen-enc-carrier-board
          - const: boundary,imx8mp-nitrogen-som
          - const: fsl,imx8mp

      - description: Boundary Device Nitrogen8MP Universal SMARC Carrier Board
        items:
          - const: boundary,imx8mp-nitrogen-smarc-universal-board
          - const: boundary,imx8mp-nitrogen-smarc-som
          - const: fsl,imx8mp

      - description: i.MX8MP DHCOM based Boards
        items:
          - enum:
              - dh,imx8mp-dhcom-drc02        # i.MX8MP DHCOM SoM on DRC02 board
              - dh,imx8mp-dhcom-pdk2         # i.MX8MP DHCOM SoM on PDK2 board
              - dh,imx8mp-dhcom-pdk3         # i.MX8MP DHCOM SoM on PDK3 board
              - dh,imx8mp-dhcom-picoitx      # i.MX8MP DHCOM SoM on PicoITX board
          - const: dh,imx8mp-dhcom-som       # i.MX8MP DHCOM SoM
          - const: fsl,imx8mp

      - description: Engicam i.Core MX8M Plus SoM based boards
        items:
          - enum:
              - engicam,icore-mx8mp-edimm2.2       # i.MX8MP Engicam i.Core MX8M Plus EDIMM2.2 Starter Kit
          - const: engicam,icore-mx8mp             # i.MX8MP Engicam i.Core MX8M Plus SoM
          - const: fsl,imx8mp

      - description: Kontron i.MX8MP OSM-S SoM based Boards
        items:
          - const: kontron,imx8mp-bl-osm-s  # Kontron BL i.MX8MP OSM-S Board
          - const: kontron,imx8mp-osm-s     # Kontron i.MX8MP OSM-S SoM
          - const: fsl,imx8mp

      - description: Kontron i.MX8MP SMARC based Boards
        items:
          - const: kontron,imx8mp-smarc-eval-carrier  # Kontron i.MX8MP SMARC Eval Carrier
          - const: kontron,imx8mp-smarc               # Kontron i.MX8MP SMARC Module
          - const: kontron,imx8mp-osm-s               # Kontron i.MX8MP OSM-S SoM
          - const: fsl,imx8mp

      - description: PHYTEC phyCORE-i.MX8MP FPSC based boards
        items:
          - enum:
              - phytec,imx8mp-libra-rdk-fpsc  # i.MX 8M Plus Libra RDK
          - const: phytec,imx8mp-phycore-fpsc # phyCORE-i.MX 8M Plus FPSC
          - const: fsl,imx8mp

      - description: PHYTEC phyCORE-i.MX8MP SoM based boards
        items:
          - const: phytec,imx8mp-phyboard-pollux-rdk # phyBOARD-Pollux RDK
          - const: phytec,imx8mp-phycore-som         # phyCORE-i.MX8MP SoM
          - const: fsl,imx8mp

      - description: Polyhex DEBIX i.MX8MP based SBCs
        items:
          - enum:
              - polyhex,imx8mp-debix-model-a        # Polyhex Debix Model A Board
          - const: polyhex,imx8mp-debix             # Polyhex i.MX8MP Debix SBCs
          - const: fsl,imx8mp

      - description: Polyhex DEBIX i.MX8MP SOM A based boards
        items:
          - enum:
              - polyhex,imx8mp-debix-som-a-bmb-08   # Polyhex Debix SOM A on SOM A I/O board
          - const: polyhex,imx8mp-debix-som-a       # Polyhex Debix SOM A
          - const: fsl,imx8mp

      - description: Toradex Boards with SMARC iMX8M Plus Modules
        items:
          - const: toradex,smarc-imx8mp-dev # Toradex SMARC iMX8M Plus on Toradex SMARC Development Board
          - const: toradex,smarc-imx8mp     # Toradex SMARC iMX8M Plus Module
          - const: fsl,imx8mp

      - description: Toradex Boards with Verdin iMX8M Plus Modules
        items:
          - enum:
              - toradex,verdin-imx8mp-nonwifi-dahlia # Verdin iMX8M Plus Module on Dahlia
              - toradex,verdin-imx8mp-nonwifi-dev    # Verdin iMX8M Plus Module on Verdin Development Board
              - toradex,verdin-imx8mp-nonwifi-ivy    # Verdin iMX8M Plus Module on Ivy
              - toradex,verdin-imx8mp-nonwifi-mallow # Verdin iMX8M Plus Module on Mallow
              - toradex,verdin-imx8mp-nonwifi-yavia  # Verdin iMX8M Plus Module on Yavia
          - const: toradex,verdin-imx8mp-nonwifi     # Verdin iMX8M Plus Module without Wi-Fi / BT
          - const: toradex,verdin-imx8mp             # Verdin iMX8M Plus Module
          - const: fsl,imx8mp

      - description: Toradex Boards with Verdin iMX8M Plus Wi-Fi / BT Modules
        items:
          - enum:
              - toradex,verdin-imx8mp-wifi-dahlia # Verdin iMX8M Plus Wi-Fi / BT Module on Dahlia
              - toradex,verdin-imx8mp-wifi-dev    # Verdin iMX8M Plus Wi-Fi / BT M. on Verdin Development B.
              - toradex,verdin-imx8mp-wifi-ivy    # Verdin iMX8M Plus Wi-Fi / BT Module on Ivy
              - toradex,verdin-imx8mp-wifi-mallow # Verdin iMX8M Plus Wi-Fi / BT Module on Mallow
              - toradex,verdin-imx8mp-wifi-yavia  # Verdin iMX8M Plus Wi-Fi / BT Module on Yavia
          - const: toradex,verdin-imx8mp-wifi     # Verdin iMX8M Plus Wi-Fi / BT Module
          - const: toradex,verdin-imx8mp          # Verdin iMX8M Plus Module
          - const: fsl,imx8mp

      - description:
          TQMa8MPxL is a series of LGA SOM featuring NXP i.MX8MP system-on-chip
          variants. It is designed to be soldered on different carrier boards.
          All CPU variants use the same device tree hence only one compatible
          is needed. MBa8MPxL mainboard can be used as starterkit or in a boxed
          version as an industrial computing device.
        items:
          - enum:
              - tq,imx8mp-tqma8mpql-mba8mpxl      # TQ-Systems GmbH i.MX8MP TQMa8MPQL SOM on MBa8MPxL
              - tq,imx8mp-tqma8mpql-mba8mp-ras314 # TQ-Systems GmbH i.MX8MP TQMa8MPQL SOM on MBa8MP-RAS314
          - const: tq,imx8mp-tqma8mpql            # TQ-Systems GmbH i.MX8MP TQMa8MPQL SOM
          - const: fsl,imx8mp

      - description: Variscite VAR-SOM-MX8M Plus based boards
        items:
          - const: variscite,var-som-mx8mp-symphony
          - const: variscite,var-som-mx8mp
          - const: fsl,imx8mp

      - description: i.MX8MQ based Boards
        items:
          - enum:
              - boundary,imx8mq-nitrogen8m # i.MX8MQ NITROGEN Board
              - boundary,imx8mq-nitrogen8m-som # i.MX8MQ NITROGEN SoM
              - einfochips,imx8mq-thor96  # i.MX8MQ Thor96 Board
              - fsl,imx8mq-evk            # i.MX8MQ EVK Board
              - google,imx8mq-phanbell    # Google Coral Edge TPU
              - kontron,pitx-imx8m        # Kontron pITX-imx8m Board
              - purism,librem5-devkit     # Purism Librem5 devkit
              - solidrun,hummingboard-pulse # SolidRun Hummingboard Pulse
              - technexion,pico-pi-imx8m  # TechNexion PICO-PI-8M evk
          - const: fsl,imx8mq

      - description: i.MX8MQ NITROGEN SoM based Boards
        items:
          - const: mntre,reform2                  # MNT Reform2 Laptop
          - const: boundary,imx8mq-nitrogen8m-som # i.MX8MQ NITROGEN SoM
          - const: fsl,imx8mq

      - description: Purism Librem5 phones
        items:
          - enum:
              - purism,librem5r2          # Purism Librem5 phone "Chestnut"
              - purism,librem5r3          # Purism Librem5 phone "Dogwood"
              - purism,librem5r4          # Purism Librem5 phone "Evergreen"
          - const: purism,librem5
          - const: fsl,imx8mq

      - description:
          TQMa8Mx is a series of SOM featuring NXP i.MX8MQ system-on-chip
          variants. It is designed to be clicked on different carrier boards.
        items:
          - enum:
              - tq,imx8mq-tqma8mq-mba8mx # TQ-Systems GmbH i.MX8MQ TQMa8Mx SOM on MBa8Mx
          - const: tq,imx8mq-tqma8mq     # TQ-Systems GmbH i.MX8MQ TQMa8Mx SOM
          - const: fsl,imx8mq

      - description: Zodiac Inflight Innovations Ultra Boards
        items:
          - enum:
              - zii,imx8mq-ultra-rmb3
              - zii,imx8mq-ultra-zest
          - const: zii,imx8mq-ultra
          - const: fsl,imx8mq

      - description: i.MX8QM based Boards
        items:
          - enum:
              - fsl,imx8qm-mek           # i.MX8QM MEK Board
              - fsl,imx8qm-mek-revd      # i.MX8QM MEK Rev D Board
          - const: fsl,imx8qm

      - description: i.MX8QM Boards with Toradex Apalis iMX8 Modules
        items:
          - enum:
              - toradex,apalis-imx8-eval            # Apalis iMX8 Module on Apalis Evaluation V1.0/V1.1 Board
              - toradex,apalis-imx8-eval-v1.2       # Apalis iMX8 Module on Apalis Evaluation V1.2 Board
              - toradex,apalis-imx8-ixora-v1.1      # Apalis iMX8 Module on Ixora V1.1 Carrier Board
          - const: toradex,apalis-imx8
          - const: fsl,imx8qm

      - description: i.MX8QM Boards with Toradex Apalis iMX8 V1.1 Modules
        items:
          - enum:
              - toradex,apalis-imx8-v1.1-eval       # Apalis iMX8 V1.1 Module on Apalis Eval. V1.0/V1.1 Board
              - toradex,apalis-imx8-v1.1-eval-v1.2  # Apalis iMX8 V1.1 Module on Apalis Eval. V1.2 Board
              - toradex,apalis-imx8-v1.1-ixora-v1.1 # Apalis iMX8 V1.1 Module on Ixora V1.1 C. Board
              - toradex,apalis-imx8-v1.1-ixora-v1.2 # Apalis iMX8 V1.1 Module on Ixora V1.2 C. Board
          - const: toradex,apalis-imx8-v1.1
          - const: fsl,imx8qm

      - description: i.MX8QXP based Boards
        items:
          - enum:
              - einfochips,imx8qxp-ai_ml  # i.MX8QXP AI_ML Board
              - fsl,imx8qxp-mek           # i.MX8QXP MEK Board
              - fsl,imx8qxp-mek-wcpu      # i.MX8QXP MEK WCPU Board
          - const: fsl,imx8qxp

      - description: i.MX8DXL based Boards
        items:
          - enum:
              - fsl,imx8dxl-evk           # i.MX8DXL EVK Board
          - const: fsl,imx8dxl

      - description: i.MX8QXP/i.MX8DX Boards with Toradex Colibri iMX8X Modules
        items:
          - enum:
              - toradex,colibri-imx8x-aster   # Colibri iMX8X Module on Aster Board
              - toradex,colibri-imx8x-eval-v3 # Colibri iMX8X Module on Colibri Evaluation Board V3
              - toradex,colibri-imx8x-iris    # Colibri iMX8X Module on Iris Board
              - toradex,colibri-imx8x-iris-v2 # Colibri iMX8X Module on Iris Board V2
          - const: toradex,colibri-imx8x
          - enum:
              - fsl,imx8qxp
              - fsl,imx8dx

      - description:
          TQMa8Xx is a series of SOM featuring NXP i.MX8X system-on-chip
          variants. It is designed to be clicked on different carrier boards
          MBa8Xx is the starterkit
        oneOf:
          - items:
              - enum:
                  - tq,imx8dxp-tqma8xdp-mba8xx # TQ-Systems GmbH TQMa8XDP SOM on MBa8Xx
              - const: tq,imx8dxp-tqma8xdp     # TQ-Systems GmbH TQMa8XDP SOM (with i.MX8DXP)
              - const: fsl,imx8dxp
          - items:
              - enum:
                  - tq,imx8qxp-tqma8xqp-mba8xx # TQ-Systems GmbH TQMa8XQP SOM on MBa8Xx
              - const: tq,imx8qxp-tqma8xqp     # TQ-Systems GmbH TQMa8XQP SOM (with i.MX8QXP)
              - const: fsl,imx8qxp

      - description:
          TQMa8XxS is a series of SOM featuring NXP i.MX8X system-on-chip
          variants. It has the SMARC-2.0 form factor and is designed to be placed on
          different carrier boards. MB-SMARC-2 is a carrier reference design.
        oneOf:
          - items:
              - enum:
                  - tq,imx8qxp-tqma8xqps-mb-smarc-2 # TQ-Systems GmbH TQMa8QXPS SOM on MB-SMARC-2
              - const: tq,imx8qxp-tqma8xqps         # TQ-Systems GmbH TQMa8QXPS SOM
              - const: fsl,imx8qxp
          - items:
              - enum:
                  - tq,imx8dxp-tqma8xdps-mb-smarc-2 # TQ-Systems GmbH TQMa8XDPS SOM on MB-SMARC-2
              - const: tq,imx8dxp-tqma8xdps         # TQ-Systems GmbH TQMa8XDPS SOM
              - const: fsl,imx8dxp

      - description: i.MX8ULP based Boards
        items:
          - enum:
              - fsl,imx8ulp-evk           # i.MX8ULP EVK Board
          - const: fsl,imx8ulp

      - description: i.MX93 based Boards
        items:
          - enum:
              - fsl,imx93-9x9-qsb         # i.MX93 9x9 QSB Board
              - fsl,imx93-11x11-evk       # i.MX93 11x11 EVK Board
              - fsl,imx93-14x14-evk       # i.MX93 14x14 EVK Board
          - const: fsl,imx93

      - description: i.MX94 based Boards
        items:
          - enum:
              - fsl,imx943-evk            # i.MX943 EVK Board
          - const: fsl,imx94

      - description: i.MX95 based Boards
        items:
          - enum:
              - fsl,imx95-15x15-evk       # i.MX95 15x15 EVK Board
              - fsl,imx95-19x19-evk       # i.MX95 19x19 EVK Board
          - const: fsl,imx95

      - description: i.MXRT1050 based Boards
        items:
          - enum:
              - fsl,imxrt1050-evk         # i.MXRT1050 EVK Board
          - const: fsl,imxrt1050

      - description: i.MXRT1170 based Boards
        items:
          - enum:
              - fsl,imxrt1170-evk         # i.MXRT1170 EVK Board
          - const: fsl,imxrt1170

      - description:
          TQMa93xxLA and TQMa93xxCA are two series of feature compatible SOM
          using NXP i.MX93 SOC in 11x11 mm package.
          TQMa93xxLA is designed to be soldered on different carrier boards.
          TQMa93xxCA is a compatible variant using board to board connectors.
          All SOM and CPU variants use the same device tree hence only one
          compatible is needed. Bootloader disables all features not present
          in the assembled SOC.
          MBa91xxCA mainboard can be used as starterkit for the SOM
          soldered on an adapter board or for the connector variant
          to evaluate RGB display support.
          MBa93xxCA mainboard can be used as starterkit for the SOM
          soldered on an adapter board or for the connector variant
          MBa93xxLA mainboard is a single board computer using the solderable
          SOM variant
        items:
          - enum:
              - tq,imx93-tqma9352-mba91xxca # TQ-Systems GmbH i.MX93 TQMa93xxCA/LA SOM on MBa91xxCA
              - tq,imx93-tqma9352-mba93xxca # TQ-Systems GmbH i.MX93 TQMa93xxCA/LA SOM on MBa93xxCA
              - tq,imx93-tqma9352-mba93xxla # TQ-Systems GmbH i.MX93 TQMa93xxLA SOM on MBa93xxLA SBC
          - const: tq,imx93-tqma9352        # TQ-Systems GmbH i.MX93 TQMa93xxCA/LA SOM
          - const: fsl,imx93

      - description: PHYTEC phyCORE-i.MX93 SoM based boards
        items:
          - enum:
              - phytec,imx93-phyboard-nash  # phyBOARD-Nash-i.MX93
              - phytec,imx93-phyboard-segin # phyBOARD-Segin with i.MX93
          - const: phytec,imx93-phycore-som # phyCORE-i.MX93 SoM
          - const: fsl,imx93

      - description: Variscite VAR-SOM-MX93 based boards
        items:
          - const: variscite,var-som-mx93-symphony
          - const: variscite,var-som-mx93
          - const: fsl,imx93

      - description: Kontron OSM-S i.MX93 SoM based boards
        items:
          - const: kontron,imx93-bl-osm-s # Kontron BL i.MX93 OSM-S board
          - const: kontron,imx93-osm-s    # Kontron OSM-S i.MX93 SoM
          - const: fsl,imx93

      - description:
          TQMa95xxSA is a series of SOM featuring NXP i.MX95 SoC variants.
          It has the SMARC form factor and is designed to be placed on
          different carrier boards. MB-SMARC-2 is a carrier reference design.
        items:
          - enum:
              - tq,imx95-tqma9596sa-mb-smarc-2 # TQ-Systems GmbH i.MX95 TQMa95xxSA SOM on MB-SMARC-2
          - const: tq,imx95-tqma9596sa         # TQ-Systems GmbH i.MX95 TQMa95xxSA SOM
          - const: fsl,imx95

      - description:
          Freescale Vybrid Platform Device Tree Bindings

          For the Vybrid SoC family all variants with DDR controller are supported,
          which is the VF5xx and VF6xx series. Out of historical reasons, in most
          places the kernel uses vf610 to refer to the whole family.
          The compatible string "fsl,vf610m4" is used for the secondary Cortex-M4
          core support.
        items:
          - enum:
              - fsl,vf500
              - fsl,vf510
              - fsl,vf600
              - fsl,vf610
              - fsl,vf610m4

      - description: Toradex Colibri VF50 Module on Colibri Evaluation Board
        items:
          - const: toradex,vf500-colibri_vf50-on-eval
          - const: toradex,vf500-colibri_vf50
          - const: fsl,vf500

      - description: VF610 based Boards
        items:
          - enum:
              - fsl,vf610-twr             # VF610 Tower Board
              - lwn,bk4                   # Liebherr BK4 controller
              - phytec,vf610-cosmic       # PHYTEC Cosmic/Cosmic+ Board
          - const: fsl,vf610

      - description: Toradex Colibri VF61 Module on Colibri Evaluation Board
        items:
          - const: toradex,vf610-colibri_vf61-on-eval
          - const: toradex,vf610-colibri_vf61
          - const: fsl,vf610

      - description: ZII's VF610 based Boards
        items:
          - enum:
              - zii,vf610cfu1      # ZII VF610 CFU1 Board
              - zii,vf610dev-c     # ZII VF610 Development Board, Rev C
              - zii,vf610dev-b     # ZII VF610 Development Board, Rev B
              - zii,vf610scu4-aib  # ZII VF610 SCU4 AIB
              - zii,vf610dtu       # ZII VF610 SSMB DTU Board
              - zii,vf610spu3      # ZII VF610 SSMB SPU3 Board
              - zii,vf610spb4      # ZII VF610 SPB4 Board
          - const: zii,vf610dev
          - const: fsl,vf610

      - description: LS1012A based Boards
        items:
          - enum:
              - ebs-systart,oxalis
              - fsl,ls1012a-rdb
              - fsl,ls1012a-frdm
              - fsl,ls1012a-frwy
              - fsl,ls1012a-qds
          - const: fsl,ls1012a

      - description: LS1021A based Boards
        items:
          - enum:
              - fsl,ls1021a-iot
              - fsl,ls1021a-moxa-uc-8410a
              - fsl,ls1021a-qds
              - fsl,ls1021a-tsn
              - fsl,ls1021a-twr
          - const: fsl,ls1021a

      - description:
          TQ-Systems TQMLS102xA is a series of socketable SOM featuring
          LS102x system-on-chip variants. MBLS102xA mainboard can be used as
          starterkit.
        items:
          - enum:
              - tq,ls1021a-tqmls1021a-mbls102xa
          - const: tq,ls1021a-tqmls1021a
          - const: fsl,ls1021a

      - description: LS1028A based Boards
        items:
          - enum:
              - fsl,ls1028a-qds
              - fsl,ls1028a-rdb
          - const: fsl,ls1028a

      - description: Kontron KBox A-230-LS
        items:
          - const: kontron,kbox-a-230-ls
          - const: kontron,sl28-var4
          - const: kontron,sl28
          - const: fsl,ls1028a
      - description:
          Kontron SMARC-sAL28 board on the SMARC Eval Carrier 2.0
        items:
          - enum:
              - kontron,sl28-var1-ads2
              - kontron,sl28-var2-ads2
              - kontron,sl28-var3-ads2
              - kontron,sl28-var4-ads2
          - enum:
              - kontron,sl28-var1
              - kontron,sl28-var2
              - kontron,sl28-var3
              - kontron,sl28-var4
          - const: kontron,sl28
          - const: fsl,ls1028a

      - description:
          Kontron SMARC-sAL28 board (on a generic/undefined carrier)
        items:
          - enum:
              - kontron,sl28-var1
              - kontron,sl28-var2
              - kontron,sl28-var3
              - kontron,sl28-var4
          - const: kontron,sl28
          - const: fsl,ls1028a

      - description:
          Kontron SMARC-sAL28 board (base). This is used in the base device
          tree which is compatible with the overlays provided by the
          vendor.
        items:
          - const: kontron,sl28
          - const: fsl,ls1028a

      - description: LS1043A based Boards
        items:
          - enum:
              - fsl,ls1043a-rdb
              - fsl,ls1043a-qds
          - const: fsl,ls1043a

      - description: TQ-Systems LS1043A based Boards
        items:
          - enum:
              - tq,ls1043a-tqmls1043a-mbls10xxa
          - const: tq,ls1043a-tqmls1043a
          - const: fsl,ls1043a

      - description: LS1046A based Boards
        items:
          - enum:
              - fsl,ls1046a-frwy
              - fsl,ls1046a-qds
              - fsl,ls1046a-rdb
          - const: fsl,ls1046a

      - description: TQ-Systems LS1046A based Boards
        items:
          - enum:
              - tq,ls1046a-tqmls1046a-mbls10xxa
          - const: tq,ls1046a-tqmls1046a
          - const: fsl,ls1046a

      - description: LS1088A based Boards
        items:
          - enum:
              - fsl,ls1088a-qds
              - fsl,ls1088a-rdb
          - const: fsl,ls1088a

      - description: TQ-Systems LS1088A based Boards
        items:
          - enum:
              - tq,ls1088a-tqmls1088a-mbls10xxa
          - const: tq,ls1088a-tqmls1088a
          - const: fsl,ls1088a

      - description: LS2080A based Boards
        items:
          - enum:
              - fsl,ls2080a-simu
              - fsl,ls2080a-qds
              - fsl,ls2080a-rdb
          - const: fsl,ls2080a

      - description: LS2081A based Boards
        items:
          - enum:
              - fsl,ls2081a-rdb
          - const: fsl,ls2081a

      - description: LS2088A based Boards
        items:
          - enum:
              - fsl,ls2088a-qds
              - fsl,ls2088a-rdb
          - const: fsl,ls2088a

      - description: LX2160A based Boards
        items:
          - enum:
              - fsl,lx2160a-bluebox3
              - fsl,lx2160a-bluebox3-rev-a
              - fsl,lx2160a-qds
              - fsl,lx2160a-rdb
              - fsl,lx2162a-qds
          - const: fsl,lx2160a

      - description: SolidRun LX2160A CEX-7 based Boards
        items:
          - enum:
              - solidrun,clearfog-cx
              - solidrun,honeycomb
          - const: solidrun,lx2160a-cex7
          - const: fsl,lx2160a

      - description: SolidRun LX2162A SoM based Boards
        items:
          - enum:
              - solidrun,lx2162a-clearfog
          - const: solidrun,lx2162a-som
          - const: fsl,lx2160a

      - description:
          TQ-Systems TQMLX2160A is a series of socketable SOM featuring
          LX2160A system-on-chip variants. MBLX2160A mainboard can be used a
          starterkit.
        items:
          - enum:
              - tq,lx2160a-tqmlx2160a-mblx2160a
          - const: tq,lx2160a-tqmlx2160a
          - const: fsl,lx2160a

      - description: S32G2 based Boards
        items:
          - enum:
              - nxp,s32g274a-evb
              - nxp,s32g274a-rdb2
          - const: nxp,s32g2

      - description: S32G3 based Boards
        items:
          - enum:
              - nxp,s32g399a-rdb3
          - const: nxp,s32g3

      - description: S32V234 based Boards
        items:
          - enum:
              - fsl,s32v234-evb           # S32V234-EVB2 Customer Evaluation Board
          - const: fsl,s32v234

      - description: Traverse LS1088A based Boards
        items:
          - enum:
              - traverse,ten64            # Ten64 Networking Appliance / Board
          - const: fsl,ls1088a

additionalProperties: true

...
