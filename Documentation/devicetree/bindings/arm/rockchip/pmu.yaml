# SPDX-License-Identifier: GPL-2.0
%YAML 1.2
---
$id: http://devicetree.org/schemas/arm/rockchip/pmu.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: Rockchip Power Management Unit (PMU)

maintainers:
  - <PERSON> <<EMAIL>>
  - <PERSON><PERSON> <<EMAIL>>

description: |
  The PMU is used to turn on and off different power domains of the SoCs.
  This includes the power to the CPU cores.

select:
  properties:
    compatible:
      contains:
        enum:
          - rockchip,px30-pmu
          - rockchip,rk3066-pmu
          - rockchip,rk3128-pmu
          - rockchip,rk3288-pmu
          - rockchip,rk3368-pmu
          - rockchip,rk3399-pmu
          - rockchip,rk3562-pmu
          - rockchip,rk3568-pmu
          - rockchip,rk3576-pmu
          - rockchip,rk3588-pmu
          - rockchip,rv1126-pmu

  required:
    - compatible

properties:
  compatible:
    items:
      - enum:
          - rockchip,px30-pmu
          - rockchip,rk3066-pmu
          - rockchip,rk3128-pmu
          - rockchip,rk3288-pmu
          - rockchip,rk3368-pmu
          - rockchip,rk3399-pmu
          - rockchip,rk3562-pmu
          - rockchip,rk3568-pmu
          - rockchip,rk3576-pmu
          - rockchip,rk3588-pmu
          - rockchip,rv1126-pmu
      - const: syscon
      - const: simple-mfd

  reg:
    maxItems: 1

  power-controller:
    type: object

  reboot-mode:
    type: object

required:
  - compatible
  - reg

additionalProperties: false

examples:
  - |
    pmu@20004000 {
      compatible = "rockchip,rk3066-pmu", "syscon", "simple-mfd";
      reg = <0x20004000 0x100>;
    };
