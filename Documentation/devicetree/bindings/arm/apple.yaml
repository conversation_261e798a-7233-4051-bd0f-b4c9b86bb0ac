# SPDX-License-Identifier: GPL-2.0-only OR BSD-2-Clause
%YAML 1.2
---
$id: http://devicetree.org/schemas/arm/apple.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: Apple ARM Machine

maintainers:
  - <PERSON> <<EMAIL>>

description: |
  ARM platforms using SoCs designed by Apple Inc., branded "Apple Silicon".

  This currently includes devices based on the "A7" SoC:

  - iPhone 5s
  - iPad Air (1)
  - iPad mini 2
  - iPad mini 3

  Devices based on the "A8" SoC:

  - iPhone 6
  - iPhone 6 Plus
  - iPad mini 4
  - iPod touch 6
  - Apple TV HD

  Device based on the "A8X" SoC:

  - iPad Air 2

  Devices based on the "A9" SoC:

  - iPhone 6s
  - iPhone 6s Plus
  - iPhone SE (2016)
  - iPad 5

  Devices based on the "A9X" SoC:

  - iPad Pro (9.7-inch)
  - iPad Pro (12.9-inch)

  Devices based on the "A10" SoC:

  - iPhone 7
  - iPhone 7 Plus
  - iPod touch 7
  - iPad 6
  - iPad 7

  Devices based on the "A10X" SoC:

  - Apple TV 4K (1st generation)
  - iPad Pro (2nd Generation) (10.5 Inch)
  - iPad Pro (2nd Generation) (12.9 Inch)

  Devices based on the "T2" SoC:

  - Apple T2 MacBookPro15,2 (j132)
  - Apple T2 iMacPro1,1 (j137)
  - Apple T2 MacBookAir8,2 (j140a)
  - Apple T2 MacBookAir8,1 (j140k)
  - Apple T2 MacBookPro16,1 (j152f)
  - Apple T2 MacPro7,1 (j160)
  - Apple T2 Macmini8,1 (j174)
  - Apple T2 iMac20,1 (j185)
  - Apple T2 iMac20,2 (j185f)
  - Apple T2 MacBookPro15,4 (j213)
  - Apple T2 MacBookPro16,2 (j214k)
  - Apple T2 MacBookPro16,4 (j215)
  - Apple T2 MacBookPro16,3 (j223)
  - Apple T2 MacBookAir9,1 (j230k)
  - Apple T2 MacBookPro15,1 (j680)
  - Apple T2 MacBookPro15,3 (j780)

  Devices based on the "A11" SoC:

  - iPhone 8
  - iPhone 8 Plus
  - iPhone X

  Devices based on the "M1" SoC:

  - Mac mini (M1, 2020)
  - MacBook Pro (13-inch, M1, 2020)
  - MacBook Air (M1, 2020)
  - iMac (24-inch, M1, 2021)

  Devices based on the "M2" SoC:

  - MacBook Air (M2, 2022)
  - MacBook Pro (13-inch, M2, 2022)
  - Mac mini (M2, 2023)

  And devices based on the "M1 Pro", "M1 Max" and "M1 Ultra" SoCs:

  - MacBook Pro (14-inch, M1 Pro, 2021)
  - MacBook Pro (14-inch, M1 Max, 2021)
  - MacBook Pro (16-inch, M1 Pro, 2021)
  - MacBook Pro (16-inch, M1 Max, 2021)
  - Mac Studio (M1 Max, 2022)
  - Mac Studio (M1 Ultra, 2022)

  The compatible property should follow this format:

  compatible = "apple,<targettype>", "apple,<socid>", "apple,arm-platform";

  <targettype> represents the board/device and comes from the `target-type`
  property of the root node of the Apple Device Tree, lowercased. It can be
  queried on macOS using the following command:

  $ ioreg -d2 -l | grep target-type

  <socid> is the lowercased SoC ID. Apple uses at least *five* different
  names for their SoCs:

  - Marketing name ("M1")
  - Internal name ("H13G")
  - Codename ("Tonga")
  - SoC ID ("T8103")
  - Package/IC part number ("APL1102")

  Devicetrees should use the lowercased SoC ID, to avoid confusion if
  multiple SoCs share the same marketing name. This can be obtained from
  the `compatible` property of the arm-io node of the Apple Device Tree,
  which can be queried as follows on macOS:

  $ ioreg -n arm-io | grep compatible

properties:
  $nodename:
    const: "/"
  compatible:
    oneOf:
      - description: Apple A7 SoC based platforms
        items:
          - enum:
              - apple,j71  # iPad Air (Wi-Fi)
              - apple,j72  # iPad Air (Cellular)
              - apple,j73  # iPad Air (Cellular, China)
              - apple,j85  # iPad mini 2 (Wi-Fi)
              - apple,j85m # iPad mini 3 (Wi-Fi)
              - apple,j86  # iPad mini 2 (Cellular)
              - apple,j86m # iPad mini 3 (Cellular)
              - apple,j87  # iPad mini 2 (Cellular, China)
              - apple,j87m # iPad mini 3 (Cellular, China)
              - apple,n51  # iPhone 5s (GSM)
              - apple,n53  # iPhone 5s (LTE)
          - const: apple,s5l8960x
          - const: apple,arm-platform

      - description: Apple A8 SoC based platforms
        items:
          - enum:
              - apple,j42d # Apple TV HD
              - apple,j96  # iPad mini 4 (Wi-Fi)
              - apple,j97  # iPad mini 4 (Cellular)
              - apple,n56  # iPhone 6 Plus
              - apple,n61  # iPhone 6
              - apple,n102 # iPod touch 6
          - const: apple,t7000
          - const: apple,arm-platform

      - description: Apple A8X SoC based platforms
        items:
          - enum:
              - apple,j81 # iPad Air 2 (Wi-Fi)
              - apple,j82 # iPad Air 2 (Cellular)
          - const: apple,t7001
          - const: apple,arm-platform

      - description: Apple Samsung A9 SoC based platforms
        items:
          - enum:
              - apple,j71s # iPad 5 (Wi-Fi) (S8000)
              - apple,j72s # iPad 5 (Cellular) (S8000)
              - apple,n66  # iPhone 6s Plus (S8000)
              - apple,n69u # iPhone SE (S8000)
              - apple,n71  # iPhone 6S (S8000)
          - const: apple,s8000
          - const: apple,arm-platform

      - description: Apple TSMC A9 SoC based platforms
        items:
          - enum:
              - apple,j71t # iPad 5 (Wi-Fi) (S8003)
              - apple,j72t # iPad 5 (Cellular) (S8003)
              - apple,n66m # iPhone 6s Plus (S8003)
              - apple,n69  # iPhone SE (S8003)
              - apple,n71m # iPhone 6S (S8003)
          - const: apple,s8003
          - const: apple,arm-platform

      - description: Apple A9X SoC based platforms
        items:
          - enum:
              - apple,j127 # iPad Pro (9.7-inch) (Wi-Fi)
              - apple,j128 # iPad Pro (9.7-inch) (Cellular)
              - apple,j98a # iPad Pro (12.9-inch) (Wi-Fi)
              - apple,j99a # iPad Pro (12.9-inch) (Cellular)
          - const: apple,s8001
          - const: apple,arm-platform

      - description: Apple A10 SoC based platforms
        items:
          - enum:
              - apple,d10  # iPhone 7 (Qualcomm)
              - apple,d11  # iPhone 7 (Intel)
              - apple,d101 # iPhone 7 Plus (Qualcomm)
              - apple,d111 # iPhone 7 Plus (Intel)
              - apple,j71b # iPad 6 (Wi-Fi)
              - apple,j72b # iPad 6 (Cellular)
              - apple,j171 # iPad 7 (Wi-Fi)
              - apple,j172 # iPad 7 (Cellular)
              - apple,n112 # iPod touch 7
          - const: apple,t8010
          - const: apple,arm-platform

      - description: Apple A10X SoC based platforms
        items:
          - enum:
              - apple,j105a # Apple TV 4K (1st Generation)
              - apple,j120  # iPad Pro 2 (12.9-inch) (Wi-Fi)
              - apple,j121  # iPad Pro 2 (12.9-inch) (Cellular)
              - apple,j207  # iPad Pro 2 (10.5-inch) (Wi-Fi)
              - apple,j208  # iPad Pro 2 (10.5-inch) (Cellular)
          - const: apple,t8011
          - const: apple,arm-platform

      - description: Apple T2 SoC based platforms
        items:
          - enum:
              - apple,j132  # Apple T2 MacBookPro15,2 (j132)
              - apple,j137  # Apple T2 iMacPro1,1 (j137)
              - apple,j140a # Apple T2 MacBookAir8,2 (j140a)
              - apple,j140k # Apple T2 MacBookAir8,1 (j140k)
              - apple,j152f # Apple T2 MacBookPro16,1 (j152f)
              - apple,j160  # Apple T2 MacPro7,1 (j160)
              - apple,j174  # Apple T2 Macmini8,1 (j174)
              - apple,j185  # Apple T2 iMac20,1 (j185)
              - apple,j185f # Apple T2 iMac20,2 (j185f)
              - apple,j213  # Apple T2 MacBookPro15,4 (j213)
              - apple,j214k # Apple T2 MacBookPro16,2 (j214k)
              - apple,j215  # Apple T2 MacBookPro16,4 (j215)
              - apple,j223  # Apple T2 MacBookPro16,3 (j223)
              - apple,j230k # Apple T2 MacBookAir9,1 (j230k)
              - apple,j680  # Apple T2 MacBookPro15,1 (j680)
              - apple,j780  # Apple T2 MacBookPro15,3 (j780)
          - const: apple,t8012
          - const: apple,arm-platform

      - description: Apple A11 SoC based platforms
        items:
          - enum:
              - apple,d20  # iPhone 8 (Global)
              - apple,d21  # iPhone 8 Plus (Global)
              - apple,d22  # iPhone X (Global)
              - apple,d201 # iPhone 8 (GSM)
              - apple,d211 # iPhone 8 Plus (GSM)
              - apple,d221 # iPhone X (GSM)
          - const: apple,t8015
          - const: apple,arm-platform

      - description: Apple M1 SoC based platforms
        items:
          - enum:
              - apple,j274 # Mac mini (M1, 2020)
              - apple,j293 # MacBook Pro (13-inch, M1, 2020)
              - apple,j313 # MacBook Air (M1, 2020)
              - apple,j456 # iMac (24-inch, 4x USB-C, M1, 2021)
              - apple,j457 # iMac (24-inch, 2x USB-C, M1, 2021)
          - const: apple,t8103
          - const: apple,arm-platform

      - description: Apple M2 SoC based platforms
        items:
          - enum:
              - apple,j413 # MacBook Air (M2, 2022)
              - apple,j473 # Mac mini (M2, 2023)
              - apple,j493 # MacBook Pro (13-inch, M2, 2022)
          - const: apple,t8112
          - const: apple,arm-platform

      - description: Apple M1 Pro SoC based platforms
        items:
          - enum:
              - apple,j314s # MacBook Pro (14-inch, M1 Pro, 2021)
              - apple,j316s # MacBook Pro (16-inch, M1 Pro, 2021)
          - const: apple,t6000
          - const: apple,arm-platform

      - description: Apple M1 Max SoC based platforms
        items:
          - enum:
              - apple,j314c # MacBook Pro (14-inch, M1 Max, 2021)
              - apple,j316c # MacBook Pro (16-inch, M1 Max, 2021)
              - apple,j375c # Mac Studio (M1 Max, 2022)
          - const: apple,t6001
          - const: apple,arm-platform

      - description: Apple M1 Ultra SoC based platforms
        items:
          - enum:
              - apple,j375d # Mac Studio (M1 Ultra, 2022)
          - const: apple,t6002
          - const: apple,arm-platform

additionalProperties: true

...
