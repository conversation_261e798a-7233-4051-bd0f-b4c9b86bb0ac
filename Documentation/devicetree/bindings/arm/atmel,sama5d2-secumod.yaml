# SPDX-License-Identifier: (GPL-2.0-only OR BSD-2-Clause)
%YAML 1.2
---
$id: http://devicetree.org/schemas/arm/atmel,sama5d2-secumod.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: Microchip AT91 Security Module (SECUMOD)

maintainers:
  - <PERSON> <<EMAIL>>

description:
  The Security Module also offers the PIOBU pins which can be used as GPIO pins.
  Note that they maintain their voltage during Backup/Self-refresh.

properties:
  compatible:
    oneOf:
      - items:
          - const: atmel,sama5d2-secumod
          - const: syscon
      - items:
          - enum:
              - microchip,sama7d65-secumod
              - microchip,sama7g5-secumod
          - const: atmel,sama5d2-secumod
          - const: syscon
  reg:
    maxItems: 1

  gpio-controller: true

  "#gpio-cells":
    const: 2

required:
  - compatible
  - reg

unevaluatedProperties: false

examples:
  - |
    security-module@fc040000 {
      compatible = "atmel,sama5d2-secumod", "syscon";
      reg = <0xfc040000 0x100>;
      gpio-controller;
      #gpio-cells = <2>;
    };
