# SPDX-License-Identifier: (GPL-2.0-only OR BSD-2-Clause)
%YAML 1.2
---
$id: http://devicetree.org/schemas/arm/intel,socfpga.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: Intel SoCFPGA platform

maintainers:
  - <PERSON><PERSON> <<EMAIL>>

properties:
  $nodename:
    const: "/"
  compatible:
    oneOf:
      - description: AgileX boards
        items:
          - enum:
              - intel,n5x-socdk
              - intel,socfpga-agilex-n6000
              - intel,socfpga-agilex-socdk
          - const: intel,socfpga-agilex
      - description: Agilex5 boards
        items:
          - enum:
              - intel,socfpga-agilex5-socdk
              - intel,socfpga-agilex5-socdk-nand
          - const: intel,socfpga-agilex5

additionalProperties: true

...
