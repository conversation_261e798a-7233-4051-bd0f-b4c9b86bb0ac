# SPDX-License-Identifier: (GPL-2.0 OR BSD-2-Clause)
%YAML 1.2
---
$id: http://devicetree.org/schemas/arm/blaize.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: Blaize Platforms

maintainers:
  - <PERSON> <<EMAIL>>
  - <PERSON> <<EMAIL>>
  - <PERSON> <<EMAIL>>
  - <PERSON><PERSON> <<EMAIL>>

description: |
  Blaize Platforms using SoCs designed by Blaize Inc.

  The products based on the BLZP1600 SoC:

  - BLZP1600-SoM: SoM (System on Module)
  - BLZP1600-CB2: Development board CB2 based on BLZP1600-SoM

  BLZP1600 SoC integrates a dual core ARM Cortex A53 cluster
  and a Blaize Graph Streaming Processor for AI and ML workloads,
  plus a suite of connectivity and other peripherals.

properties:
  $nodename:
    const: '/'
  compatible:
    oneOf:
      - description: Blaize BLZP1600 based boards
        items:
          - enum:
              - blaize,blzp1600-cb2
          - const: blaize,blzp1600

additionalProperties: true

...
