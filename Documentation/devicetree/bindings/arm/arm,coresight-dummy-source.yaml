# SPDX-License-Identifier: GPL-2.0-only OR BSD-2-Clause
%YAML 1.2
---
$id: http://devicetree.org/schemas/arm/arm,coresight-dummy-source.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: ARM Coresight Dummy source component

description: |
  CoreSight components are compliant with the ARM CoreSight architecture
  specification and can be connected in various topologies to suit a particular
  SoCs tracing needs. These trace components can generally be classified as
  sinks, links and sources. Trace data produced by one or more sources flows
  through the intermediate links connecting the source to the currently selected
  sink.

  The Coresight dummy source component is for the specific coresight source
  devices kernel don't have permission to access or configure. For some SOCs,
  there would be Coresight source trace components on sub-processor which
  are connected to AP processor via debug bus. For these devices, a dummy driver
  is needed to register them as Coresight source devices, so that paths can be
  created in the driver. It provides Coresight API for operations on dummy
  source devices, such as enabling and disabling them. It also provides the
  Coresight dummy source paths for debugging.

  The primary use case of the coresight dummy source is to build path in kernel
  side for dummy source component.

maintainers:
  - <PERSON> <<EMAIL>>
  - <PERSON> <<EMAIL>>
  - <PERSON> <<EMAIL>>
  - <PERSON>long <<EMAIL>>
  - Hao Zhang <<EMAIL>>

properties:
  compatible:
    enum:
      - arm,coresight-dummy-source

  arm,static-trace-id:
    description: If dummy source needs static id support, use this to set trace id.
    $ref: /schemas/types.yaml#/definitions/uint32
    minimum: 1
    maximum: 111

  out-ports:
    $ref: /schemas/graph.yaml#/properties/ports

    properties:
      port:
        description: Output connection from the source to Coresight
          Trace bus.
        $ref: /schemas/graph.yaml#/properties/port

required:
  - compatible
  - out-ports

additionalProperties: false

examples:
  # Minimum dummy source definition. Dummy source connect to coresight funnel.
  - |
    source {
      compatible = "arm,coresight-dummy-source";

      out-ports {
        port {
          dummy_riscv_out_funnel_swao: endpoint {
            remote-endpoint = <&funnel_swao_in_dummy_riscv>;
          };
        };
      };
    };

...
