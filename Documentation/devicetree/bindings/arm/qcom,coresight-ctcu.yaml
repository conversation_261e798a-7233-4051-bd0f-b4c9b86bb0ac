# SPDX-License-Identifier: (GPL-2.0-only OR BSD-2-Clause)
%YAML 1.2
---
$id: http://devicetree.org/schemas/arm/qcom,coresight-ctcu.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: CoreSight TMC Control Unit

maintainers:
  - <PERSON><PERSON><PERSON> <<EMAIL>>
  - <PERSON> <quic_jin<PERSON><PERSON>@quicinc.com>
  - <PERSON><PERSON> <<EMAIL>>

description: |
  The Trace Memory Controller(TMC) is used for Embedded Trace Buffer(ETB),
  Embedded Trace FIFO(ETF) and Embedded Trace Router(ETR) configurations.
  The configuration mode (ETB, ETF, ETR) is discovered at boot time when
  the device is probed.

  The Coresight TMC Control unit controls various Coresight behaviors.
  It works as a helper device when connected to TMC ETR device.
  It is responsible for controlling the data filter function based on
  the source device's Trace ID for TMC ETR device. The trace data with
  that Trace id can get into ETR's buffer while other trace data gets
  ignored.

properties:
  compatible:
    enum:
      - qcom,sa8775p-ctcu

  reg:
    maxItems: 1

  clocks:
    maxItems: 1

  clock-names:
    items:
      - const: apb

  in-ports:
    $ref: /schemas/graph.yaml#/properties/ports

    patternProperties:
      '^port(@[0-1])?$':
        description: Input connections from CoreSight Trace bus
        $ref: /schemas/graph.yaml#/properties/port

required:
  - compatible
  - reg
  - in-ports

additionalProperties: false

examples:
  - |
    ctcu@1001000 {
        compatible = "qcom,sa8775p-ctcu";
        reg = <0x1001000 0x1000>;

        clocks = <&aoss_qmp>;
        clock-names = "apb";

        in-ports {
            #address-cells = <1>;
            #size-cells = <0>;

            port@0 {
                reg = <0>;
                ctcu_in_port0: endpoint {
                    remote-endpoint = <&etr0_out_port>;
                };
            };

            port@1 {
                reg = <1>;
                ctcu_in_port1: endpoint {
                    remote-endpoint = <&etr1_out_port>;
                };
            };
        };
    };
