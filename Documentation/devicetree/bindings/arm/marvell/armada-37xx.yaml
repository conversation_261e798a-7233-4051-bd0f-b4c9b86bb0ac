# SPDX-License-Identifier: (GPL-2.0-only OR BSD-2-Clause)
%YAML 1.2
---
$id: http://devicetree.org/schemas/arm/marvell/armada-37xx.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: Marvell Armada 37xx Platforms

maintainers:
  - <PERSON> <<EMAIL>>

properties:
  $nodename:
    const: '/'
  compatible:
    oneOf:
      - description: Armada 3720 SoC boards
        items:
          - enum:
              - cznic,turris-mox
              - glinet,gl-mv1000
              - globalscale,espressobin
              - marvell,armada-3720-db
              - methode,edpu
              - methode,udpu
          - const: marvell,armada3720
          - const: marvell,armada3710

      - description: Globalscale Espressobin boards
        items:
          - enum:
              - globalscale,espressobin-emmc
              - globalscale,espressobin-ultra
              - globalscale,espressobin-v7
          - const: globalscale,espressobin
          - const: marvell,armada3720
          - const: marvell,armada3710

      - description: Globalscale Espressobin V7 boards
        items:
          - enum:
              - globalscale,espressobin-v7-emmc
          - const: globalscale,espressobin-v7
          - const: globalscale,espressobin
          - const: marvell,armada3720
          - const: marvell,armada3710

additionalProperties: true
