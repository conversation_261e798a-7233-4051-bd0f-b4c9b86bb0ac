# SPDX-License-Identifier: (GPL-2.0+ OR X11)
%YAML 1.2
---
$id: http://devicetree.org/schemas/arm/marvell/armada-7k-8k.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: Marvell Armada 7K/8K Platforms

maintainers:
  - <PERSON>LEMENT <<EMAIL>>

properties:
  $nodename:
    const: '/'
  compatible:
    oneOf:

      - description: Armada 7020 SoC
        items:
          - const: marvell,armada7020
          - const: marvell,armada-ap806-dual
          - const: marvell,armada-ap806

      - description: Armada 7040 SoC
        items:
          - enum:
              - globalscale,mochabin
              - marvell,armada7040-db
          - const: marvell,armada7040
          - const: marvell,armada-ap806-quad
          - const: marvell,armada-ap806

      - description: Armada 8020 SoC
        items:
          - const: marvell,armada8020
          - const: marvell,armada-ap806-dual
          - const: marvell,armada-ap806

      - description: Armada 8040 SoC
        items:
          - enum:
              - iei,puzzle-m801
              - marvell,armada8040-db
              - solidrun,clearfog-gt-8k
          - const: marvell,armada8040
          - const: marvell,armada-ap806-quad
          - const: marvell,armada-ap806

      - description: Armada 8040 SoC MACCHIATOBin Boards
        items:
          - enum:
              - marvell,armada8040-mcbin-doubleshot
              - marvell,armada8040-mcbin-singleshot
          - const: marvell,armada8040-mcbin
          - const: marvell,armada8040
          - const: marvell,armada-ap806-quad
          - const: marvell,armada-ap806

      - description: Armada 8080 SoC
        items:
          - enum:
              - marvell,armada-8080-db
          - const: marvell,armada-8080
          - const: marvell,armada-ap810-octa
          - const: marvell,armada-ap810

      - description: Armada CN9130 SoC with no external CP
        items:
          - const: marvell,cn9130
          - const: marvell,armada-ap807-quad
          - const: marvell,armada-ap807

      - description: Armada CN9131 SoC with one external CP
        items:
          - const: marvell,cn9131
          - const: marvell,cn9130
          - const: marvell,armada-ap807-quad
          - const: marvell,armada-ap807

      - description: Armada CN9132 SoC with two external CPs
        items:
          - const: marvell,cn9132
          - const: marvell,cn9131
          - const: marvell,cn9130
          - const: marvell,armada-ap807-quad
          - const: marvell,armada-ap807

      - description:
          Alleycat5X (98DX35xx) Reference Design as COM Express Carrier plus
          Armada CN9130 COM Express CPU module
        items:
          - const: marvell,cn9130-ac5x-carrier
          - const: marvell,rd-ac5x-carrier
          - const: marvell,cn9130-cpu-module
          - const: marvell,cn9130
          - const: marvell,armada-ap807-quad
          - const: marvell,armada-ap807

      - description:
          Alleycat5X (98DX35xx) Reference Design as COM Express Carrier plus
          Armada CN9131 COM Express CPU module
        items:
          - const: marvell,cn9131-ac5x-carrier
          - const: marvell,rd-ac5x-carrier
          - const: marvell,cn9131-cpu-module
          - const: marvell,cn9131
          - const: marvell,armada-ap807-quad
          - const: marvell,armada-ap807

      - description:
          SolidRun CN9130 SoM based single-board computers
        items:
          - enum:
              - solidrun,cn9130-clearfog-base
              - solidrun,cn9130-clearfog-pro
              - solidrun,cn9131-solidwan
          - const: solidrun,cn9130-sr-som
          - const: marvell,cn9130

      - description:
          SolidRun CN9132 COM-Express Type 7 based single-board computers
        items:
          - enum:
              - solidrun,cn9132-clearfog
          - const: solidrun,cn9132-sr-cex7
          - const: marvell,cn9130

additionalProperties: true
