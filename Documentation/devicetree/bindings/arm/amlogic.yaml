# SPDX-License-Identifier: GPL-2.0
%YAML 1.2
---
$id: http://devicetree.org/schemas/arm/amlogic.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: Amlogic SoC based Platforms

maintainers:
  - <PERSON> <<EMAIL>>
  - <PERSON> <<EMAIL>>
  - <PERSON> <<EMAIL>>
  - <PERSON> <<EMAIL>>

properties:
  $nodename:
    const: '/'
  compatible:
    oneOf:
      - description: Boards with the Amlogic Meson6 SoC
        items:
          - enum:
              - geniatech,atv1200
          - const: amlogic,meson6

      - description: Boards with the Amlogic Meson8 SoC
        items:
          - enum:
              - minix,neo-x8
              - tcu,fernsehfee3
          - const: amlogic,meson8

      - description: Boards with the Amlogic Meson8m2 SoC
        items:
          - enum:
              - tronsmart,mxiii-plus
          - const: amlogic,meson8m2

      - description: Boards with the Amlogic Meson8b SoC
        items:
          - enum:
              - endless,ec100
              - hardkernel,odroid-c1
              - tronfy,mxq
          - const: amlogic,meson8b

      - description: Boards with the Amlogic Meson GXBaby SoC
        items:
          - enum:
              - amlogic,p200
              - amlogic,p201
              - friendlyarm,nanopi-k2
              - hardkernel,odroid-c2
              - nexbox,a95x
              - videostrong,kii-pro
              - wetek,hub
              - wetek,play2
          - const: amlogic,meson-gxbb

      - description: Tronsmart Vega S95 devices
        items:
          - enum:
              - tronsmart,vega-s95-pro
              - tronsmart,vega-s95-meta
              - tronsmart,vega-s95-telos
          - const: tronsmart,vega-s95
          - const: amlogic,meson-gxbb

      - description: Boards with the Amlogic Meson GXL S805X SoC
        items:
          - enum:
              - amlogic,p241
              - libretech,aml-s805x-ac
          - const: amlogic,s805x
          - const: amlogic,meson-gxl

      - description: Boards with the Amlogic Meson GXL S805Y SoC
        items:
          - enum:
              - xiaomi,aquaman
          - const: amlogic,s805y
          - const: amlogic,meson-gxl

      - description: Boards with the Amlogic Meson GXL S905W SoC
        items:
          - enum:
              - amlogic,p281
              - oranth,tx3-mini
              - jethome,jethub-j80
          - const: amlogic,s905w
          - const: amlogic,meson-gxl

      - description: Boards with the Amlogic Meson GXL S905X SoC
        items:
          - enum:
              - amlogic,p212
              - hwacom,amazetv
              - khadas,vim
              - libretech,aml-s905x-cc
              - libretech,aml-s905x-cc-v2
              - nexbox,a95x
              - osmc,vero4k
          - const: amlogic,s905x
          - const: amlogic,meson-gxl

      - description: Boards with the Amlogic Meson GXL S905D SoC
        items:
          - enum:
              - amlogic,p230
              - amlogic,p231
              - libretech,aml-s905d-pc
              - osmc,vero4k-plus
              - phicomm,n1
              - smartlabs,sml5442tw
              - videostrong,gxl-kii-pro
          - const: amlogic,s905d
          - const: amlogic,meson-gxl

      - description: Boards with the Amlogic Meson GXLX S905L SoC
        items:
          - enum:
              - amlogic,p271
          - const: amlogic,s905l
          - const: amlogic,meson-gxlx

      - description: Boards with the Amlogic Meson GXM S912 SoC
        items:
          - enum:
              - amlogic,q200
              - amlogic,q201
              - azw,gt1-ultimate
              - khadas,vim2
              - kingnovel,r-box-pro
              - libretech,aml-s912-pc
              - minix,neo-u9h
              - nexbox,a1
              - tronsmart,vega-s96
              - videostrong,gxm-kiii-pro
              - wetek,core2
          - const: amlogic,s912
          - const: amlogic,meson-gxm

      - description: Boards with the Amlogic Meson AXG A113D SoC
        items:
          - enum:
              - amlogic,s400
              - jethome,jethub-j100
              - jethome,jethub-j110
          - const: amlogic,a113d
          - const: amlogic,meson-axg

      - description: Boards with the Amlogic Meson G12A S905D2/X2/Y2 SoC
        items:
          - enum:
              - amediatech,x96-max
              - amlogic,u200
              - freebox,fbx8am
              - radxa,zero
              - seirobotics,sei510
          - const: amlogic,g12a

      - description: Boards with the Amlogic Meson G12B A311D SoC
        items:
          - enum:
              - bananapi,bpi-m2s
              - khadas,vim3
              - libretech,aml-a311d-cc
              - radxa,zero2
          - const: amlogic,a311d
          - const: amlogic,g12b

      - description: Boards using the BPI-CM4 module with Amlogic Meson G12B A311D SoC
        items:
          - enum:
              - bananapi,bpi-cm4io
              - mntre,reform2-cm4
          - const: bananapi,bpi-cm4
          - const: amlogic,a311d
          - const: amlogic,g12b

      - description: Boards with the Amlogic Meson G12B S922X SoC
        items:
          - enum:
              - azw,gsking-x
              - azw,gtking
              - azw,gtking-pro
              - bananapi,bpi-m2s
              - dream,dreambox-one
              - dream,dreambox-two
              - hardkernel,odroid-go-ultra
              - hardkernel,odroid-n2
              - hardkernel,odroid-n2l
              - hardkernel,odroid-n2-plus
              - khadas,vim3
              - ugoos,am6
          - const: amlogic,s922x
          - const: amlogic,g12b

      - description: Boards with the Amlogic Meson SM1 S905X3/D3/Y3 SoC
        items:
          - enum:
              - amediatech,x96-air
              - amediatech,x96-air-gbit
              - bananapi,bpi-m2-pro
              - bananapi,bpi-m5
              - cyx,a95xf3-air
              - cyx,a95xf3-air-gbit
              - hardkernel,odroid-c4
              - hardkernel,odroid-hc4
              - haochuangyi,h96-max
              - khadas,vim3l
              - libretech,aml-s905d3-cc
              - seirobotics,sei610
          - const: amlogic,sm1

      - description: Boards with the Amlogic Meson A1 A113L SoC
        items:
          - enum:
              - amlogic,ad401
              - amlogic,ad402
          - const: amlogic,a1

      - description: Boards with the Amlogic A4 A113L2 SoC
        items:
          - enum:
              - amlogic,ba400
          - const: amlogic,a4

      - description: Boards with the Amlogic A5 A113X2 SoC
        items:
          - enum:
              - amlogic,av400
          - const: amlogic,a5

      - description: Boards with the Amlogic C3 C302X/C308L SoC
        items:
          - enum:
              - amlogic,aw409
              - amlogic,aw419
          - const: amlogic,c3

      - description: Boards with the Amlogic Meson S4 S805X2 SoC
        items:
          - enum:
              - amlogic,aq222
          - const: amlogic,s4

      - description: Boards with the Amlogic S6 S905X5 SoC
        items:
          - enum:
              - amlogic,bl209
          - const: amlogic,s6

      - description: Boards with the Amlogic S7 S805X3 SoC
        items:
          - enum:
              - amlogic,bp201
          - const: amlogic,s7

      - description: Boards with the Amlogic S7D S905X5M SoC
        items:
          - enum:
              - amlogic,bm202
          - const: amlogic,s7d

      - description: Boards with the Amlogic T7 A311D2 SoC
        items:
          - enum:
              - amlogic,an400
              - khadas,vim4
          - const: amlogic,a311d2
          - const: amlogic,t7

additionalProperties: true

...
