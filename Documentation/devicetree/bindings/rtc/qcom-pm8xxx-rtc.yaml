# SPDX-License-Identifier: (GPL-2.0 OR BSD-2-Clause)
%YAML 1.2
---
$id: http://devicetree.org/schemas/rtc/qcom-pm8xxx-rtc.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: Qualcomm PM8xxx PMIC RTC device

maintainers:
  - <PERSON><PERSON><PERSON> <<EMAIL>>

properties:
  compatible:
    oneOf:
      - enum:
          - qcom,pm8058-rtc
          - qcom,pm8921-rtc
          - qcom,pm8941-rtc
          - qcom,pmk8350-rtc
      - items:
          - enum:
              - qcom,pm8018-rtc
          - const: qcom,pm8921-rtc

  reg:
    minItems: 1
    maxItems: 2

  reg-names:
    minItems: 1
    items:
      - const: rtc
      - const: alarm

  interrupts:
    maxItems: 1

  allow-set-time:
    $ref: /schemas/types.yaml#/definitions/flag
    description:
      Indicates that the setting of RTC time is allowed by the host CPU.

  nvmem-cells:
    items:
      - description:
          four-byte nvmem cell holding a little-endian offset from the Unix
          epoch representing the time when the RTC timer was last reset

  nvmem-cell-names:
    items:
      - const: offset

  qcom,no-alarm:
    type: boolean
    description:
      RTC alarm is not owned by the OS

  qcom,uefi-rtc-info:
    type: boolean
    description:
      RTC offset is stored as a four-byte GPS time offset in a 12-byte UEFI
      variable 882f8c2b-9646-435f-8de5-f208ff80c1bd-RTCInfo

  wakeup-source: true

required:
  - compatible
  - reg
  - interrupts

additionalProperties: false

examples:
  - |
    #include <dt-bindings/interrupt-controller/irq.h>
    #include <dt-bindings/spmi/spmi.h>

    spmi {
        #address-cells = <2>;
        #size-cells = <0>;

        pmic@0 {
            compatible = "qcom,pm8941", "qcom,spmi-pmic";
            reg = <0x0 SPMI_USID>;
            #address-cells = <1>;
            #size-cells = <0>;

            rtc@6000 {
                compatible = "qcom,pm8941-rtc";
                reg = <0x6000>, <0x6100>;
                reg-names = "rtc", "alarm";
                interrupts = <0x0 0x61 0x1 IRQ_TYPE_EDGE_RISING>;
                nvmem-cells = <&rtc_offset>;
                nvmem-cell-names = "offset";
            };
        };
    };
...
