# SPDX-License-Identifier: GPL-2.0-only OR BSD-2-Clause
%YAML 1.2
---
$id: http://devicetree.org/schemas/rtc/adi,max31335.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: Analog Devices MAX31335 RTC

maintainers:
  - <PERSON><PERSON> <<EMAIL>>

description:
  Analog Devices MAX31335 I2C RTC ±2ppm Automotive Real-Time Clock with
  Integrated MEMS Resonator.

allOf:
  - $ref: rtc.yaml#

properties:
  compatible:
    enum:
      - adi,max31331
      - adi,max31335

  reg:
    maxItems: 1

  interrupts:
    maxItems: 1

  "#clock-cells":
    description:
      RTC can be used as a clock source through its clock output pin.
    const: 0

  adi,tc-diode:
    description:
      Select the diode configuration for the trickle charger.
      schottky - Schottky diode in series.
      standard+schottky - standard diode + Schottky diode in series.
    enum: [schottky, standard+schottky]

  trickle-resistor-ohms:
    description:
      Selected resistor for trickle charger. Should be specified if trickle
      charger should be enabled.
    enum: [3000, 6000, 11000]

required:
  - compatible
  - reg

unevaluatedProperties: false

examples:
  - |
    #include <dt-bindings/interrupt-controller/irq.h>
    i2c {
        #address-cells = <1>;
        #size-cells = <0>;

        rtc@68 {
            compatible = "adi,max31335";
            reg = <0x68>;
            pinctrl-0 = <&rtc_nint_pins>;
            interrupts-extended = <&gpio1 16 IRQ_TYPE_LEVEL_HIGH>;
            aux-voltage-chargeable = <1>;
            trickle-resistor-ohms = <6000>;
            adi,tc-diode = "schottky";
        };
    };
...
