# SPDX-License-Identifier: GPL-2.0
%YAML 1.2
---
$id: http://devicetree.org/schemas/rtc/atmel,at91rm9200-rtc.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: Atmel AT91 RTC

allOf:
  - $ref: rtc.yaml#

maintainers:
  - <PERSON> <<EMAIL>>

properties:
  compatible:
    oneOf:
      - enum:
          - atmel,at91rm9200-rtc
          - atmel,at91sam9x5-rtc
          - atmel,sama5d4-rtc
          - atmel,sama5d2-rtc
          - microchip,sam9x60-rtc
          - microchip,sama7g5-rtc
      - items:
          - enum:
              - microchip,sam9x7-rtc
              - microchip,sama7d65-rtc
          - const: microchip,sam9x60-rtc

  reg:
    maxItems: 1

  interrupts:
    maxItems: 1

  clocks:
    maxItems: 1

required:
  - compatible
  - reg
  - interrupts
  - clocks

additionalProperties: false

examples:
  - |
    rtc@fffffe00 {
        compatible = "atmel,at91rm9200-rtc";
        reg = <0xfffffe00 0x100>;
        interrupts = <1 4 7>;
        clocks = <&clk32k>;
    };
...
