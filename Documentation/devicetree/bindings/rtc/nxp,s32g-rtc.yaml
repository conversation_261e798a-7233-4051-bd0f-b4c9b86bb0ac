# SPDX-License-Identifier: (GPL-2.0 OR BSD-2-Clause)
%YAML 1.2
---
$id: http://devicetree.org/schemas/rtc/nxp,s32g-rtc.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: NXP S32G2/S32G3 Real Time Clock (RTC)

maintainers:
  - <PERSON><PERSON><PERSON> <<EMAIL>>
  - <PERSON><PERSON><PERSON> <<EMAIL>>

description:
  RTC hardware module present on S32G2/S32G3 SoCs is used as a wakeup source.
  It is not kept alive during system reset and it is not battery-powered.

allOf:
  - $ref: rtc.yaml#

properties:
  compatible:
    oneOf:
      - enum:
          - nxp,s32g2-rtc
      - items:
          - const: nxp,s32g3-rtc
          - const: nxp,s32g2-rtc

  reg:
    maxItems: 1

  interrupts:
    maxItems: 1

  clocks:
    items:
      - description: ipg clock drives the access to the RTC iomapped registers
      - description: Clock source for the RTC module. Can be selected between
          4 different clock sources using an integrated hardware mux.
          On S32G2/S32G3 SoCs, 'source0' is the SIRC clock (~32KHz) and it is
          available during standby and runtime. 'source1' is reserved and cannot
          be used. 'source2' is the FIRC clock and it is only available during
          runtime providing a better resolution (~48MHz). 'source3' is an external
          RTC clock source which can be additionally added in hardware.

  clock-names:
    items:
      - const: ipg
      - enum: [ source0, source1, source2, source3 ]

required:
  - compatible
  - reg
  - interrupts
  - clocks
  - clock-names

additionalProperties: false

examples:
  - |
    #include <dt-bindings/interrupt-controller/arm-gic.h>
    #include <dt-bindings/interrupt-controller/irq.h>

    rtc@40060000 {
        compatible = "nxp,s32g3-rtc",
                     "nxp,s32g2-rtc";
        reg = <0x40060000 0x1000>;
        interrupts = <GIC_SPI 121 IRQ_TYPE_LEVEL_HIGH>;
        clocks = <&clks 54>, <&clks 55>;
        clock-names = "ipg", "source0";
    };
