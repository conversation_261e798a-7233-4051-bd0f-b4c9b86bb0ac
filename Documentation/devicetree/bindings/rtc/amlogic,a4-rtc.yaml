# SPDX-License-Identifier: (GPL-2.0-only OR BSD-2-Clause)
# Copyright (C) 2024 Amlogic, Inc. All rights reserved
%YAML 1.2
---
$id: http://devicetree.org/schemas/rtc/amlogic,a4-rtc.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: Amlogic A4 and A5 RTC

maintainers:
  - Yiting Deng <<EMAIL>>
  - <PERSON><PERSON><PERSON> <<EMAIL>>

allOf:
  - $ref: rtc.yaml#

properties:
  compatible:
    enum:
      - amlogic,a4-rtc
      - amlogic,a5-rtc

  reg:
    maxItems: 1

  clocks:
    items:
      - description: RTC clock source, available 24M or 32K crystal
          oscillator source. when using 24M, need to divide 24M into 32K.
      - description: RTC module accesses the clock of the apb bus.

  clock-names:
    items:
      - const: osc
      - const: sys

  interrupts:
    maxItems: 1

required:
  - compatible
  - reg
  - clocks
  - clock-names
  - interrupts

additionalProperties: false

examples:
  - |
    #include <dt-bindings/interrupt-controller/arm-gic.h>
    apb {
        #address-cells = <2>;
        #size-cells = <2>;

        rtc@8e600 {
            compatible = "amlogic,a4-rtc";
            reg = <0x0 0x8e600 0x0 0x38>;
            clocks = <&xtal_32k>, <&clkc_periphs 1>;
            clock-names = "osc", "sys";
            interrupts = <GIC_SPI 131 IRQ_TYPE_EDGE_RISING>;
        };
    };
