# SPDX-License-Identifier: (GPL-2.0-only OR BSD-2-Clause)
%YAML 1.2
---
$id: http://devicetree.org/schemas/rtc/renesas,rz-rtca3.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: Renesas RTCA-3 Real Time Clock

maintainers:
  - <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>

allOf:
  - $ref: rtc.yaml#

properties:
  compatible:
    items:
      - enum:
          - renesas,r9a08g045-rtca3 # RZ/G3S
      - const: renesas,rz-rtca3

  reg:
    maxItems: 1

  interrupts:
    items:
      - description: Alarm interrupt
      - description: Periodic interrupt
      - description: Carry interrupt

  interrupt-names:
    items:
      - const: alarm
      - const: period
      - const: carry

  clocks:
    items:
      - description: RTC bus clock
      - description: RTC counter clock

  clock-names:
    items:
      - const: bus
      - const: counter

  power-domains:
    maxItems: 1

  resets:
    items:
      - description: VBATTB module reset

required:
  - compatible
  - reg
  - interrupts
  - interrupt-names
  - clocks
  - clock-names
  - power-domains
  - resets

additionalProperties: false

examples:
  - |
    #include <dt-bindings/clock/r9a08g045-cpg.h>
    #include <dt-bindings/clock/renesas,r9a08g045-vbattb.h>
    #include <dt-bindings/interrupt-controller/arm-gic.h>
    #include <dt-bindings/interrupt-controller/irq.h>

    rtc@1004ec00 {
        compatible = "renesas,r9a08g045-rtca3", "renesas,rz-rtca3";
        reg = <0x1004ec00 0x400>;
        interrupts = <GIC_SPI 315 IRQ_TYPE_LEVEL_HIGH>,
                     <GIC_SPI 316 IRQ_TYPE_LEVEL_HIGH>,
                     <GIC_SPI 317 IRQ_TYPE_LEVEL_HIGH>;
        interrupt-names = "alarm", "period", "carry";
        clocks = <&cpg CPG_MOD R9A08G045_VBAT_BCLK>, <&vbattclk VBATTB_VBATTCLK>;
        clock-names = "bus", "counter";
        power-domains = <&cpg>;
        resets = <&cpg R9A08G045_VBAT_BRESETN>;
    };
