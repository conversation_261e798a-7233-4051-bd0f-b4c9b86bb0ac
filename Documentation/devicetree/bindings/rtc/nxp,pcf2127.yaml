# SPDX-License-Identifier: GPL-2.0-only OR BSD-2-Clause
%YAML 1.2
---
$id: http://devicetree.org/schemas/rtc/nxp,pcf2127.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: NXP PCF2127 Real Time Clock

allOf:
  - $ref: rtc.yaml#
  - $ref: /schemas/spi/spi-peripheral-props.yaml#

maintainers:
  - <PERSON> <<EMAIL>>

properties:
  compatible:
    enum:
      - nxp,pca2129
      - nxp,pcf2127
      - nxp,pcf2129
      - nxp,pcf2131

  reg:
    maxItems: 1

  interrupts:
    maxItems: 1

  start-year: true

  reset-source: true

required:
  - compatible
  - reg

unevaluatedProperties: false

examples:
  - |
    #include <dt-bindings/interrupt-controller/irq.h>
    i2c {
        #address-cells = <1>;
        #size-cells = <0>;

        rtc@51 {
            compatible = "nxp,pcf2127";
            reg = <0x51>;
            pinctrl-0 = <&rtc_nint_pins>;
            interrupts-extended = <&gpio1 16 IRQ_TYPE_LEVEL_HIGH>;
            reset-source;
        };
    };

...
