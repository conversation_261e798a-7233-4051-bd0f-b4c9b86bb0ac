# SPDX-License-Identifier: (GPL-2.0-only OR BSD-2-Clause)
%YAML 1.2
---
$id: http://devicetree.org/schemas/rtc/renesas,rzn1-rtc.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: Renesas RZ/N1 SoCs Real-Time Clock

maintainers:
  - <PERSON><PERSON> <<EMAIL>>

allOf:
  - $ref: rtc.yaml#

properties:
  compatible:
    items:
      - enum:
          - renesas,r9a06g032-rtc
      - const: renesas,rzn1-rtc

  reg:
    maxItems: 1

  interrupts:
    minItems: 3
    maxItems: 3

  interrupt-names:
    items:
      - const: alarm
      - const: timer
      - const: pps

  clocks:
    minItems: 1
    maxItems: 2

  clock-names:
    minItems: 1
    items:
      - const: hclk
      - const: xtal

  power-domains:
    maxItems: 1

required:
  - compatible
  - reg
  - interrupts
  - interrupt-names
  - clocks
  - clock-names
  - power-domains

unevaluatedProperties: false

examples:
  - |
    #include <dt-bindings/interrupt-controller/arm-gic.h>
    #include <dt-bindings/clock/r9a06g032-sysctrl.h>
    rtc@40006000 {
       compatible = "renesas,r9a06g032-rtc", "renesas,rzn1-rtc";
       reg = <0x40006000 0x1000>;
       interrupts = <GIC_SPI 66 IRQ_TYPE_EDGE_RISING>,
                    <GIC_SPI 67 IRQ_TYPE_EDGE_RISING>,
                    <GIC_SPI 68 IRQ_TYPE_EDGE_RISING>;
       interrupt-names = "alarm", "timer", "pps";
       clocks = <&sysctrl R9A06G032_HCLK_RTC>;
       clock-names = "hclk";
       power-domains = <&sysctrl>;
       start-year = <2000>;
     };
