# SPDX-License-Identifier: GPL-2.0-only OR BSD-2-Clause
%YAML 1.2
---
$id: http://devicetree.org/schemas/rtc/sprd,sc2731-rtc.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: Spreadtrum SC2731 Real Time Clock

maintainers:
  - <PERSON><PERSON> <orson<PERSON><PERSON>@gmail.com>
  - <PERSON><PERSON> <<EMAIL>>
  - <PERSON><PERSON> <<EMAIL>>

properties:
  compatible:
    const: sprd,sc2731-rtc

  reg:
    maxItems: 1

  interrupts:
    maxItems: 1

required:
  - compatible
  - reg
  - interrupts

allOf:
  - $ref: rtc.yaml#

unevaluatedProperties: false
...
