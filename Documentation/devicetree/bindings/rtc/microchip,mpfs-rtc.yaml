# SPDX-License-Identifier: (GPL-2.0-only OR BSD-2-Clause)
%YAML 1.2
---
$id: http://devicetree.org/schemas/rtc/microchip,mpfs-rtc.yaml#

$schema: http://devicetree.org/meta-schemas/core.yaml#

title: Microchip PolarFire Soc (MPFS) RTC

allOf:
  - $ref: rtc.yaml#

maintainers:
  - <PERSON><PERSON> <<EMAIL>>

properties:
  compatible:
    oneOf:
      - items:
          - const: microchip,pic64gx-rtc
          - const: microchip,mpfs-rtc
      - const: microchip,mpfs-rtc

  reg:
    maxItems: 1

  interrupts:
    items:
      - description: |
          RTC_WAKEUP interrupt
      - description: |
          RTC_MATCH, asserted when the content of the Alarm register is equal
          to that of the RTC's count register.

  clocks:
    items:
      - description: |
          AHB clock
      - description: |
          Reference clock: divided by the prescaler to create a time-based
          strobe (typically 1 Hz) for the calendar counter. By default, the rtc
          on the PolarFire SoC shares it's reference with MTIMER so this will
          be a 1 MHz clock.

  clock-names:
    items:
      - const: rtc
      - const: rtcref

required:
  - compatible
  - reg
  - interrupts
  - clocks
  - clock-names

additionalProperties: false

examples:
  - |
    #include "dt-bindings/clock/microchip,mpfs-clock.h"
    rtc@20124000 {
        compatible = "microchip,mpfs-rtc";
        reg = <0x20124000 0x1000>;
        clocks = <&clkcfg CLK_RTC>, <&clkcfg CLK_RTCREF>;
        clock-names = "rtc", "rtcref";
        interrupts = <80>, <81>;
    };
...
