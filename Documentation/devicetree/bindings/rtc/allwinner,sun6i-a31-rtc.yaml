# SPDX-License-Identifier: GPL-2.0
%YAML 1.2
---
$id: http://devicetree.org/schemas/rtc/allwinner,sun6i-a31-rtc.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: Allwinner A31 RTC

maintainers:
  - <PERSON><PERSON><PERSON> <<EMAIL>>
  - <PERSON><PERSON> <<EMAIL>>

properties:
  "#clock-cells":
    const: 1

  compatible:
    oneOf:
      - enum:
          - allwinner,sun6i-a31-rtc
          - allwinner,sun8i-a23-rtc
          - allwinner,sun8i-h3-rtc
          - allwinner,sun8i-r40-rtc
          - allwinner,sun8i-v3-rtc
          - allwinner,sun50i-h5-rtc
          - allwinner,sun50i-h6-rtc
          - allwinner,sun50i-h616-rtc
          - allwinner,sun50i-r329-rtc
      - items:
          - const: allwinner,sun50i-a64-rtc
          - const: allwinner,sun8i-h3-rtc
      - items:
          - enum:
              - allwinner,sun20i-d1-rtc
              - allwinner,sun55i-a523-rtc
          - const: allwinner,sun50i-r329-rtc

  reg:
    maxItems: 1

  interrupts:
    minItems: 1
    items:
      - description: RTC Alarm 0
      - description: RTC Alarm 1

  clocks:
    minItems: 1
    maxItems: 4

  clock-names:
    minItems: 1
    maxItems: 4

  clock-output-names:
    minItems: 1
    maxItems: 3
    description:
      The RTC provides up to three clocks
        - the Low Frequency Oscillator or LOSC, at index 0,
        - the Low Frequency Oscillator External output (X32KFOUT in
          the datasheet), at index 1,
        - the Internal Oscillator, at index 2.

allOf:
  - $ref: rtc.yaml#
  - if:
      properties:
        compatible:
          contains:
            const: allwinner,sun6i-a31-rtc

    then:
      properties:
        clock-output-names:
          maxItems: 1

  - if:
      properties:
        compatible:
          contains:
            enum:
              - allwinner,sun8i-a23-rtc
              - allwinner,sun8i-r40-rtc
              - allwinner,sun8i-v3-rtc

    then:
      properties:
        clock-output-names:
          minItems: 2
          maxItems: 2

  - if:
      properties:
        compatible:
          contains:
            enum:
              - allwinner,sun8i-h3-rtc
              - allwinner,sun50i-h5-rtc
              - allwinner,sun50i-h6-rtc

    then:
      properties:
        clock-output-names:
          minItems: 3

  - if:
      properties:
        compatible:
          contains:
            const: allwinner,sun50i-h616-rtc

    then:
      properties:
        clocks:
          items:
            - description: Bus clock for register access
            - description: 24 MHz oscillator
            - description: 32 kHz clock from the CCU

        clock-names:
          items:
            - const: bus
            - const: hosc
            - const: pll-32k

      required:
        - clocks
        - clock-names

  - if:
      properties:
        compatible:
          contains:
            const: allwinner,sun50i-r329-rtc

    then:
      properties:
        clocks:
          minItems: 3
          items:
            - description: Bus clock for register access
            - description: 24 MHz oscillator
            - description: AHB parent for internal SPI clock
            - description: External 32768 Hz oscillator

        clock-names:
          minItems: 3
          items:
            - const: bus
            - const: hosc
            - const: ahb
            - const: ext-osc32k

      required:
        - clocks
        - clock-names

  - if:
      properties:
        compatible:
          contains:
            enum:
              - allwinner,sun8i-r40-rtc
              - allwinner,sun50i-h616-rtc
              - allwinner,sun50i-r329-rtc

    then:
      properties:
        interrupts:
          maxItems: 1

    else:
      properties:
        interrupts:
          minItems: 2

required:
  - "#clock-cells"
  - compatible
  - reg
  - interrupts

additionalProperties: false

examples:
  - |
    rtc: rtc@1f00000 {
        compatible = "allwinner,sun6i-a31-rtc";
        reg = <0x01f00000 0x400>;
        interrupts = <0 40 4>, <0 41 4>;
        clock-output-names = "osc32k";
        clocks = <&ext_osc32k>;
        #clock-cells = <1>;
    };

...
