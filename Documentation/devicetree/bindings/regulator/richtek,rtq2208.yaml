# SPDX-License-Identifier: GPL-2.0-only OR BSD-2-Clause
%YAML 1.2
---
$id: http://devicetree.org/schemas/regulator/richtek,rtq2208.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: Richtek RTQ2208 SubPMIC Regulator

maintainers:
  - <PERSON><PERSON> <<EMAIL>>

description: |
  RTQ2208 is a highly integrated power converter that offers functional safety dual
  multi-configurable synchronous buck converters and two LDOs.

  Bucks support "regulator-allowed-modes" and "regulator-mode". The former defines the permitted
  switching operation in normal mode; the latter defines the operation in suspend to RAM mode.

  No matter the RTQ2208 is configured to normal or suspend to RAM mode, there are two switching
  operation modes for all buck rails, automatic power saving mode (Auto mode) and forced continuous
  conduction mode (FCCM).

  The definition of modes is in the datasheet which is available in below link
  and their meaning is::
    0 - Auto mode for power saving, which reducing the switching frequency at light load condition
    to maintain high frequency.
    1 - FCCM to meet the strict voltage regulation accuracy, which keeping constant switching frequency.

  Datasheet will be available soon at
  https://www.richtek.com/assets/Products

properties:
  compatible:
    enum:
      - richtek,rtq2208

  reg:
    maxItems: 1

  interrupts:
    maxItems: 1

  richtek,mtp-sel-high:
    type: boolean
    description:
      vout register selection based on this boolean value.
      false - Using DVS0 register setting to adjust vout
      true - Using DVS1 register setting to adjust vout

  regulators:
    type: object
    additionalProperties: false

    patternProperties:
      "^buck-[a-h]$":
        type: object
        $ref: regulator.yaml#
        unevaluatedProperties: false
        description:
          description for buck-[a-h] regulator.

        properties:
          regulator-allowed-modes:
            description:
              two buck modes in different switching accuracy.
              0 - Auto mode
              1 - FCCM
            items:
              enum: [0, 1]

      "^ldo[1-2]$":
        type: object
        $ref: regulator.yaml#
        unevaluatedProperties: false
        description:
          regulator description for ldo[1-2].

        properties:
          richtek,fixed-microvolt:
            deprecated: true
            description: |
              This property can be used to set a fixed operating voltage that lies outside
              the range of the regulator's adjustable mode.

required:
  - compatible
  - reg
  - regulators

additionalProperties: false

examples:
  - |
    #include <dt-bindings/interrupt-controller/irq.h>
    i2c {
      #address-cells = <1>;
      #size-cells = <0>;

      pmic@10 {
        compatible = "richtek,rtq2208";
        reg = <0x10>;
        interrupts-extended = <&gpio26 0 IRQ_TYPE_LEVEL_LOW>;
        richtek,mtp-sel-high;

        regulators {
          buck-a {
            regulator-min-microvolt = <400000>;
            regulator-max-microvolt = <2050000>;
            regulator-allowed-modes = <0 1>;
            regulator-always-on;
            regulator-state-mem {
              regulator-on-in-suspend;
              regulator-mode = <1>;
            };
          };
          buck-b {
            regulator-min-microvolt = <400000>;
            regulator-max-microvolt = <2050000>;
            regulator-allowed-modes = <0 1>;
            regulator-always-on;
            regulator-state-mem {
              regulator-on-in-suspend;
              regulator-mode = <1>;
            };
          };
          buck-c {
            regulator-min-microvolt = <400000>;
            regulator-max-microvolt = <2050000>;
            regulator-allowed-modes = <0 1>;
            regulator-always-on;
            regulator-state-mem {
              regulator-on-in-suspend;
              regulator-mode = <1>;
            };
          };
          buck-d {
            regulator-min-microvolt = <400000>;
            regulator-max-microvolt = <2050000>;
            regulator-allowed-modes = <0 1>;
            regulator-always-on;
            regulator-state-mem {
              regulator-on-in-suspend;
              regulator-mode = <1>;
            };
          };
          buck-e {
            regulator-min-microvolt = <400000>;
            regulator-max-microvolt = <2050000>;
            regulator-allowed-modes = <0 1>;
            regulator-always-on;
            regulator-state-mem {
              regulator-on-in-suspend;
              regulator-mode = <1>;
            };
          };
          buck-f {
            regulator-min-microvolt = <400000>;
            regulator-max-microvolt = <2050000>;
            regulator-allowed-modes = <0 1>;
            regulator-always-on;
            regulator-state-mem {
              regulator-on-in-suspend;
              regulator-mode = <1>;
            };
          };
          buck-g {
            regulator-min-microvolt = <400000>;
            regulator-max-microvolt = <2050000>;
            regulator-allowed-modes = <0 1>;
            regulator-always-on;
            regulator-state-mem {
              regulator-on-in-suspend;
              regulator-mode = <1>;
            };
          };
          buck-h {
            regulator-min-microvolt = <400000>;
            regulator-max-microvolt = <2050000>;
            regulator-allowed-modes = <0 1>;
            regulator-always-on;
            regulator-state-mem {
              regulator-on-in-suspend;
              regulator-mode = <1>;
            };
          };
          ldo1 {
            /* Fixed LDO VOUT */
            richtek,fixed-microvolt = <1200000>;
            regulator-min-microvolt = <1200000>;
            regulator-max-microvolt = <1200000>;
            regulator-always-on;
            regulator-state-mem {
              regulator-on-in-suspend;
            };
          };
          ldo2 {
            /* Adjustable LDO VOUT */
            regulator-min-microvolt = <1800000>;
            regulator-max-microvolt = <3300000>;
            regulator-always-on;
            regulator-state-mem {
              regulator-on-in-suspend;
            };
          };
        };
      };
    };
