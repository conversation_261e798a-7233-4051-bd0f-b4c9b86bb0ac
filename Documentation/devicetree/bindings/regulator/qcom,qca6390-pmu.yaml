# SPDX-License-Identifier: (GPL-2.0 OR BSD-2-Clause)
%YAML 1.2
---
$id: http://devicetree.org/schemas/regulator/qcom,qca6390-pmu.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: Qualcomm Technologies, Inc. QCA6390 PMU Regulators

maintainers:
  - <PERSON><PERSON><PERSON> <bartosz.go<PERSON><PERSON><PERSON>@linaro.org>

description:
  The QCA6390 package contains discrete modules for WLAN and Bluetooth. They
  are powered by the Power Management Unit (PMU) that takes inputs from the
  host and provides LDO outputs. This document describes this module.

properties:
  compatible:
    enum:
      - qcom,qca6390-pmu
      - qcom,wcn6750-pmu
      - qcom,wcn6855-pmu
      - qcom,wcn7850-pmu

  vdd-supply:
    description: VDD supply regulator handle

  vddaon-supply:
    description: VDD_AON supply regulator handle

  vddasd-supply:
    description: VDD_ASD supply regulator handle

  vdddig-supply:
    description: VDD_DIG supply regulator handle

  vddpmu-supply:
    description: VDD_PMU supply regulator handle

  vddpmumx-supply:
    description: VDD_PMU_MX supply regulator handle

  vddpmucx-supply:
    description: VDD_PMU_CX supply regulator handle

  vddio1p2-supply:
    description: VDD_IO_1P2 supply regulator handle

  vddrfa0p8-supply:
    description: VDD_RFA_0P8 supply regulator handle

  vddrfa0p95-supply:
    description: VDD_RFA_0P95 supply regulator handle

  vddrfa1p2-supply:
    description: VDD_RFA_1P2 supply regulator handle

  vddrfa1p3-supply:
    description: VDD_RFA_1P3 supply regulator handle

  vddrfa1p7-supply:
    description: VDD_RFA_1P7 supply regulator handle

  vddrfa1p8-supply:
    description: VDD_RFA_1P8 supply regulator handle

  vddrfa1p9-supply:
    description: VDD_RFA_1P9 supply regulator handle

  vddrfa2p2-supply:
    description: VDD_RFA_2P2 supply regulator handle

  vddpcie1p3-supply:
    description: VDD_PCIE_1P3 supply regulator handle

  vddpcie1p9-supply:
    description: VDD_PCIE_1P9 supply regulator handle

  vddio-supply:
    description: VDD_IO supply regulator handle

  wlan-enable-gpios:
    maxItems: 1
    description: GPIO line enabling the ATH11K WLAN module supplied by the PMU

  bt-enable-gpios:
    maxItems: 1
    description: GPIO line enabling the Bluetooth module supplied by the PMU

  swctrl-gpios:
    maxItems: 1
    description: GPIO line indicating the state of the clock supply to the BT module

  xo-clk-gpios:
    maxItems: 1
    description: GPIO line allowing to select the XO clock configuration for the module

  clocks:
    maxItems: 1
    description: Reference clock handle

  regulators:
    type: object
    description:
      LDO outputs of the PMU

    patternProperties:
      "^ldo[0-9]$":
        $ref: regulator.yaml#
        type: object
        unevaluatedProperties: false

    additionalProperties: false

required:
  - compatible
  - regulators

allOf:
  - if:
      properties:
        compatible:
          contains:
            const: qcom,qca6390-pmu
    then:
      required:
        - vddaon-supply
        - vddpmu-supply
        - vddrfa0p95-supply
        - vddrfa1p3-supply
        - vddrfa1p9-supply
        - vddpcie1p3-supply
        - vddpcie1p9-supply
        - vddio-supply
  - if:
      properties:
        compatible:
          contains:
            const: qcom,wcn6750-pmu
    then:
      required:
        - vddaon-supply
        - vddasd-supply
        - vddpmu-supply
        - vddrfa0p8-supply
        - vddrfa1p2-supply
        - vddrfa1p7-supply
        - vddrfa2p2-supply
  - if:
      properties:
        compatible:
          contains:
            const: qcom,wcn6855-pmu
    then:
      required:
        - vddio-supply
        - vddaon-supply
        - vddpmu-supply
        - vddpmumx-supply
        - vddpmucx-supply
        - vddrfa0p95-supply
        - vddrfa1p3-supply
        - vddrfa1p9-supply
        - vddpcie1p3-supply
        - vddpcie1p9-supply
  - if:
      properties:
        compatible:
          contains:
            const: qcom,wcn7850-pmu
    then:
      required:
        - vdd-supply
        - vddio-supply
        - vddaon-supply
        - vdddig-supply
        - vddrfa1p2-supply
        - vddrfa1p8-supply

additionalProperties: false

examples:
  - |
    #include <dt-bindings/gpio/gpio.h>
    pmu {
        compatible = "qcom,qca6390-pmu";

        pinctrl-names = "default";
        pinctrl-0 = <&bt_en_state>, <&wlan_en_state>;

        vddaon-supply = <&vreg_s6a_0p95>;
        vddpmu-supply = <&vreg_s2f_0p95>;
        vddrfa0p95-supply = <&vreg_s2f_0p95>;
        vddrfa1p3-supply = <&vreg_s8c_1p3>;
        vddrfa1p9-supply = <&vreg_s5a_1p9>;
        vddpcie1p3-supply = <&vreg_s8c_1p3>;
        vddpcie1p9-supply = <&vreg_s5a_1p9>;
        vddio-supply = <&vreg_s4a_1p8>;

        wlan-enable-gpios = <&tlmm 20 GPIO_ACTIVE_HIGH>;
        bt-enable-gpios = <&tlmm 21 GPIO_ACTIVE_HIGH>;

        regulators {
            vreg_pmu_rfa_cmn: ldo0 {
                regulator-name = "vreg_pmu_rfa_cmn";
            };

            vreg_pmu_aon_0p59: ldo1 {
                regulator-name = "vreg_pmu_aon_0p59";
            };

            vreg_pmu_wlcx_0p8: ldo2 {
                regulator-name = "vreg_pmu_wlcx_0p8";
            };

            vreg_pmu_wlmx_0p85: ldo3 {
                regulator-name = "vreg_pmu_wlmx_0p85";
            };

            vreg_pmu_btcmx_0p85: ldo4 {
                regulator-name = "vreg_pmu_btcmx_0p85";
            };

            vreg_pmu_rfa_0p8: ldo5 {
                regulator-name = "vreg_pmu_rfa_0p8";
            };

            vreg_pmu_rfa_1p2: ldo6 {
                regulator-name = "vreg_pmu_rfa_1p2";
            };

            vreg_pmu_rfa_1p7: ldo7 {
                regulator-name = "vreg_pmu_rfa_1p7";
            };

            vreg_pmu_pcie_0p9: ldo8 {
                regulator-name = "vreg_pmu_pcie_0p9";
            };

            vreg_pmu_pcie_1p8: ldo9 {
                regulator-name = "vreg_pmu_pcie_1p8";
            };
        };
    };
