# SPDX-License-Identifier: GPL-2.0-only OR BSD-2-Clause
%YAML 1.2
---
$id: http://devicetree.org/schemas/regulator/sprd,sc2731-regulator.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: Spreadtrum SC2731 Power Management IC regulators

maintainers:
  - <PERSON><PERSON> <orson<PERSON><PERSON>@gmail.com>
  - <PERSON><PERSON> <<EMAIL>>
  - <PERSON><PERSON> <<EMAIL>>

description: |
  The SC2731 integrates low-voltage and low quiescent current DCDC/LDO.
  14 LDO and 3 DCDCs are designed for external use. All DCDCs/LDOs have
  their own bypass (power-down) control signals. It is recommended to use
  external tantalum or MLCC ceramic capacitors with these LDOs.
  Valid names for the regulators are:
    BUCK:
      BUCK_CPU0, BUCK_CPU1, BUCK_RF
    LDO:
      LDO_CAMA0, LDO_CAMA1, LDO_CAMD0, LDO_CAMD1, <PERSON><PERSON><PERSON>_CAMIO, <PERSON><PERSON><PERSON>_<PERSON>MMOT,
      <PERSON><PERSON><PERSON>_CON, LDO_EMMCCORE, LD<PERSON>_SDCORE, LDO_SDIO, LDO_SRAM, LDO_USB33,
      LDO_VLDO, LDO_WIFIPA

properties:
  compatible:
    const: sprd,sc2731-regulator

patternProperties:
  "^BUCK_(CPU[0-1]|RF)$":
    type: object
    $ref: regulator.yaml#
    unevaluatedProperties: false

  "^LDO_(CAM(A0|A1|D0|D1|IO|MOT)|CON|EMMCCORE|SD(CORE|IO)|SRAM|USB33|VLDO|WIFIPA)$":
    type: object
    $ref: regulator.yaml#
    unevaluatedProperties: false

required:
  - compatible

additionalProperties: false
...
