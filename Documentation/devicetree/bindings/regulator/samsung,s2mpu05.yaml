# SPDX-License-Identifier: GPL-2.0-only OR BSD-2-Clause
%YAML 1.2
---
$id: http://devicetree.org/schemas/regulator/samsung,s2mpu05.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: Samsung S2MPU05 Power Management IC regulators

maintainers:
  - <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>

description: |
  This is a part of device tree bindings for S2M and S5M family of Power
  Management IC (PMIC).

  The S2MPU05 provides buck and LDO regulators.

  See also Documentation/devicetree/bindings/mfd/samsung,s2mps11.yaml for
  additional information and example.

patternProperties:
  # 21 LDOs
  "^ldo([1-9]|10|2[5-9]|3[0-5])$":
    type: object
    $ref: regulator.yaml#
    unevaluatedProperties: false
    description:
      Properties for single LDO regulator.

      LDOs 11-24 are used for CP, and they're left unimplemented due to lack
      of documentation on these regulators.

    required:
      - regulator-name

  # 5 bucks
  "^buck[1-5]$":
    type: object
    $ref: regulator.yaml#
    unevaluatedProperties: false
    description:
      Properties for single buck regulator.

    required:
      - regulator-name

additionalProperties: false
