# SPDX-License-Identifier: GPL-2.0-only OR BSD-2-Clause
%YAML 1.2
---
$id: http://devicetree.org/schemas/regulator/rohm,bd96802-regulator.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: ROHM BD96802 Power Management Integrated Circuit regulators

maintainers:
  - <PERSON><PERSON> <<EMAIL>>

description:
  This module is part of the ROHM BD96802 MFD device. For more details
  see Documentation/devicetree/bindings/mfd/rohm,bd96802-pmic.yaml.

  The regulator controller is represented as a sub-node of the PMIC node
  on the device tree.

  Regulator nodes should be named to buck1 and buck2.

patternProperties:
  "^buck[1-2]$":
    type: object
    description:
      Properties for single BUCK regulator.
    $ref: regulator.yaml#

    properties:
      rohm,initial-voltage-microvolt:
        description:
          Initial voltage for regulator. Voltage can be tuned +/-150 mV from
          this value. NOTE, This can be modified via I2C only when PMIC is in
          STBY state.
        minimum: 500000
        maximum: 3300000

      rohm,keep-on-stby:
        description:
          Keep the regulator powered when PMIC transitions to STBY state.
        type: boolean

    unevaluatedProperties: false

additionalProperties: false
