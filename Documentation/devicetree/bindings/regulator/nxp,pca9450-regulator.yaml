# SPDX-License-Identifier: GPL-2.0-only OR BSD-2-Clause
%YAML 1.2
---
$id: http://devicetree.org/schemas/regulator/nxp,pca9450-regulator.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: NXP PCA9450A/B/C Power Management Integrated Circuit regulators

maintainers:
  - <PERSON> <<EMAIL>>

description: |
  Regulator nodes should be named to BUCK_<number> and LDO_<number>. The
  definition for each of these nodes is defined using the standard
  binding for regulators at
  Documentation/devicetree/bindings/regulator/regulator.txt.
  Datasheet is available at
  https://www.nxp.com/docs/en/data-sheet/PCA9450DS.pdf

  Support PF9453, Datasheet is available at
  https://www.nxp.com/docs/en/data-sheet/PF9453_SDS.pdf

# The valid names for PCA9450 regulator nodes are:
# <PERSON><PERSON><PERSON>1, <PERSON><PERSON><PERSON>2, <PERSON><PERSON><PERSON>3, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>,
# <PERSON><PERSON><PERSON>1, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>
# Note: Buck3 removed on PCA9450B and connect with <PERSON><PERSON> on PCA9450C.

properties:
  compatible:
    enum:
      - nxp,pca9450a
      - nxp,pca9450b
      - nxp,pca9450c
      - nxp,pca9451a
      - nxp,pca9452
      - nxp,pf9453

  reg:
    maxItems: 1

  interrupts:
    maxItems: 1

  regulators:
    type: object
    description: |
      list of regulators provided by this controller

    properties:
      LDO5:
        type: object
        $ref: regulator.yaml#
        description:
          Properties for single LDO5 regulator.

        properties:
          nxp,sd-vsel-fixed-low:
            type: boolean
            description:
              Let the driver know that SD_VSEL is hardwired to low level and
              there is no GPIO to get the actual value from.

          sd-vsel-gpios:
            description:
              GPIO that can be used to read the current status of the SD_VSEL
              signal in order for the driver to know if LDO5CTRL_L or LDO5CTRL_H
              is used by the hardware.

        unevaluatedProperties: false

    patternProperties:
      "^LDO([1-4]|-SNVS)$":
        type: object
        $ref: regulator.yaml#
        description:
          Properties for single LDO regulator.

        unevaluatedProperties: false

      "^BUCK[1-6]$":
        type: object
        $ref: regulator.yaml#
        description:
          Properties for single BUCK regulator.

        properties:
          nxp,dvs-run-voltage:
            $ref: /schemas/types.yaml#/definitions/uint32
            minimum: 600000
            maximum: 2187500
            description:
              PMIC default "RUN" state voltage in uV. Only Buck1~3 have such
              dvs(dynamic voltage scaling) property.

          nxp,dvs-standby-voltage:
            $ref: /schemas/types.yaml#/definitions/uint32
            minimum: 600000
            maximum: 2187500
            description:
              PMIC default "STANDBY" state voltage in uV. Only Buck1~3 have such
              dvs(dynamic voltage scaling) property.

        unevaluatedProperties: false

    additionalProperties: false

  nxp,i2c-lt-enable:
    type: boolean
    description:
      Indicates that the I2C Level Translator is used.

  nxp,wdog_b-warm-reset:
    type: boolean
    description:
      When WDOG_B signal is asserted a warm reset will be done instead of cold
      reset.

required:
  - compatible
  - reg
  - regulators

additionalProperties: false

allOf:
  - if:
      properties:
        compatible:
          contains:
            const: nxp,pf9453
    then:
      properties:
        regulators:
          patternProperties:
            "^LDO[3-4]$": false
            "^BUCK[5-6]$": false
    else:
      properties:
        regulators:
          properties:
            LDO-SNVS: false

examples:
  - |
    #include <dt-bindings/interrupt-controller/irq.h>

    i2c {
        #address-cells = <1>;
        #size-cells = <0>;
        pmic: pmic@25 {
            compatible = "nxp,pca9450b";
            reg = <0x25>;
            pinctrl-0 = <&pinctrl_pmic>;
            interrupt-parent = <&gpio1>;
            interrupts = <3 IRQ_TYPE_LEVEL_LOW>;

            regulators {
                buck1: BUCK1 {
                    regulator-name = "BUCK1";
                    regulator-min-microvolt = <600000>;
                    regulator-max-microvolt = <2187500>;
                    regulator-boot-on;
                    regulator-always-on;
                    regulator-ramp-delay = <3125>;
                };
                buck2: BUCK2 {
                    regulator-name = "BUCK2";
                    regulator-min-microvolt = <600000>;
                    regulator-max-microvolt = <2187500>;
                    regulator-boot-on;
                    regulator-always-on;
                    regulator-ramp-delay = <3125>;
                    nxp,dvs-run-voltage = <950000>;
                    nxp,dvs-standby-voltage = <850000>;
                };
                buck4: BUCK4 {
                    regulator-name = "BUCK4";
                    regulator-min-microvolt = <600000>;
                    regulator-max-microvolt = <3400000>;
                    regulator-boot-on;
                    regulator-always-on;
                };
                buck5: BUCK5 {
                    regulator-name = "BUCK5";
                    regulator-min-microvolt = <600000>;
                    regulator-max-microvolt = <3400000>;
                    regulator-boot-on;
                    regulator-always-on;
                };
                buck6: BUCK6 {
                    regulator-name = "BUCK6";
                    regulator-min-microvolt = <600000>;
                    regulator-max-microvolt = <3400000>;
                    regulator-boot-on;
                    regulator-always-on;
                };

                ldo1: LDO1 {
                    regulator-name = "LDO1";
                    regulator-min-microvolt = <1600000>;
                    regulator-max-microvolt = <3300000>;
                    regulator-boot-on;
                    regulator-always-on;
                };
                ldo2: LDO2 {
                    regulator-name = "LDO2";
                    regulator-min-microvolt = <800000>;
                    regulator-max-microvolt = <1150000>;
                    regulator-boot-on;
                    regulator-always-on;
                };
                ldo3: LDO3 {
                    regulator-name = "LDO3";
                    regulator-min-microvolt = <800000>;
                    regulator-max-microvolt = <3300000>;
                    regulator-boot-on;
                    regulator-always-on;
                };
                ldo4: LDO4 {
                    regulator-name = "LDO4";
                    regulator-min-microvolt = <800000>;
                    regulator-max-microvolt = <3300000>;
                    regulator-boot-on;
                    regulator-always-on;
                };
                ldo5: LDO5 {
                    regulator-name = "LDO5";
                    regulator-min-microvolt = <1800000>;
                    regulator-max-microvolt = <3300000>;
                    regulator-boot-on;
                    regulator-always-on;
                };
            };
        };
    };
