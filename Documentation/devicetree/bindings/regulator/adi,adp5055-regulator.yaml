# SPDX-License-Identifier: (GPL-2.0 OR BSD-2-Clause)
%YAML 1.2
---
$id: http://devicetree.org/schemas/regulator/adi,adp5055-regulator.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: Analog Devices ADP5055 Triple Buck Regulator

maintainers:
  - <PERSON> <<EMAIL>>

description: |
  The ADP5055 combines three high performance buck regulator. The device enables
  direct connection to high input voltages up to 18 V with no preregulators.
  https://www.analog.com/media/en/technical-documentation/data-sheets/adp5055.pdf

properties:
  compatible:
    enum:
      - adi,adp5055

  reg:
    enum:
      - 0x70
      - 0x71

  adi,tset-us:
    description:
      Setting time used by the device. This is changed via soldering specific
      resistor values on the CFG2 pin.
    enum: [2600, 20800]
    default: 2600

  adi,ocp-blanking:
    description:
      If present, overcurrent protection (OCP) blanking for all regulator is on.
    type: boolean

  adi,delay-power-good:
    description:
      Configures delay timer of the power good (PWRGD) pin. Delay is based on
      Tset which can be 2.6 ms or 20.8 ms.
    type: boolean

  '#address-cells':
    const: 1

  '#size-cells':
    const: 0

patternProperties:
  '^buck[0-2]$':
    type: object
    $ref: regulator.yaml#
    unevaluatedProperties: false

    properties:
      enable-gpios:
        maxItems: 1
        description:
          GPIO specifier to enable the GPIO control for each regulator. The
          driver supports two modes of enable, hardware only (GPIOs) or software
          only (Registers). Pure hardware enabling requires each regulator to
          contain this property. If at least one regulator does not have this,
          the driver automatically switches to software only mode.

      adi,dvs-limit-upper-microvolt:
        description:
          Configure the allowable upper side limit of the voltage output of each
          regulator in microvolt. Relative to the default Vref trimming value.
          Vref = 600 mV. Voltages are in 12 mV steps, value is autoadjusted.
          Vout_high = Vref_trim + dvs-limit-upper.
        minimum: 12000
        maximum: 192000
        default: 192000

      adi,dvs-limit-lower-microvolt:
        description:
          Configure the allowable lower side limit of the voltage output of each
          regulator in microvolt. Relative to the default Vref trimming value.
          Vref = 600 mV. Voltages are in 12 mV steps, value is autoadjusted.
          Vout_low = Vref_trim + dvs-limit-lower.
        minimum: -190500
        maximum: -10500
        default: -190500

      adi,fast-transient:
        description:
          Configures the fast transient sensitivity for each regulator.
          "none"    - No fast transient.
          "3G_1.5%" - 1.5% window with 3*350uA/V
          "5G_1.5%" - 1.5% window with 5*350uA/V
          "5G_2.5%" - 2.5% window with 5*350uA/V
        enum: [none, 3G_1.5%, 5G_1.5%, 5G_2.5%]
        default: 5G_2.5%

      adi,mask-power-good:
        description:
          If present, masks individual regulators PWRGD signal to the external
          PWRGD hardware pin.
        type: boolean

    required:
      - regulator-name

required:
  - compatible
  - reg

additionalProperties: false

examples:
  - |
    #include <dt-bindings/gpio/gpio.h>

    i2c {
        #address-cells = <1>;
        #size-cells = <0>;

        regulator@70 {
            compatible = "adi,adp5055";
            reg = <0x70>;
            #address-cells = <1>;
            #size-cells = <0>;

            adi,tset-us = <2600>;
            adi,ocp-blanking;
            adi,delay-power-good;

            buck0 {
                regulator-name = "buck0";
                enable-gpios = <&gpio 17 GPIO_ACTIVE_HIGH>;
                adi,dvs-limit-upper-microvolt = <192000>;
                adi,dvs-limit-lower-microvolt = <(-190500)>;
                adi,fast-transient = "5G_2.5%";
                adi,mask-power-good;
            };

            buck1 {
                regulator-name = "buck1";
                enable-gpios = <&gpio 18 GPIO_ACTIVE_HIGH>;
                adi,dvs-limit-upper-microvolt = <192000>;
                adi,dvs-limit-lower-microvolt = <(-190500)>;
                adi,fast-transient = "5G_2.5%";
                adi,mask-power-good;
            };

            buck2 {
                regulator-name = "buck2";
                enable-gpios = <&gpio 19 GPIO_ACTIVE_HIGH>;
                adi,dvs-limit-upper-microvolt = <192000>;
                adi,dvs-limit-lower-microvolt = <(-190500)>;
                adi,fast-transient = "5G_2.5%";
                adi,mask-power-good;
            };
        };
    };
