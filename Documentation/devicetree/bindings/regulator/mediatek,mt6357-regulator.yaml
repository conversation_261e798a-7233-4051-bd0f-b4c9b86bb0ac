# SPDX-License-Identifier: (GPL-2.0 OR BSD-2-Clause)
%YAML 1.2
---
$id: http://devicetree.org/schemas/regulator/mediatek,mt6357-regulator.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: MediaTek MT6357 Regulators

maintainers:
  - <PERSON> <<EMAIL>>
  - Fabien Parent <<EMAIL>>
  - <PERSON> <<EMAIL>>

description: |
  The MT6357 PMIC provides 5 BUCK and 29 LDO.
  Regulators and nodes are named according to the regulator type:
  - buck-<name>
  - ldo-<name>.
  MT6357 regulators node should be sub node of the MT6397 MFD node.

patternProperties:
  "^buck-v(core|modem|pa|proc|s1)$":
    type: object
    $ref: regulator.yaml#
    unevaluatedProperties: false
    description:
      Properties for single BUCK regulator.

    required:
      - regulator-name
      - regulator-min-microvolt
      - regulator-max-microvolt

  "^ldo-v(camio18|aud28|aux18|io18|io28|rf12|rf18|cn18|cn28|fe28)$":
    type: object
    $ref: regulator.yaml#
    unevaluatedProperties: false
    description:
      Properties for single fixed LDO regulator.

    required:
      - regulator-name
      - regulator-min-microvolt
      - regulator-max-microvolt

  "^ldo-v(efuse|ibr|ldo28|mch|cama|camd|cn33-bt|cn33-wifi)$":
    type: object
    $ref: regulator.yaml#
    unevaluatedProperties: false
    description:
      Properties for single LDO regulator.

    required:
      - regulator-name
      - regulator-min-microvolt
      - regulator-max-microvolt

  "^ldo-v(xo22|emc|mc|sim1|sim2|sram-others|sram-proc|dram|usb33)$":
    type: object
    $ref: regulator.yaml#
    unevaluatedProperties: false
    description:
      Properties for single LDO regulator.

    required:
      - regulator-name
      - regulator-min-microvolt
      - regulator-max-microvolt

additionalProperties: false

examples:
  - |
    pmic {
      regulators {
        mt6357_vproc_reg: buck-vproc {
          regulator-name = "vproc";
          regulator-min-microvolt = <518750>;
          regulator-max-microvolt = <1312500>;
          regulator-ramp-delay = <6250>;
          regulator-enable-ramp-delay = <220>;
          regulator-always-on;
        };
        mt6357_vcore_reg: buck-vcore {
          regulator-name = "vcore";
          regulator-min-microvolt = <518750>;
          regulator-max-microvolt = <1312500>;
          regulator-ramp-delay = <6250>;
          regulator-enable-ramp-delay = <220>;
          regulator-always-on;
        };
        mt6357_vmodem_reg: buck-vmodem {
          regulator-name = "vmodem";
          regulator-min-microvolt = <500000>;
          regulator-max-microvolt = <1193750>;
          regulator-ramp-delay = <6250>;
          regulator-enable-ramp-delay = <220>;
        };
        mt6357_vs1_reg: buck-vs1 {
          regulator-name = "vs1";
          regulator-min-microvolt = <1200000>;
          regulator-max-microvolt = <2200000>;
          regulator-ramp-delay = <12500>;
          regulator-enable-ramp-delay = <220>;
          regulator-always-on;
        };
        mt6357_vpa_reg: buck-vpa {
          regulator-name = "vpa";
          regulator-min-microvolt = <500000>;
          regulator-max-microvolt = <3650000>;
          regulator-ramp-delay = <50000>;
          regulator-enable-ramp-delay = <220>;
        };
        mt6357_vfe28_reg: ldo-vfe28 {
          regulator-name = "vfe28";
          regulator-min-microvolt = <2800000>;
          regulator-max-microvolt = <2800000>;
          regulator-enable-ramp-delay = <264>;
        };
        mt6357_vxo22_reg: ldo-vxo22 {
          regulator-name = "vxo22";
          regulator-min-microvolt = <2200000>;
          regulator-max-microvolt = <2400000>;
          regulator-enable-ramp-delay = <110>;
        };
        mt6357_vrf18_reg: ldo-vrf18 {
          regulator-name = "vrf18";
          regulator-min-microvolt = <1800000>;
          regulator-max-microvolt = <1800000>;
          regulator-enable-ramp-delay = <110>;
        };
        mt6357_vrf12_reg: ldo-vrf12 {
          regulator-name = "vrf12";
          regulator-min-microvolt = <1200000>;
          regulator-max-microvolt = <1200000>;
          regulator-enable-ramp-delay = <110>;
        };
        mt6357_vefuse_reg: ldo-vefuse {
          regulator-name = "vefuse";
          regulator-min-microvolt = <1200000>;
          regulator-max-microvolt = <3300000>;
          regulator-enable-ramp-delay = <264>;
        };
        mt6357_vcn33_bt_reg: ldo-vcn33-bt {
          regulator-name = "vcn33-bt";
          regulator-min-microvolt = <3300000>;
          regulator-max-microvolt = <3500000>;
          regulator-enable-ramp-delay = <264>;
        };
        mt6357_vcn33_wifi_reg: ldo-vcn33-wifi {
          regulator-name = "vcn33-wifi";
          regulator-min-microvolt = <3300000>;
          regulator-max-microvolt = <3500000>;
          regulator-enable-ramp-delay = <264>;
        };
        mt6357_vcn28_reg: ldo-vcn28 {
          regulator-name = "vcn28";
          regulator-min-microvolt = <2800000>;
          regulator-max-microvolt = <2800000>;
          regulator-enable-ramp-delay = <264>;
        };
        mt6357_vcn18_reg: ldo-vcn18 {
          regulator-name = "vcn18";
          regulator-min-microvolt = <1800000>;
          regulator-max-microvolt = <1800000>;
          regulator-enable-ramp-delay = <264>;
        };
        mt6357_vcama_reg: ldo-vcama {
          regulator-name = "vcama";
          regulator-min-microvolt = <2500000>;
          regulator-max-microvolt = <2800000>;
          regulator-enable-ramp-delay = <264>;
        };
        mt6357_vcamd_reg: ldo-vcamd {
          regulator-name = "vcamd";
          regulator-min-microvolt = <1000000>;
          regulator-max-microvolt = <1800000>;
          regulator-enable-ramp-delay = <264>;
        };
        mt6357_vcamio_reg: ldo-vcamio18 {
          regulator-name = "vcamio";
          regulator-min-microvolt = <1800000>;
          regulator-max-microvolt = <1800000>;
          regulator-enable-ramp-delay = <264>;
        };
        mt6357_vldo28_reg: ldo-vldo28 {
          regulator-name = "vldo28";
          regulator-min-microvolt = <2800000>;
          regulator-max-microvolt = <3000000>;
          regulator-enable-ramp-delay = <264>;
        };
        mt6357_vsram_others_reg: ldo-vsram-others {
          regulator-name = "vsram-others";
          regulator-min-microvolt = <518750>;
          regulator-max-microvolt = <1312500>;
          regulator-ramp-delay = <6250>;
          regulator-enable-ramp-delay = <110>;
          regulator-always-on;
        };
        mt6357_vsram_proc_reg: ldo-vsram-proc {
          regulator-name = "vsram-proc";
          regulator-min-microvolt = <518750>;
          regulator-max-microvolt = <1312500>;
          regulator-ramp-delay = <6250>;
          regulator-enable-ramp-delay = <110>;
          regulator-always-on;
        };
        mt6357_vaux18_reg: ldo-vaux18 {
          regulator-name = "vaux18";
          regulator-min-microvolt = <1800000>;
          regulator-max-microvolt = <1800000>;
          regulator-enable-ramp-delay = <264>;
        };
        mt6357_vaud28_reg: ldo-vaud28 {
          regulator-name = "vaud28";
          regulator-min-microvolt = <2800000>;
          regulator-max-microvolt = <2800000>;
          regulator-enable-ramp-delay = <264>;
        };
        mt6357_vio28_reg: ldo-vio28 {
          regulator-name = "vio28";
          regulator-min-microvolt = <2800000>;
          regulator-max-microvolt = <2800000>;
          regulator-enable-ramp-delay = <264>;
        };
        mt6357_vio18_reg: ldo-vio18 {
          regulator-name = "vio18";
          regulator-min-microvolt = <1800000>;
          regulator-max-microvolt = <1800000>;
          regulator-enable-ramp-delay = <264>;
          regulator-always-on;
        };
        mt6357_vdram_reg: ldo-vdram {
          regulator-name = "vdram";
          regulator-min-microvolt = <1100000>;
          regulator-max-microvolt = <1200000>;
          regulator-enable-ramp-delay = <3300>;
        };
        mt6357_vmc_reg: ldo-vmc {
          regulator-name = "vmc";
          regulator-min-microvolt = <1800000>;
          regulator-max-microvolt = <3300000>;
          regulator-enable-ramp-delay = <44>;
        };
        mt6357_vmch_reg: ldo-vmch {
          regulator-name = "vmch";
          regulator-min-microvolt = <2900000>;
          regulator-max-microvolt = <3300000>;
          regulator-enable-ramp-delay = <44>;
        };
        mt6357_vemc_reg: ldo-vemc {
          regulator-name = "vemc";
          regulator-min-microvolt = <2900000>;
          regulator-max-microvolt = <3300000>;
          regulator-enable-ramp-delay = <44>;
          regulator-always-on;
        };
        mt6357_vsim1_reg: ldo-vsim1 {
          regulator-name = "vsim1";
          regulator-min-microvolt = <1700000>;
          regulator-max-microvolt = <3100000>;
          regulator-enable-ramp-delay = <264>;
        };
        mt6357_vsim2_reg: ldo-vsim2 {
          regulator-name = "vsim2";
          regulator-min-microvolt = <1700000>;
          regulator-max-microvolt = <3100000>;
          regulator-enable-ramp-delay = <264>;
        };
        mt6357_vibr_reg: ldo-vibr {
          regulator-name = "vibr";
          regulator-min-microvolt = <1200000>;
          regulator-max-microvolt = <3300000>;
          regulator-enable-ramp-delay = <44>;
        };
        mt6357_vusb33_reg: ldo-vusb33 {
          regulator-name = "vusb33";
          regulator-min-microvolt = <3000000>;
          regulator-max-microvolt = <3100000>;
          regulator-enable-ramp-delay = <264>;
        };
      };
    };
...
