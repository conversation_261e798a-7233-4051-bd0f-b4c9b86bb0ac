# SPDX-License-Identifier: GPL-2.0-only OR BSD-2-Clause
%YAML 1.2
---
$id: http://devicetree.org/schemas/regulator/mt6315-regulator.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: Mediatek MT6315 Regulator

maintainers:
  - <PERSON><PERSON>-<PERSON><PERSON><PERSON> <<EMAIL>>

description: |
  The MT6315 is a power management IC (PMIC) configurable with SPMI.
  that contains 4 BUCKs output which can combine with each other
  by different efuse settings.

properties:
  compatible:
    oneOf:
      - items:
          - const: mediatek,mt6319-regulator
          - const: mediatek,mt6315-regulator
      - const: mediatek,mt6315-regulator

  reg:
    maxItems: 1

  regulators:
    type: object
    description: List of regulators and its properties

    patternProperties:
      "^vbuck[1-4]$":
        type: object
        $ref: regulator.yaml#
        unevaluatedProperties: false

    additionalProperties: false

required:
  - compatible
  - reg
  - regulators

additionalProperties: false

examples:
  - |
    pmic@6 {
      compatible = "mediatek,mt6315-regulator";
      reg = <0x6 0>;

      regulators {
        vbuck1 {
          regulator-min-microvolt = <300000>;
          regulator-max-microvolt = <1193750>;
          regulator-enable-ramp-delay = <256>;
          regulator-allowed-modes = <0 1 2>;
        };

        vbuck3 {
          regulator-min-microvolt = <300000>;
          regulator-max-microvolt = <1193750>;
          regulator-enable-ramp-delay = <256>;
          regulator-allowed-modes = <0 1 2>;
        };
      };
    };
