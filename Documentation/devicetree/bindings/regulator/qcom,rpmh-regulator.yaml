# SPDX-License-Identifier: (GPL-2.0 OR BSD-2-Clause)
%YAML 1.2
---
$id: http://devicetree.org/schemas/regulator/qcom,rpmh-regulator.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: Qualcomm Technologies, Inc. RPMh Regulators

maintainers:
  - <PERSON><PERSON><PERSON> <bjorn.and<PERSON><PERSON>@linaro.org>
  - <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> <<EMAIL>>

description: |
    rpmh-regulator devices support PMIC regulator management via the Voltage
    Regulator Manager (VRM) and Oscillator Buffer (XOB) RPMh accelerators.
    The APPS processor communicates with these hardware blocks via a
    Resource State Coordinator (RSC) using command packets.  The VRM allows
    changing three parameters for a given regulator, enable state, output
    voltage, and operating mode.  The XOB allows changing only a single
    parameter for a given regulator, its enable state.  Despite its name,
    the XOB is capable of controlling the enable state of any PMIC peripheral.
    It is used for clock buffers, low-voltage switches, and LDO/SMPS regulators
    which have a fixed voltage and mode.

    =======================
    Required Node Structure
    =======================

    RPMh regulators must be described in two levels of device nodes.  The first
    level describes the PMIC containing the regulators and must reside within an
    RPMh device node.  The second level describes each regulator within the PMIC
    which is to be used on the board.  Each of these regulators maps to a single
    RPMh resource.

    The names used for regulator nodes must match those supported by a given
    PMIC. Supported regulator node names are
      For PM6150, smps1 - smps5, ldo1 - ldo19
      For PM6150L, smps1 - smps8, ldo1 - ldo11, bob
      For PM6350, smps1 - smps5, ldo1 - ldo22
      For PM660, smps1 - smps6, ldo1 - ldo3, ldo5 - ldo19
      For PM660L, smps1 - smps3, smps5, ldo1 - ldo8, bob
      For PM7325, smps1 - smps8, ldo1 - ldo19
      For PM8005, smps1 - smps4
      For PM8009, smps1 - smps2, ldo1 - ldo7
      For PM8010, ldo1 - ldo7
      For PM8150, smps1 - smps10, ldo1 - ldo18
      For PM8150L, smps1 - smps8, ldo1 - ldo11, bob, flash, rgb
      For PM8350, smps1 - smps12, ldo1 - ldo10
      For PM8350C, smps1 - smps10, ldo1 - ldo13, bob
      For PM8450, smps1 - smps6, ldo1 - ldo4
      For PM8550, smps1 - smps6, ldo1 - ldo17, bob1 - bob2
      For PM8998, smps1 - smps13, ldo1 - ldo28, lvs1 - lvs2
      For PMI8998, bob
      For PMC8380, smps1 - smps8, ldo1 - lodo3
      For PMR735A, smps1 - smps3, ldo1 - ldo7
      For PMX55, smps1 - smps7, ldo1 - ldo16
      For PMX65, smps1 - smps8, ldo1 - ldo21
      For PMX75, smps1 - smps10, ldo1 - ldo21

properties:
  compatible:
    enum:
      - qcom,pm6150-rpmh-regulators
      - qcom,pm6150l-rpmh-regulators
      - qcom,pm6350-rpmh-regulators
      - qcom,pm660-rpmh-regulators
      - qcom,pm660l-rpmh-regulators
      - qcom,pm7325-rpmh-regulators
      - qcom,pm8005-rpmh-regulators
      - qcom,pm8009-rpmh-regulators
      - qcom,pm8009-1-rpmh-regulators
      - qcom,pm8010-rpmh-regulators
      - qcom,pm8150-rpmh-regulators
      - qcom,pm8150l-rpmh-regulators
      - qcom,pm8350-rpmh-regulators
      - qcom,pm8350c-rpmh-regulators
      - qcom,pm8450-rpmh-regulators
      - qcom,pm8550-rpmh-regulators
      - qcom,pm8550ve-rpmh-regulators
      - qcom,pm8550vs-rpmh-regulators
      - qcom,pm8998-rpmh-regulators
      - qcom,pmc8180-rpmh-regulators
      - qcom,pmc8180c-rpmh-regulators
      - qcom,pmc8380-rpmh-regulators
      - qcom,pmg1110-rpmh-regulators
      - qcom,pmi8998-rpmh-regulators
      - qcom,pmm8155au-rpmh-regulators
      - qcom,pmm8654au-rpmh-regulators
      - qcom,pmr735a-rpmh-regulators
      - qcom,pmx55-rpmh-regulators
      - qcom,pmx65-rpmh-regulators
      - qcom,pmx75-rpmh-regulators

  qcom,pmic-id:
    description: |
        RPMh resource name suffix used for the regulators found
        on this PMIC.
    $ref: /schemas/types.yaml#/definitions/string
    enum: [a, b, c, d, e, f, g, h, i, j, k, l, m, n]

  qcom,always-wait-for-ack:
    description: |
        Boolean flag which indicates that the application processor
        must wait for an ACK or a NACK from RPMh for every request
        sent for this regulator including those which are for a
        strictly lower power state.
    $ref: /schemas/types.yaml#/definitions/flag

  vdd-flash-supply:
    description: Input supply phandle of flash.

  vdd-rgb-supply:
    description: Input supply phandle of rgb.

  bob:
    type: object
    $ref: regulator.yaml#
    unevaluatedProperties: false
    description: BOB regulator node.
    dependencies:
      regulator-allow-set-load: [ regulator-allowed-modes ]

patternProperties:
  "^(smps|ldo|lvs|bob)[0-9]+$":
    type: object
    $ref: regulator.yaml#
    unevaluatedProperties: false
    description: smps/ldo regulator nodes(s).
    dependencies:
      regulator-allow-set-load: [ regulator-allowed-modes ]

required:
  - compatible
  - qcom,pmic-id

allOf:
  - if:
      properties:
        compatible:
          enum:
            - qcom,pm6150-rpmh-regulators
    then:
      properties:
        vdd-l2-l3-supply: true
        vdd-l4-l7-l8-supply: true
        vdd-l5-l16-l17-l18-l19-supply: true
        vdd-l10-l14-l15-supply: true
        vdd-l11-l12-l13-supply: true
      patternProperties:
        "^vdd-l[169]-supply$": true
        "^vdd-s[1-5]-supply$": true

  - if:
      properties:
        compatible:
          enum:
            - qcom,pm6150l-rpmh-regulators
            - qcom,pm8150l-rpmh-regulators
            - qcom,pmc8180c-rpmh-regulators
    then:
      properties:
        vdd-bob-supply:
          description: BOB regulator parent supply phandle.
        vdd-l1-l8-supply: true
        vdd-l2-l3-supply: true
        vdd-l4-l5-l6-supply: true
        vdd-l7-l11-supply: true
        vdd-l9-l10-supply: true
      patternProperties:
        "^vdd-s[1-8]-supply$": true

  - if:
      properties:
        compatible:
          enum:
            - qcom,pm660-rpmh-regulators
    then:
      properties:
        vdd-l1-l6-l7-supply: true
        vdd-l2-l3-supply: true
        vdd-l5-supply: true
        vdd-l8-l9-l10-l11-l12-l13-l14-supply: true
        vdd-l15-l16-l17-l18-l19-supply: true
      patternProperties:
        "^vdd-s[1-6]-supply$": true

  - if:
      properties:
        compatible:
          enum:
            - qcom,pm660l-rpmh-regulators
    then:
      properties:
        vdd-bob-supply:
          description: BOB regulator parent supply phandle.
        vdd-l1-l9-l10-supply: true
        vdd-l2-supply: true
        vdd-l3-l5-l7-l8-supply: true
        vdd-l4-l6-supply: true
        vdd-s3-s4-supply: true
      patternProperties:
        "^vdd-s[125]-supply$": true

  - if:
      properties:
        compatible:
          enum:
            - qcom,pm7325-rpmh-regulators
    then:
      properties:
        vdd-l1-l4-l12-l15-supply: true
        vdd-l2-l7-supply: true
        vdd-l6-l9-l10-supply: true
        vdd-l11-l17-l18-l19-supply: true
        vdd-l13-supply: true
        vdd-l14-l16-supply: true
      patternProperties:
        "^vdd-l[358]-supply$": true
        "^vdd-s[1-8]-supply$": true

  - if:
      properties:
        compatible:
          enum:
            - qcom,pm8005-rpmh-regulators
    then:
      patternProperties:
        "^vdd-s[1-4]-supply$": true

  - if:
      properties:
        compatible:
          enum:
            - qcom,pm8009-rpmh-regulators
            - qcom,pm8009-1-rpmh-regulators
    then:
      properties:
        vdd-l5-l6-supply: true
      patternProperties:
        "^vdd-l[1-47]-supply$": true
        "^vdd-s[1-2]-supply$": true

  - if:
      properties:
        compatible:
          enum:
            - qcom,pm8010-rpmh-regulators
    then:
      properties:
        vdd-l1-l2-supply: true
        vdd-l3-l4-supply: true
      patternProperties:
        "^vdd-l[5-7]-supply$": true

  - if:
      properties:
        compatible:
          enum:
            - qcom,pm8150-rpmh-regulators
            - qcom,pmc8180-rpmh-regulators
            - qcom,pmm8155au-rpmh-regulators
    then:
      properties:
        vdd-l1-l8-l11-supply: true
        vdd-l2-l10-supply: true
        vdd-l3-l4-l5-l18-supply: true
        vdd-l6-l9-supply: true
        vdd-l7-l12-l14-l15-supply: true
        vdd-l13-l16-l17-supply: true
      patternProperties:
        "^vdd-s([1-9]|10)-supply$": true

  - if:
      properties:
        compatible:
          enum:
            - qcom,pmm8654au-rpmh-regulators
    then:
      properties:
        vdd-l1-supply: true
        vdd-l2-l3-supply: true
        vdd-l4-supply: true
        vdd-l5-supply: true
        vdd-l6-l7-supply: true
        vdd-l8-l9-supply: true
      patternProperties:
        "^vdd-s[1-9]-supply$": true

  - if:
      properties:
        compatible:
          enum:
            - qcom,pm8350-rpmh-regulators
    then:
      properties:
        vdd-l1-l4-supply: true
        vdd-l2-l7-supply: true
        vdd-l3-l5-supply: true
        vdd-l6-l9-l10-supply: true
        vdd-l8-supply: true
      patternProperties:
        "^vdd-s([1-9]|1[0-2])-supply$": true

  - if:
      properties:
        compatible:
          enum:
            - qcom,pm8350c-rpmh-regulators
    then:
      properties:
        vdd-bob-supply:
          description: BOB regulator parent supply phandle.
        vdd-l1-l12-supply: true
        vdd-l2-l8-supply: true
        vdd-l3-l4-l5-l7-l13-supply: true
        vdd-l6-l9-l11-supply: true
        vdd-l10-supply: true
      patternProperties:
        "^vdd-s([1-9]|10)-supply$": true

  - if:
      properties:
        compatible:
          enum:
            - qcom,pm8450-rpmh-regulators
    then:
      patternProperties:
        "^vdd-l[1-4]-supply$": true
        "^vdd-s[1-6]-supply$": true

  - if:
      properties:
        compatible:
          enum:
            - qcom,pm8550-rpmh-regulators
    then:
      properties:
        vdd-l1-l4-l10-supply: true
        vdd-l2-l13-l14-supply: true
        vdd-l5-l16-supply: true
        vdd-l6-l7-supply: true
        vdd-l8-l9-supply: true
      patternProperties:
        "^vdd-l(3|1[1-7])-supply$": true
        "^vdd-s[1-6]-supply$": true
        "^vdd-bob[1-2]-supply$": true

  - if:
      properties:
        compatible:
          enum:
            - qcom,pm8550vs-rpmh-regulators
    then:
      patternProperties:
        "^vdd-l[1-3]-supply$": true
        "^vdd-s[1-6]-supply$": true

  - if:
      properties:
        compatible:
          enum:
            - qcom,pm8998-rpmh-regulators
    then:
      properties:
        vdd-l1-l27-supply: true
        vdd-l2-l8-l17-supply: true
        vdd-l3-l11-supply: true
        vdd-l4-l5-supply: true
        vdd-l6-supply: true
        vdd-l7-l12-l14-l15-supply: true
        vdd-l9-supply: true
        vdd-l10-l23-l25-supply: true
        vdd-l13-l19-l21-supply: true
        vdd-l16-l28-supply: true
        vdd-l18-l22-supply: true
        vdd-l20-l24-supply: true
        vdd-l26-supply: true
        vin-lvs-1-2-supply: true
      patternProperties:
        "^vdd-s([1-9]|1[0-3])-supply$": true

  - if:
      properties:
        compatible:
          enum:
            - qcom,pmc8380-rpmh-regulators
            - qcom,pm8550ve-rpmh-regulators
    then:
      patternProperties:
        "^vdd-l[1-3]-supply$": true
        "^vdd-s[1-8]-supply$": true

  - if:
      properties:
        compatible:
          enum:
            - qcom,pmg1110-rpmh-regulators
    then:
      properties:
        vdd-s1-supply: true

  - if:
      properties:
        compatible:
          enum:
            - qcom,pmi8998-rpmh-regulators
    then:
      properties:
        vdd-bob-supply:
          description: BOB regulator parent supply phandle.

  - if:
      properties:
        compatible:
          enum:
            - qcom,pmr735a-rpmh-regulators
    then:
      properties:
        vdd-l1-l2-supply: true
        vdd-l3-supply: true
        vdd-l4-supply: true
        vdd-l5-l6-supply: true
        vdd-l7-bob-supply: true
      patternProperties:
        "^vdd-s[1-3]-supply$": true

  - if:
      properties:
        compatible:
          enum:
            - qcom,pmx55-rpmh-regulators
    then:
      properties:
        vdd-l1-l2-supply: true
        vdd-l3-l9-supply: true
        vdd-l4-l12-supply: true
        vdd-l5-l6-supply: true
        vdd-l7-l8-supply: true
        vdd-l10-l11-l13-supply: true
      patternProperties:
        "^vdd-l1[4-6]-supply$": true
        "^vdd-s[1-7]-supply$": true

  - if:
      properties:
        compatible:
          enum:
            - qcom,pmx65-rpmh-regulators
    then:
      properties:
        vdd-l2-l18-supply: true
        vdd-l5-l6-l16-supply: true
        vdd-l8-l9-supply: true
        vdd-l11-l13-supply: true
      patternProperties:
        "^vdd-l[1347]-supply$": true
        "^vdd-l1[024579]-supply$": true
        "^vdd-l2[01]-supply$": true
        "^vdd-s[1-8]-supply$": true

  - if:
      properties:
        compatible:
          enum:
            - qcom,pmx75-rpmh-regulators
    then:
      properties:
        vdd-l2-l18-supply: true
        vdd-l4-l16-supply: true
        vdd-l5-l6-supply: true
        vdd-l8-l9-supply: true
        vdd-l11-l13-supply: true
        vdd-l20-l21-supply: true
      patternProperties:
        "^vdd-l[137]-supply$": true
        "^vdd-l1[024579]-supply$": true
        "^vdd-s([1-9]|10)-supply$": true

unevaluatedProperties: false

examples:
  - |
    #include <dt-bindings/regulator/qcom,rpmh-regulator.h>

    pm8998-rpmh-regulators {
        compatible = "qcom,pm8998-rpmh-regulators";
        qcom,pmic-id = "a";

        vdd-l7-l12-l14-l15-supply = <&pm8998_s5>;

        smps2 {
            regulator-min-microvolt = <1100000>;
            regulator-max-microvolt = <1100000>;
        };

        ldo7 {
            regulator-min-microvolt = <1800000>;
            regulator-max-microvolt = <1800000>;
            regulator-initial-mode = <RPMH_REGULATOR_MODE_HPM>;
            regulator-allowed-modes = <RPMH_REGULATOR_MODE_LPM
                                       RPMH_REGULATOR_MODE_HPM>;
            regulator-allow-set-load;
        };

        lvs1 {
            regulator-min-microvolt = <1800000>;
            regulator-max-microvolt = <1800000>;
        };
    };

    pmi8998-rpmh-regulators {
        compatible = "qcom,pmi8998-rpmh-regulators";
        qcom,pmic-id = "b";

        bob {
            regulator-min-microvolt = <3312000>;
            regulator-max-microvolt = <3600000>;
            regulator-allowed-modes = <RPMH_REGULATOR_MODE_AUTO
                                       RPMH_REGULATOR_MODE_HPM>;
            regulator-initial-mode = <RPMH_REGULATOR_MODE_AUTO>;
        };
    };
...
