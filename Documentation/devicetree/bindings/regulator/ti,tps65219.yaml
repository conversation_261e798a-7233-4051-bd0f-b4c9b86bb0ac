# SPDX-License-Identifier: GPL-2.0-only OR BSD-2-Clause
%YAML 1.2
---
$id: http://devicetree.org/schemas/regulator/ti,tps65219.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: TI TPS65214/TPS65215/TPS65219 Power Management Integrated Circuit

maintainers:
  - <PERSON> <<EMAIL>>

description: |
  Regulator nodes should be named to buck<number> and ldo<number>.

  TI TPS65219 is a Power Management IC with 3 Buck regulators, 4 Low
  Drop-out Regulators (LDOs), 1 GPIO, 2 GPOs, and power-button.

  TI TPS65215 is a derivative of TPS65219 with 3 Buck regulators, 2 Low
  Drop-out Regulators (LDOs), 1 GPIO, 1 GPO, and power-button.

  TI TPS65214 is a derivative of TPS65219 with 3 Buck regulators, 2 Low
  Drop-out Regulators (LDOs), 1 GPIO, 1 GPO, and power-button.

properties:
  compatible:
    enum:
      - ti,tps65214
      - ti,tps65215
      - ti,tps65219

  reg:
    maxItems: 1

  system-power-controller:
    type: boolean
    description: Optional property that indicates that this device is
      controlling system power.

  interrupts:
    description: Short-circuit, over-current, under-voltage for regulators, PB interrupts.
    maxItems: 1

  interrupt-controller: true

  '#interrupt-cells':
    description: Specifies the PIN numbers and Flags, as defined in
      include/dt-bindings/interrupt-controller/irq.h
    const: 1

  ti,power-button:
    type: boolean
    description: |
      Optional property that sets the EN/PB/VSENSE pin to be a
      power-button.
      TPS65219 has a multipurpose pin called EN/PB/VSENSE that can be either
      1. EN in which case it functions as an enable pin.
      2. VSENSE which compares the voltages and triggers an automatic
      on/off request.
      3. PB in which case it can be configured to trigger an interrupt
      to the SoC.
      ti,power-button reflects the last one of those options
      where the board has a button wired to the pin and triggers
      an interrupt on pressing it.

  regulators:
    type: object
    description: |
      list of regulators provided by this controller

    patternProperties:
      "^ldo[1-4]$":
        type: object
        $ref: regulator.yaml#
        description:
          Properties for single LDO regulator.

        unevaluatedProperties: false

      "^buck[1-3]$":
        type: object
        $ref: regulator.yaml#
        description:
          Properties for single BUCK regulator.

        unevaluatedProperties: false

    additionalProperties: false

patternProperties:
  "^buck[1-3]-supply$":
    description: Input supply phandle of one regulator.

  "^ldo[1-4]-supply$":
    description: Input supply phandle of one regulator.

required:
  - compatible
  - reg
  - interrupts
  - regulators

additionalProperties: false

allOf:
  - if:
      properties:
        compatible:
          contains:
            enum:
              - ti,tps65214
              - ti,tps65215
    then:
      properties:
        regulators:
          patternProperties:
            "^ldo[3-4]$": false

examples:
  - |
    #include <dt-bindings/interrupt-controller/arm-gic.h>
    i2c {
        #address-cells = <1>;
        #size-cells = <0>;

        tps65219: pmic@30 {
            compatible = "ti,tps65219";
            reg = <0x30>;
            buck1-supply = <&vcc_3v3_sys>;
            buck2-supply = <&vcc_3v3_sys>;
            buck3-supply = <&vcc_3v3_sys>;
            ldo1-supply = <&vcc_3v3_sys>;
            ldo2-supply = <&buck2_reg>;
            ldo3-supply = <&vcc_3v3_sys>;
            ldo4-supply = <&vcc_3v3_sys>;

            pinctrl-0 = <&pmic_irq_pins_default>;

            interrupt-parent = <&gic500>;
            interrupts = <GIC_SPI 224 IRQ_TYPE_LEVEL_HIGH>;
            ti,power-button;

            regulators {
                buck1_reg: buck1 {
                    regulator-name = "VDD_CORE";
                    regulator-min-microvolt = <750000>;
                    regulator-max-microvolt = <750000>;
                    regulator-boot-on;
                    regulator-always-on;
                };

                buck2_reg: buck2 {
                    regulator-name = "VCC1V8";
                    regulator-min-microvolt = <1800000>;
                    regulator-max-microvolt = <1800000>;
                    regulator-boot-on;
                    regulator-always-on;
                };

                buck3_reg: buck3 {
                    regulator-name = "VDD_LPDDR4";
                    regulator-min-microvolt = <1100000>;
                    regulator-max-microvolt = <1100000>;
                    regulator-boot-on;
                    regulator-always-on;
                };

                ldo1_reg: ldo1 {
                    regulator-name = "VDDSHV_SD_IO_PMIC";
                    regulator-min-microvolt = <33000000>;
                    regulator-max-microvolt = <33000000>;
                };

                ldo2_reg: ldo2 {
                    regulator-name = "VDDAR_CORE";
                    regulator-min-microvolt = <850000>;
                    regulator-max-microvolt = <850000>;
                    regulator-boot-on;
                    regulator-always-on;
                };

                ldo3_reg: ldo3 {
                    regulator-name = "VDDA_1V8";
                    regulator-min-microvolt = <18000000>;
                    regulator-max-microvolt = <18000000>;
                    regulator-boot-on;
                    regulator-always-on;
                };

                ldo4_reg: ldo4 {
                    regulator-name = "VDD_PHY_2V5";
                    regulator-min-microvolt = <25000000>;
                    regulator-max-microvolt = <25000000>;
                    regulator-boot-on;
                    regulator-always-on;
                };
            };
        };
    };
