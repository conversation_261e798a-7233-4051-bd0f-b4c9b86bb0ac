# SPDX-License-Identifier: (GPL-2.0-only OR BSD-2-Clause)
%YAML 1.2
---
$id: http://devicetree.org/schemas/regulator/brcm,bcm59056.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: Broadcom BCM59056 Power Management Unit regulators

description: |
  This is a part of device tree bindings for the BCM59056 power
  management unit.

  See Documentation/devicetree/bindings/mfd/brcm,bcm59056.yaml for
  additional information and example.

maintainers:
  - <PERSON><PERSON> <<EMAIL>>

patternProperties:
  "^(cam|sim|mmc)ldo[1-2]$":
    type: object
    $ref: /schemas/regulator/regulator.yaml#
    unevaluatedProperties: false

  "^(rf|sd|sdx|aud|mic|usb|vib)ldo$":
    type: object
    $ref: /schemas/regulator/regulator.yaml#
    unevaluatedProperties: false

  "^(c|m|v)sr$":
    type: object
    $ref: /schemas/regulator/regulator.yaml#
    unevaluatedProperties: false

  "^(io|sd)sr[1-2]$":
    type: object
    $ref: /schemas/regulator/regulator.yaml#
    unevaluatedProperties: false

  "^gpldo[1-6]$":
    type: object
    $ref: /schemas/regulator/regulator.yaml#
    unevaluatedProperties: false

properties:
  vbus:
    type: object
    $ref: /schemas/regulator/regulator.yaml#
    unevaluatedProperties: false

additionalProperties: false
