# SPDX-License-Identifier: (GPL-2.0-only OR BSD-2-Clause)
%YAML 1.2
---
$id: http://devicetree.org/schemas/reset/atmel,at91sam9260-reset.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: Atmel/Microchip System Reset Controller

maintainers:
  - <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>

description: |
  The system reset controller can be used to reset the CPU. In case of
  SAMA7G5 it can also reset some devices (e.g. USB PHYs).

properties:
  compatible:
    oneOf:
      - items:
          - enum:
              - atmel,at91sam9260-rstc
              - atmel,at91sam9g45-rstc
              - atmel,sama5d3-rstc
              - microchip,sam9x60-rstc
              - microchip,sama7g5-rstc
      - items:
          - const: microchip,sama7d65-rstc
          - const: microchip,sama7g5-rstc
      - items:
          - const: atmel,sama5d3-rstc
          - const: atmel,at91sam9g45-rstc
      - items:
          - enum:
              - microchip,sam9x7-rstc
          - const: microchip,sam9x60-rstc

  reg:
    minItems: 1
    items:
      - description: base registers for system reset control
      - description: registers for device specific reset control

  clocks:
    maxItems: 1

  "#reset-cells":
    const: 1

required:
  - compatible
  - reg
  - clocks

allOf:
  - if:
      properties:
        compatible:
          contains:
            enum:
              - microchip,sama7g5-rstc
    then:
      required:
        - "#reset-cells"

additionalProperties: false

examples:
  - |
    #include <dt-bindings/clock/at91.h>

    reset-controller@fffffd00 {
        compatible = "atmel,at91sam9260-rstc";
        reg = <0xfffffd00 0x10>;
        clocks = <&pmc PMC_TYPE_CORE PMC_SLOW>;
    };
