# SPDX-License-Identifier: (GPL-2.0 OR BSD-2-Clause)
# Copyright 2019 Bay<PERSON>ibre, SAS
%YAML 1.2
---
$id: http://devicetree.org/schemas/reset/amlogic,meson-reset.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: Amlogic Meson SoC Reset Controller

maintainers:
  - <PERSON> <<EMAIL>>

properties:
  compatible:
    oneOf:
      - enum:
          - amlogic,meson8b-reset # Reset Controller on Meson8b and compatible SoCs
          - amlogic,meson-gxbb-reset # Reset Controller on GXBB and compatible SoCs
          - amlogic,meson-axg-reset # Reset Controller on AXG and compatible SoCs
          - amlogic,meson-a1-reset # Reset Controller on A1 and compatible SoCs
          - amlogic,meson-s4-reset # Reset Controller on S4 and compatible SoCs
          - amlogic,c3-reset # Reset Controller on C3 and compatible SoCs
          - amlogic,t7-reset
      - items:
          - enum:
              - amlogic,a4-reset
              - amlogic,a5-reset
          - const: amlogic,meson-s4-reset

  reg:
    maxItems: 1

  "#reset-cells":
    const: 1

required:
  - compatible
  - reg
  - "#reset-cells"

additionalProperties: false

examples:
  - |
    reset-controller@c884404 {
          compatible = "amlogic,meson-gxbb-reset";
          reg = <0xc884404 0x20>;
          #reset-cells = <1>;
    };
