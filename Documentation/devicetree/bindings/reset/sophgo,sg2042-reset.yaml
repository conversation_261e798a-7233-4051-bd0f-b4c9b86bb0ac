# SPDX-License-Identifier: (GPL-2.0 OR BSD-2-Clause)
%YAML 1.2
---
$id: http://devicetree.org/schemas/reset/sophgo,sg2042-reset.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: Sophgo SG2042 SoC Reset Controller

maintainers:
  - <PERSON> <<EMAIL>>

properties:
  compatible:
    oneOf:
      - items:
          - enum:
              - sophgo,sg2044-reset
          - const: sophgo,sg2042-reset
      - const: sophgo,sg2042-reset

  reg:
    maxItems: 1

  "#reset-cells":
    const: 1

required:
  - compatible
  - reg
  - "#reset-cells"

additionalProperties: false

examples:
  - |
    rstgen: reset-controller@c00 {
        compatible = "sophgo,sg2042-reset";
        reg = <0xc00 0xc>;
        #reset-cells = <1>;
    };
