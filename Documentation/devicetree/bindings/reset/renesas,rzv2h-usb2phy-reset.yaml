# SPDX-License-Identifier: (GPL-2.0-only OR BSD-2-Clause)
%YAML 1.2
---
$id: http://devicetree.org/schemas/reset/renesas,rzv2h-usb2phy-reset.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: Renesas RZ/V2H(P) USB2PHY Port reset Control

maintainers:
  - Lad <PERSON> <<EMAIL>>

description:
  The RZ/V2H(P) USB2PHY Control mainly controls Port reset and power down of the
  USB2.0 PHY.

properties:
  compatible:
    const: renesas,r9a09g057-usb2phy-reset     # RZ/V2H(P)

  reg:
    maxItems: 1

  clocks:
    maxItems: 1

  resets:
    maxItems: 1

  power-domains:
    maxItems: 1

  '#reset-cells':
    const: 0

required:
  - compatible
  - reg
  - clocks
  - resets
  - power-domains
  - '#reset-cells'

additionalProperties: false

examples:
  - |
    #include <dt-bindings/clock/renesas,r9a09g057-cpg.h>

    reset-controller@15830000 {
        compatible = "renesas,r9a09g057-usb2phy-reset";
        reg = <0x15830000 0x10000>;
        clocks = <&cpg CPG_MOD 0xb6>;
        resets = <&cpg 0xaf>;
        power-domains = <&cpg>;
        #reset-cells = <0>;
    };
