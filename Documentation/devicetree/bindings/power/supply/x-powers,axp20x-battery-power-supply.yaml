# SPDX-License-Identifier: GPL-2.0
%YAML 1.2
---
$id: http://devicetree.org/schemas/power/supply/x-powers,axp20x-battery-power-supply.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: AXP20x Battery power-supply

description: |
  The supported devices can read the battery voltage, charge and discharge
  currents of the battery by reading ADC channels from the ADC.

maintainers:
  - <PERSON><PERSON><PERSON> <<EMAIL>>
  - <PERSON> <<EMAIL>>

properties:
  compatible:
    oneOf:
      - const: x-powers,axp202-battery-power-supply
      - const: x-powers,axp209-battery-power-supply
      - const: x-powers,axp221-battery-power-supply
      - const: x-powers,axp717-battery-power-supply
      - items:
          - const: x-powers,axp803-battery-power-supply
          - const: x-powers,axp813-battery-power-supply
      - const: x-powers,axp813-battery-power-supply

  monitored-battery:
    description:
      Specifies the phandle of an optional simple-battery connected to
      this gauge.
    $ref: /schemas/types.yaml#/definitions/phandle

  x-powers,no-thermistor:
    type: boolean
    description: Indicates that no thermistor is connected to the TS pin

required:
  - compatible

allOf:
  - $ref: power-supply.yaml#
  - if:
      not:
        properties:
          compatible:
            contains:
              enum:
                - x-powers,axp717-battery-power-supply
    then:
      properties:
        x-powers,no-thermistor: false

additionalProperties: false
