# SPDX-License-Identifier: GPL-2.0 OR BSD-2-Clause
%YAML 1.2
---
$id: http://devicetree.org/schemas/power/supply/qcom,pmi8998-charger.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: Qualcomm PMI8998/PM660 Switch-Mode Battery Charger "2"

maintainers:
  - <PERSON> <<EMAIL>>

properties:
  compatible:
    enum:
      - qcom,pmi8998-charger
      - qcom,pm660-charger

  reg:
    maxItems: 1

  interrupts:
    maxItems: 4

  interrupt-names:
    items:
      - const: usb-plugin
      - const: bat-ov
      - const: wdog-bark
      - const: usbin-icl-change

  io-channels:
    items:
      - description: USB in current in uA
      - description: USB in voltage in uV

  io-channel-names:
    items:
      - const: usbin_i
      - const: usbin_v

  monitored-battery:
    description: phandle to the simple-battery node
    $ref: /schemas/types.yaml#/definitions/phandle

required:
  - compatible
  - reg
  - interrupts
  - interrupt-names
  - io-channels
  - io-channel-names
  - monitored-battery

additionalProperties: false

examples:
  - |
    #include <dt-bindings/interrupt-controller/irq.h>

    pmic {
      #address-cells = <1>;
      #size-cells = <0>;

      charger@1000 {
        compatible = "qcom,pmi8998-charger";
        reg = <0x1000>;

        interrupts = <0x2 0x12 0x2 IRQ_TYPE_EDGE_BOTH>,
                     <0x2 0x13 0x4 IRQ_TYPE_EDGE_BOTH>,
                     <0x2 0x13 0x6 IRQ_TYPE_EDGE_RISING>,
                     <0x2 0x16 0x1 IRQ_TYPE_EDGE_RISING>;
        interrupt-names = "usb-plugin", "bat-ov", "wdog-bark", "usbin-icl-change";

        io-channels = <&pmi8998_rradc 3>,
                      <&pmi8998_rradc 4>;
        io-channel-names = "usbin_i",
                           "usbin_v";

        monitored-battery = <&battery>;
      };
    };
