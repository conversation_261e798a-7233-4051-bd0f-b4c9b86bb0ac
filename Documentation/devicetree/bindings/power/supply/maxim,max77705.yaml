# SPDX-License-Identifier: GPL-2.0-only OR BSD-2-Clause
%YAML 1.2
---
$id: http://devicetree.org/schemas/power/supply/maxim,max77705.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: Maxim MAX777705 charger

maintainers:
  - <PERSON><PERSON><PERSON><PERSON> <dsan<PERSON><PERSON><PERSON>@gmail.com>

description: |
  This is a device tree bindings for charger found in Maxim MAX77705 chip.

allOf:
  - $ref: power-supply.yaml#

properties:
  compatible:
    const: maxim,max77705-charger

  interrupts:
    maxItems: 1

  reg:
    maxItems: 1

required:
  - compatible
  - reg
  - monitored-battery

unevaluatedProperties: false

examples:
  - |
    #include <dt-bindings/interrupt-controller/irq.h>

    i2c {
        #address-cells = <1>;
        #size-cells = <0>;

        charger@69 {
            compatible = "maxim,max77705-charger";
            reg = <0x69>;
            monitored-battery = <&battery>;
            interrupt-parent = <&pm8998_gpios>;
            interrupts = <11 IRQ_TYPE_LEVEL_LOW>;
        };
    };
