# SPDX-License-Identifier: (GPL-2.0 OR BSD-2-Clause)
# Copyright (C) 2020 Topic Embedded Products
%YAML 1.2
---
$id: http://devicetree.org/schemas/power/supply/ltc4162-l.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: Linear Technology (Analog Devices) LTC4162-L Charger

maintainers:
  - <PERSON> <<EMAIL>>

description: |
  The LTC ® 4162-L is an advanced monolithic synchronous step-down switching
  battery charger and PowerPath (TM) manager that seamlessly manages power
  distribution between input sources such as wall adapters, backplanes, solar
  panels, etc., and a rechargeable Lithium-Ion/Polymer battery.

  Specifications about the charger can be found at:
    https://www.analog.com/en/products/ltc4162-l.html
    https://www.analog.com/en/products/ltc4162-f.html
    https://www.analog.com/en/products/ltc4162-s.html
    https://www.analog.com/en/products/ltc4015.html

properties:
  compatible:
    enum:
      - lltc,ltc4015
      - lltc,ltc4162-f
      - lltc,ltc4162-l
      - lltc,ltc4162-s

  reg:
    maxItems: 1
    description: I2C address of the charger.

  lltc,rsnsb-micro-ohms:
    description: Battery sense resistor in microohm.
    minimum: 1000

  lltc,rsnsi-micro-ohms:
    description: Input current sense resistor in microohm.
    minimum: 1000

  lltc,cell-count:
    $ref: /schemas/types.yaml#/definitions/uint32
    description: |
      Number of battery cells. If not provided, will be obtained from the chip
      once the external power is applied. Omit this when the number of cells
      is somewhat dynamic. Without it, several measurements will return 0 until
      the charger is connected to an external supply.

required:
  - compatible
  - reg
  - lltc,rsnsb-micro-ohms
  - lltc,rsnsi-micro-ohms

additionalProperties: false

examples:
  - |
    i2c {
        #address-cells = <1>;
        #size-cells = <0>;
        charger: battery-charger@68 {
            compatible = "lltc,ltc4162-l";
            reg = <0x68>;
            lltc,rsnsb-micro-ohms = <10000>;
            lltc,rsnsi-micro-ohms = <16000>;
            lltc,cell-count = <2>;
        };
    };
