# SPDX-License-Identifier: GPL-2.0
%YAML 1.2
---
$id: http://devicetree.org/schemas/power/supply/sc2731-charger.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: Spreadtrum SC2731 PMICs battery charger

maintainers:
  - <PERSON> <<EMAIL>>

allOf:
  - $ref: power-supply.yaml#

properties:
  compatible:
    const: sprd,sc2731-charger

  reg:
    maxItems: 1

  phys:
    maxItems: 1
    description: phandle to the USB phy

  monitored-battery:
    description: |
      The charger uses the following battery properties
      - charge-term-current-microamp: current for charge termination phase.
      - constant-charge-voltage-max-microvolt: maximum constant input voltage.

additionalProperties: false
...
