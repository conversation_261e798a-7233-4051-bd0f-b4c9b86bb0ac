# SPDX-License-Identifier: (GPL-2.0-only OR BSD-2-Clause)
# Copyright (C) 2020 Texas Instruments Incorporated
%YAML 1.2
---
$id: http://devicetree.org/schemas/power/supply/bq25980.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: TI BQ25980 Flash Charger

maintainers:
  - <PERSON> <<EMAIL>>

description: |
  The BQ25980, BQ25975, and BQ25960 are a series of flash chargers intended
  for use in high-power density portable electronics. These inductorless
  switching chargers can provide over 97% efficiency by making use of the
  switched capacitor architecture.

allOf:
  - $ref: power-supply.yaml#

properties:
  compatible:
    enum:
      - ti,bq25980
      - ti,bq25975
      - ti,bq25960

  reg:
    maxItems: 1

  ti,watchdog-timeout-ms:
    description: |
      Watchdog timer in milli seconds. 0 disables the watchdog.
    default: 0
    minimum: 0
    maximum: 300000
    enum: [ 0, 5000, 10000, 50000, 300000]

  ti,sc-ovp-limit-microvolt:
    description: |
      Minimum input voltage limit in micro volts with a when the charger is in
      switch cap mode. 100000 micro volt step.
    default: 17800000
    minimum: 14000000
    maximum: 22000000

  ti,sc-ocp-limit-microamp:
    description: |
      Maximum input current limit in micro amps with a 100000 micro amp step.
    minimum: 100000
    maximum: 3300000

  ti,bypass-ovp-limit-microvolt:
    description: |
      Minimum input voltage limit in micro volts with a when the charger is in
      switch cap mode. 50000 micro volt step.
    minimum: 7000000
    maximum: 12750000

  ti,bypass-ocp-limit-microamp:
    description: |
      Maximum input current limit in micro amps with a 100000 micro amp step.
    minimum: 100000
    maximum: 3300000

  ti,bypass-enable:
    type: boolean
    description: Enables bypass mode at boot time

  interrupts:
    maxItems: 1
    description: |
      Indicates that the device state has changed.

  monitored-battery:
    $ref: /schemas/types.yaml#/definitions/phandle
    description: phandle to the battery node being monitored

required:
  - compatible
  - reg
  - monitored-battery

unevaluatedProperties: false

examples:
  - |
    bat: battery {
        compatible = "simple-battery";
        constant-charge-current-max-microamp = <4000000>;
        constant-charge-voltage-max-microvolt = <8400000>;
        precharge-current-microamp = <160000>;
        charge-term-current-microamp = <160000>;
    };
    #include <dt-bindings/gpio/gpio.h>
    #include <dt-bindings/interrupt-controller/irq.h>
    i2c {
        #address-cells = <1>;
        #size-cells = <0>;

        bq25980: charger@65 {
            compatible = "ti,bq25980";
            reg = <0x65>;
            interrupt-parent = <&gpio1>;
            interrupts = <16 IRQ_TYPE_EDGE_FALLING>;
            ti,watchdog-timeout-ms = <0>;
            ti,sc-ocp-limit-microamp = <2000000>;
            ti,sc-ovp-limit-microvolt = <17800000>;
            monitored-battery = <&bat>;
        };
    };

...
