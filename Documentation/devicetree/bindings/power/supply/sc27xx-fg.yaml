# SPDX-License-Identifier: GPL-2.0
%YAML 1.2
---
$id: http://devicetree.org/schemas/power/supply/sc27xx-fg.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: Spreadtrum SC27XX PMICs Fuel Gauge Unit Power Supply

maintainers:
  - <PERSON> <<EMAIL>>

allOf:
  - $ref: power-supply.yaml#

properties:
  compatible:
    enum:
      - sprd,sc2720-fgu
      - sprd,sc2721-fgu
      - sprd,sc2723-fgu
      - sprd,sc2730-fgu
      - sprd,sc2731-fgu

  reg:
    maxItems: 1

  battery-detect-gpios:
    maxItems: 1

  bat-detect-gpio:
    maxItems: 1
    deprecated: true
    description: use battery-detect-gpios instead

  interrupts:
    maxItems: 1

  io-channels:
    items:
      - description: Battery Temperature ADC
      - description: Battery Charge Voltage ADC

  io-channel-names:
    items:
      - const: bat-temp
      - const: charge-vol

  nvmem-cells:
    maxItems: 1
    description: Calibration cells provided by eFuse device

  nvmem-cell-names:
    const: fgu_calib

  sprd,calib-resistance-micro-ohms:
    description: real resistance of coulomb counter chip in micro Ohms

  monitored-battery: true

required:
  - compatible
  - reg
  - battery-detect-gpios
  - interrupts
  - io-channels
  - io-channel-names
  - nvmem-cells
  - nvmem-cell-names
  - sprd,calib-resistance-micro-ohms
  - monitored-battery

additionalProperties: false
...
