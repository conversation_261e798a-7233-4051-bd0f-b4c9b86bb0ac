# SPDX-License-Identifier: (GPL-2.0-only OR BSD-2-Clause)
%YAML 1.2
---
$id: http://devicetree.org/schemas/power/supply/st,stc3117.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: STMicroelectronics STC3117 Fuel Gauge Unit Power Supply

maintainers:
  - <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>
  - <PERSON><PERSON><PERSON> <<EMAIL>>

description: |
  The STC3117 includes the STMicroelectronics OptimGauge algorithm.
  It provides accurate battery state-of-charge (SOC) monitoring, tracks
  battery parameter changes with operation conditions, temperature,
  and aging, and allows the application to get a battery state-of-health
  (SOH) indication.

  An alarm output signals low SOC or low voltage conditions and also
  indicates fault conditions like a missing or swapped battery.

  Datasheet is available at
  https://www.st.com/resource/en/datasheet/stc3117.pdf

allOf:
  - $ref: power-supply.yaml#

properties:
  compatible:
    enum:
      - st,stc3117

  reg:
    maxItems: 1

  monitored-battery:
    description: |
      The fuel gauge uses the following battery properties:
      - charge-full-design-microamp-hours
      - voltage-min-design-microvolt
      - voltage-max-design-microvolt

  shunt-resistor-micro-ohms:
    description: Current sense resistor

  interrupts:
    maxItems: 1

required:
  - compatible
  - reg
  - monitored-battery
  - shunt-resistor-micro-ohms

unevaluatedProperties: false

examples:
  - |
    #include <dt-bindings/interrupt-controller/irq.h>

    i2c {
      #address-cells = <1>;
      #size-cells = <0>;

      battery@70 {
        compatible = "st,stc3117";
        reg = <0x70>;
        interrupt-parent = <&gpio0>;
        interrupts = <31 IRQ_TYPE_LEVEL_LOW>;
        monitored-battery = <&bat>;
        shunt-resistor-micro-ohms = <10000>;
      };
    };
