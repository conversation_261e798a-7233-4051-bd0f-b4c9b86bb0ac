# SPDX-License-Identifier: (GPL-2.0-only OR BSD-2-Clause)
%YAML 1.2
---
$id: http://devicetree.org/schemas/power/supply/maxim,max8971.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: Maxim MAX8971 IC charger

maintainers:
  - <PERSON><PERSON><PERSON> R<PERSON>hel <<EMAIL>>

description:
  The MAX8971 is a compact, high-frequency, high-efficiency switch-mode charger
  for a one-cell lithium-ion (Li+) battery.

allOf:
  - $ref: power-supply.yaml#

properties:
  compatible:
    const: maxim,max8971

  reg:
    maxItems: 1

  interrupts:
    maxItems: 1

  monitored-battery: true

  port:
    description:
      An optional port node to link the extcon device to detect type of plug.
    $ref: /schemas/graph.yaml#/properties/port

required:
  - compatible
  - reg
  - interrupts

unevaluatedProperties: false

examples:
  - |
    #include <dt-bindings/gpio/gpio.h>
    #include <dt-bindings/interrupt-controller/irq.h>

    i2c {
        #address-cells = <1>;
        #size-cells = <0>;

        charger@35 {
            compatible = "maxim,max8971";
            reg = <0x35>;

            interrupt-parent = <&gpio>;
            interrupts = <74 IRQ_TYPE_LEVEL_LOW>;

            monitored-battery = <&battery>;

            port {
                charger_input: endpoint {
                    remote-endpoint = <&extcon_output>;
                };
            };
        };
    };
...
