# SPDX-License-Identifier: GPL-2.0
%YAML 1.2
---
$id: http://devicetree.org/schemas/power/supply/maxim,max17042.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: Maxim 17042 fuel gauge series

maintainers:
  - <PERSON> <<EMAIL>>

allOf:
  - $ref: power-supply.yaml#

properties:
  compatible:
    enum:
      - maxim,max17042
      - maxim,max17047
      - maxim,max17050
      - maxim,max17055
      - maxim,max77705-battery
      - maxim,max77849-battery

  reg:
    maxItems: 1

  interrupts:
    maxItems: 1
    description: |
      The ALRT pin, an open-drain interrupt.

  maxim,rsns-microohm:
    $ref: /schemas/types.yaml#/definitions/uint32
    description: |
      Resistance of rsns resistor in micro Ohms (datasheet-recommended value is 10000).
      Defining this property enables current-sense functionality.

  maxim,cold-temp:
    $ref: /schemas/types.yaml#/definitions/uint32
    description: |
      Temperature threshold to report battery as cold (in tenths of degree Celsius).
      Default is not to report cold events.

  maxim,over-heat-temp:
    $ref: /schemas/types.yaml#/definitions/uint32
    description: |
      Temperature threshold to report battery as over heated (in tenths of degree Celsius).
      Default is not to report over heating events.

  maxim,dead-volt:
    $ref: /schemas/types.yaml#/definitions/uint32
    description: |
      Voltage threshold to report battery as dead (in mV).
      Default is not to report dead battery events.

  maxim,over-volt:
    $ref: /schemas/types.yaml#/definitions/uint32
    description: |
      Voltage threshold to report battery as over voltage (in mV).
      Default is not to report over-voltage events.

  power-supplies: true

required:
  - compatible
  - reg

additionalProperties: false

examples:
  - |
    i2c {
      #address-cells = <1>;
      #size-cells = <0>;

      battery@36 {
        compatible = "maxim,max17042";
        reg = <0x36>;
        maxim,rsns-microohm = <10000>;
        maxim,over-heat-temp = <600>;
        maxim,over-volt = <4300>;
      };
    };
