# SPDX-License-Identifier: GPL-2.0-only OR BSD-2-Clause
%YAML 1.2
---
$id: http://devicetree.org/schemas/power/supply/ti,twl6030-charger.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: TWL6030/32 BCI (Battery Charger Interface)

description:
  The battery charger needs to be configured to do any charging besides of
  precharging. The GPADC in the PMIC has to be used to get the related
  voltages.

maintainers:
  - <PERSON> <<EMAIL>>

allOf:
  - $ref: power-supply.yaml#

properties:
  compatible:
    oneOf:
      - const: ti,twl6030-charger
      - items:
          - const: ti,twl6032-charger
          - const: ti,twl6030-charger

  interrupts:
    items:
      - description: Charger Control Interrupt
      - description: Charger Fault Interrupt

  io-channels:
    items:
      - description: VBUS Voltage Channel

  io-channel-names:
    items:
      - const: vusb

  monitored-battery: true

required:
  - compatible
  - interrupts
  - monitored-battery

additionalProperties: false
