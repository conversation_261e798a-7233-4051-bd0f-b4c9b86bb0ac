# SPDX-License-Identifier: (GPL-2.0 OR BSD-2-Clause)
%YAML 1.2
---
$id: http://devicetree.org/schemas/power/reset/qcom,pon.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: Qualcomm PON Device

maintainers:
  - <PERSON><PERSON> <<EMAIL>>

description: |
  The Power On device for Qualcomm PM8xxx is MFD supporting pwrkey
  and resin along with the Android reboot-mode.

  This DT node has pwrkey and resin as sub nodes.

properties:
  compatible:
    enum:
      - qcom,pm8916-pon
      - qcom,pm8941-pon
      - qcom,pms405-pon
      - qcom,pm8998-pon
      - qcom,pmk8350-pon

  reg:
    description: |
      Specifies the SPMI base address for the PON (power-on) peripheral.  For
      PMICs that have the PON peripheral (GEN3) split into PON_HLOS and PON_PBS
      (e.g. PMK8350), this can hold addresses of both PON_HLOS and PON_PBS
      peripherals.  In that case, the PON_PBS address needs to be specified to
      facilitate software debouncing on some PMIC.
    minItems: 1
    maxItems: 2

  reg-names:
    minItems: 1
    maxItems: 2

  pwrkey:
    type: object
    $ref: /schemas/input/qcom,pm8941-pwrkey.yaml#

  resin:
    type: object
    $ref: /schemas/input/qcom,pm8941-pwrkey.yaml#

  watchdog:
    type: object
    $ref: /schemas/watchdog/qcom,pm8916-wdt.yaml

required:
  - compatible
  - reg

patternProperties:
  "^mode-.*$":
    maxItems: 1

unevaluatedProperties: false

allOf:
  - if:
      properties:
        compatible:
          contains:
            enum:
              - qcom,pm8916-pon
              - qcom,pms405-pon
              - qcom,pm8998-pon
    then:
      allOf:
        - $ref: reboot-mode.yaml#

      properties:
        reg:
          maxItems: 1
        reg-names:
          items:
            - const: pon
    else:
      patternProperties:
        "^mode-.*$": false

    # Special case for pm8941, which doesn't store reset mode
  - if:
      properties:
        compatible:
          contains:
            const: qcom,pm8941-pon
    then:
      properties:
        reg:
          maxItems: 1
        reg-names:
          items:
            - const: pon

  - if:
      properties:
        compatible:
          contains:
            const: qcom,pmk8350-pon
    then:
      properties:
        reg:
          minItems: 1
          maxItems: 2
        reg-names:
          minItems: 1
          items:
            - const: hlos
            - const: pbs

examples:
  - |
   #include <dt-bindings/interrupt-controller/irq.h>
   #include <dt-bindings/input/linux-event-codes.h>
   #include <dt-bindings/spmi/spmi.h>

   spmi@c440000 {
     reg = <0x0c440000 0x1100>;
     #address-cells = <2>;
     #size-cells = <0>;

     pmic@0 {
       reg = <0x0 SPMI_USID>;
       #address-cells = <1>;
       #size-cells = <0>;

       pon@800 {
         compatible = "qcom,pm8998-pon";
         reg = <0x800>;

         pwrkey {
            compatible = "qcom,pm8941-pwrkey";
            interrupts = <0x0 0x8 0 IRQ_TYPE_EDGE_BOTH>;
            debounce = <15625>;
            bias-pull-up;
            linux,code = <KEY_POWER>;
         };

         resin {
            compatible = "qcom,pm8941-resin";
            interrupts = <0x0 0x8 1 IRQ_TYPE_EDGE_BOTH>;
            debounce = <15625>;
            bias-pull-up;
            linux,code = <KEY_VOLUMEDOWN>;
         };
       };
     };
   };
...
