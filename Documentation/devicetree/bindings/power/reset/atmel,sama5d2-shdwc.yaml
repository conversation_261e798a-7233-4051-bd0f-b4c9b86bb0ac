# SPDX-License-Identifier: (GPL-2.0-only OR BSD-2-Clause)
%YAML 1.2
---
$id: http://devicetree.org/schemas/power/reset/atmel,sama5d2-shdwc.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: Microchip AT91 SAMA5D2 SHDWC Shutdown Controller

maintainers:
  - <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>

description: |
  Microchip AT91 SHDWC shutdown controller controls the power supplies VDDIO
  and VDDCORE and the wake-up detection on debounced input lines.

properties:
  compatible:
    oneOf:
      - items:
          - enum:
              - microchip,sama7d65-shdwc
          - const: microchip,sama7g5-shdwc
          - const: syscon
      - items:
          - const: microchip,sama7g5-shdwc
          - const: syscon
      - enum:
          - atmel,sama5d2-shdwc
          - microchip,sam9x60-shdwc
      - items:
          - const: microchip,sam9x7-shdwc
          - const: microchip,sam9x60-shdwc

  reg:
    maxItems: 1

  clocks:
    maxItems: 1

  "#address-cells":
    const: 1

  "#size-cells":
    const: 0

  debounce-delay-us:
    description:
      Minimum wake-up inputs debouncer period in microseconds. It is usually a
      board-related property.

  atmel,wakeup-rtc-timer:
    description: enable real-time clock wake-up
    type: boolean

  atmel,wakeup-rtt-timer:
    description: enable real-time timer wake-up
    type: boolean

patternProperties:
  "^input@[0-15]$":
    description:
      Wake-up input nodes. These are usually described in the "board" part of
      the Device Tree. Note also that input 0 is linked to the wake-up pin and
      is frequently used.
    type: object
    properties:
      reg:
        description: contains the wake-up input index
        minimum: 0
        maximum: 15

      atmel,wakeup-active-high:
        description:
          The corresponding wake-up input described by the child forces the
          wake-up of the core power supply on a high level. The default is to
          be active low.
        type: boolean

    required:
      - reg

    additionalProperties: false

required:
  - compatible
  - reg
  - clocks

allOf:
  - if:
      properties:
        compatible:
          contains:
            const: atmel,sama5d2-shdwc
    then:
      properties:
        atmel,wakeup-rtt-timer: false

additionalProperties: false

examples:
  - |
    shdwc: poweroff@f8048010 {
        compatible = "atmel,sama5d2-shdwc";
        reg = <0xf8048010 0x10>;
        clocks = <&clk32k>;
        #address-cells = <1>;
        #size-cells = <0>;
        atmel,wakeup-rtc-timer;
        debounce-delay-us = <976>;

        input@0 {
            reg = <0>;
        };

        input@1 {
            reg = <1>;
            atmel,wakeup-active-high;
        };
    };

...
