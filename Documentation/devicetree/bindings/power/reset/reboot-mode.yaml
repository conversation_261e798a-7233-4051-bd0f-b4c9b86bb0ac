# SPDX-License-Identifier: (GPL-2.0-only OR BSD-2-Clause)
%YAML 1.2
---
$id: http://devicetree.org/schemas/power/reset/reboot-mode.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: Generic reboot mode core map

maintainers:
  - <PERSON> <<EMAIL>>

description: |
  This driver get reboot mode arguments and call the write
  interface to store the magic value in special register
  or ram. Then the bootloader can read it and take different
  action according to the argument stored.

  All mode properties are vendor specific, it is a indication to tell
  the bootloader what to do when the system reboots, and should be named
  as mode-xxx = <magic> (xxx is mode name, magic should be a non-zero value).

  For example, modes common Android platform are:
    - normal: Normal reboot mode, system reboot with command "reboot".
    - recovery: Android Recovery mode, it is a mode to format the device or update a new image.
    - bootloader: Android fastboot mode, it's a mode to re-flash partitions on the Android based device.
    - loader: A bootloader mode, it's a mode used to download image on Rockchip platform,
              usually used in development.

properties:
  mode-normal:
    $ref: /schemas/types.yaml#/definitions/uint32-array
    description:
      Default value to set on a reboot if no command was provided.

patternProperties:
  "^mode-.*$":
    $ref: /schemas/types.yaml#/definitions/uint32-array

additionalProperties: true

examples:
  - |
    reboot-mode {
      mode-normal = <0>;
      mode-recovery = <1>;
      mode-bootloader = <2>;
      mode-loader = <3>;
    };
...
