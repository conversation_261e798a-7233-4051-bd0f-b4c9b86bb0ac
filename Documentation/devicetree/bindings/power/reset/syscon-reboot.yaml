# SPDX-License-Identifier: GPL-2.0
%YAML 1.2
---
$id: http://devicetree.org/schemas/power/reset/syscon-reboot.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: Generic SYSCON mapped register reset driver

maintainers:
  - <PERSON> <<EMAIL>>

description: |+
  This is a generic reset driver using syscon to map the reset register.
  The reset is generally performed with a write to the reset register
  defined by the SYSCON register map base plus the offset with the value and
  mask defined in the reboot node. Default will be little endian mode, 32 bit
  access only. The SYSCON registers map is normally retrieved from the
  parental dt-node. So the SYSCON reboot node should be represented as a
  sub-node of a "syscon", "simple-mfd" node. Though the regmap property
  pointing to the system controller node is also supported.

properties:
  compatible:
    enum:
      - syscon-reboot
      - google,gs101-reboot

  mask:
    $ref: /schemas/types.yaml#/definitions/uint32
    description: Update only the register bits defined by the mask (32 bit).

  offset:
    $ref: /schemas/types.yaml#/definitions/uint32
    description: Offset in the register map for the reboot register (in bytes).

  reg:
    maxItems: 1
    description: Base address and size for the reboot register.

  regmap:
    $ref: /schemas/types.yaml#/definitions/phandle
    deprecated: true
    description: |
      Phandle to the register map node. This property is deprecated in favor of
      the syscon-reboot node been a child of a system controller node.

  value:
    $ref: /schemas/types.yaml#/definitions/uint32
    description: The reset value written to the reboot register (32 bit access).

  priority:
    default: 192

required:
  - compatible

additionalProperties: false

allOf:
  - $ref: restart-handler.yaml#
  - if:
      properties:
        compatible:
          contains:
            const: google,gs101-reboot
    then:
      properties:
        mask: false
        offset: false
        reg: false
        value: false

    else:
      if:
        not:
          required:
            - mask
      then:
        required:
          - value

      oneOf:
        - required: [offset]
        - required: [reg]

examples:
  - |
    reboot {
        compatible = "syscon-reboot";
        regmap = <&regmapnode>;
        offset = <0x0>;
        mask = <0x1>;
    };

  - |
    reboot {
        compatible = "google,gs101-reboot";
    };
