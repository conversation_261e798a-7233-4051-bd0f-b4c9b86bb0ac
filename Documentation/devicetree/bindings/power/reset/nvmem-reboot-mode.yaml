# SPDX-License-Identifier: (GPL-2.0-only OR BSD-2-Clause)
%YAML 1.2
---
$id: http://devicetree.org/schemas/power/reset/nvmem-reboot-mode.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: Generic NVMEM reboot mode

maintainers:
  - <PERSON><PERSON><PERSON> <bartosz.go<PERSON>@linaro.org>

description:
  This driver gets the reboot mode magic value from the reboot-mode driver
  and stores it in the NVMEM cell named "reboot-mode". The bootloader can
  then read it and take different action according to the value.

properties:
  compatible:
    const: nvmem-reboot-mode

  nvmem-cells:
    description:
      A phandle pointing to the nvmem-cells node where the vendor-specific
      magic value representing the reboot mode is stored.
    maxItems: 1

  nvmem-cell-names:
    items:
      - const: reboot-mode

allOf:
  - $ref: reboot-mode.yaml#

patternProperties:
  "^mode-.*$":
    maxItems: 1

required:
  - compatible
  - nvmem-cells
  - nvmem-cell-names

unevaluatedProperties: false

examples:
  - |
    reboot-mode {
        compatible = "nvmem-reboot-mode";
        nvmem-cells = <&reboot_reason>;
        nvmem-cell-names = "reboot-mode";
        mode-recovery = <0x01>;
        mode-bootloader = <0x02>;
    };
...
