# SPDX-License-Identifier: GPL-2.0-only
%YAML 1.2
---
$id: http://devicetree.org/schemas/power/reset/syscon-reboot-mode.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: Generic SYSCON reboot mode driver

maintainers:
  - <PERSON> <<EMAIL>>

description: |
  This driver gets reboot mode magic value from reboot-mode driver
  and stores it in a SYSCON mapped register. Then the bootloader
  can read it and take different action according to the magic
  value stored. The SYSCON mapped register is retrieved from the
  parental dt-node plus the offset. So the SYSCON reboot-mode node
  should be represented as a sub-node of a "syscon", "simple-mfd" node.

properties:
  compatible:
    const: syscon-reboot-mode

  mask:
    $ref: /schemas/types.yaml#/definitions/uint32
    description: Update only the register bits defined by the mask (32 bit)

  offset:
    $ref: /schemas/types.yaml#/definitions/uint32
    description: Offset in the register map for the mode register (in bytes)

allOf:
  - $ref: reboot-mode.yaml#

patternProperties:
  "^mode-.*$":
    maxItems: 1

unevaluatedProperties: false

required:
  - compatible
  - offset

examples:
  - |
    #include <dt-bindings/soc/rockchip,boot-mode.h>

    reboot-mode {
      compatible = "syscon-reboot-mode";
      offset = <0x40>;
      mode-normal = <BOOT_NORMAL>;
      mode-recovery = <BOOT_RECOVERY>;
      mode-bootloader = <BOOT_FASTBOOT>;
      mode-loader = <BOOT_BL_DOWNLOAD>;
    };
...
