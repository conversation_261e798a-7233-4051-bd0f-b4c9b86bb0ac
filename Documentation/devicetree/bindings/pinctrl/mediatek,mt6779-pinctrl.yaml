# SPDX-License-Identifier: (GPL-2.0-only OR BSD-2-Clause)
%YAML 1.2
---
$id: http://devicetree.org/schemas/pinctrl/mediatek,mt6779-pinctrl.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: MediaTek MT6779 Pin Controller

maintainers:
  - <PERSON>g <<EMAIL>>
  - <PERSON> <<EMAIL>>

description:
  The MediaTek pin controller on MT6779 is used to control pin functions, pull
  up/down resistance and drive strength options.

properties:
  compatible:
    enum:
      - mediatek,mt6779-pinctrl
      - mediatek,mt6797-pinctrl

  reg:
    description: Physical addresses for GPIO base(s) and EINT registers.

  reg-names: true

  gpio-controller: true

  "#gpio-cells":
    const: 2
    description:
      Number of cells in GPIO specifier. Since the generic GPIO binding is used,
      the amount of cells must be specified as 2. See the below mentioned gpio
      binding representation for description of particular cells.

  gpio-ranges:
    minItems: 1
    maxItems: 5
    description:
      GPIO valid number range.

  interrupt-controller: true

  interrupts:
    maxItems: 1
    description:
      Specifies the summary IRQ.

  "#interrupt-cells":
    const: 2

required:
  - compatible
  - reg
  - reg-names
  - gpio-controller
  - "#gpio-cells"

allOf:
  - $ref: pinctrl.yaml#
  - if:
      properties:
        compatible:
          contains:
            const: mediatek,mt6779-pinctrl
    then:
      properties:
        reg:
          minItems: 9
          maxItems: 9

        reg-names:
          items:
            - const: gpio
            - const: iocfg_rm
            - const: iocfg_br
            - const: iocfg_lm
            - const: iocfg_lb
            - const: iocfg_rt
            - const: iocfg_lt
            - const: iocfg_tl
            - const: eint
  - if:
      properties:
        compatible:
          contains:
            const: mediatek,mt6797-pinctrl
    then:
      properties:
        reg:
          minItems: 5
          maxItems: 5

        reg-names:
          items:
            - const: gpio
            - const: iocfgl
            - const: iocfgb
            - const: iocfgr
            - const: iocfgt
  - if:
      properties:
        reg-names:
          contains:
            const: eint
    then:
      required:
        - interrupts
        - interrupt-controller
        - "#interrupt-cells"

patternProperties:
  '-[0-9]*$':
    type: object
    additionalProperties: false

    patternProperties:
      '-pins*$':
        type: object
        description:
          A pinctrl node should contain at least one subnodes representing the
          pinctrl groups available on the machine. Each subnode will list the
          pins it needs, and how they should be configured, with regard to muxer
          configuration, pullups, drive strength, input enable/disable and input
          schmitt.
        $ref: /schemas/pinctrl/pincfg-node.yaml

        properties:
          pinmux:
            description:
              Integer array, represents gpio pin number and mux setting.
              Supported pin number and mux varies for different SoCs, and are
              defined as macros in dt-bindings/pinctrl/<soc>-pinfunc.h directly.

          bias-disable: true

          bias-pull-up: true

          bias-pull-down: true

          input-enable: true

          input-disable: true

          output-low: true

          output-high: true

          input-schmitt-enable: true

          input-schmitt-disable: true

          drive-strength:
            enum: [2, 4, 8, 12, 16]

          slew-rate:
            enum: [0, 1]

          mediatek,pull-up-adv:
            description: |
              Pull up settings for 2 pull resistors, R0 and R1. User can
              configure those special pins. Valid arguments are described as
              below:
              0: (R1, R0) = (0, 0) which means R1 disabled and R0 disabled.
              1: (R1, R0) = (0, 1) which means R1 disabled and R0 enabled.
              2: (R1, R0) = (1, 0) which means R1 enabled and R0 disabled.
              3: (R1, R0) = (1, 1) which means R1 enabled and R0 enabled.
            $ref: /schemas/types.yaml#/definitions/uint32
            enum: [0, 1, 2, 3]

          mediatek,pull-down-adv:
            description: |
              Pull down settings for 2 pull resistors, R0 and R1. User can
              configure those special pins. Valid arguments are described as
              below:
              0: (R1, R0) = (0, 0) which means R1 disabled and R0 disabled.
              1: (R1, R0) = (0, 1) which means R1 disabled and R0 enabled.
              2: (R1, R0) = (1, 0) which means R1 enabled and R0 disabled.
              3: (R1, R0) = (1, 1) which means R1 enabled and R0 enabled.
            $ref: /schemas/types.yaml#/definitions/uint32
            enum: [0, 1, 2, 3]

        required:
          - pinmux

        additionalProperties: false

additionalProperties: false

examples:
  - |
    #include <dt-bindings/interrupt-controller/irq.h>
    #include <dt-bindings/interrupt-controller/arm-gic.h>
    #include <dt-bindings/pinctrl/mt6779-pinfunc.h>

    soc {
        #address-cells = <2>;
        #size-cells = <2>;

        pio: pinctrl@10005000 {
            compatible = "mediatek,mt6779-pinctrl";
            reg = <0 0x10005000 0 0x1000>,
                <0 0x11c20000 0 0x1000>,
                <0 0x11d10000 0 0x1000>,
                <0 0x11e20000 0 0x1000>,
                <0 0x11e70000 0 0x1000>,
                <0 0x11ea0000 0 0x1000>,
                <0 0x11f20000 0 0x1000>,
                <0 0x11f30000 0 0x1000>,
                <0 0x1000b000 0 0x1000>;
            reg-names = "gpio", "iocfg_rm",
              "iocfg_br", "iocfg_lm",
              "iocfg_lb", "iocfg_rt",
              "iocfg_lt", "iocfg_tl",
              "eint";
            gpio-controller;
            #gpio-cells = <2>;
            gpio-ranges = <&pio 0 0 210>;
            interrupt-controller;
            #interrupt-cells = <2>;
            interrupts = <GIC_SPI 204 IRQ_TYPE_LEVEL_HIGH>;

            mmc0_pins_default: mmc0-0 {
                cmd-dat-pins {
                    pinmux = <PINMUX_GPIO168__FUNC_MSDC0_DAT0>,
                        <PINMUX_GPIO172__FUNC_MSDC0_DAT1>,
                        <PINMUX_GPIO169__FUNC_MSDC0_DAT2>,
                        <PINMUX_GPIO177__FUNC_MSDC0_DAT3>,
                        <PINMUX_GPIO170__FUNC_MSDC0_DAT4>,
                        <PINMUX_GPIO173__FUNC_MSDC0_DAT5>,
                        <PINMUX_GPIO171__FUNC_MSDC0_DAT6>,
                        <PINMUX_GPIO174__FUNC_MSDC0_DAT7>,
                        <PINMUX_GPIO167__FUNC_MSDC0_CMD>;
                    input-enable;
                    mediatek,pull-up-adv = <1>;
                };
                clk-pins {
                    pinmux = <PINMUX_GPIO176__FUNC_MSDC0_CLK>;
                    mediatek,pull-down-adv = <2>;
                };
                rst-pins {
                    pinmux = <PINMUX_GPIO178__FUNC_MSDC0_RSTB>;
                    mediatek,pull-up-adv = <0>;
                };
            };
        };
    };
