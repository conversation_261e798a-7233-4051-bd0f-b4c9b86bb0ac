# SPDX-License-Identifier: (GPL-2.0-only OR BSD-2-Clause)
%YAML 1.2
---
$id: http://devicetree.org/schemas/pinctrl/allwinner,sun55i-a523-pinctrl.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: Allwinner A523 Pin Controller

maintainers:
  - <PERSON> <<EMAIL>>

properties:
  "#gpio-cells":
    const: 3
    description:
      GPIO consumers must use three arguments, first the number of the
      bank, then the pin number inside that bank, and finally the GPIO
      flags.

  "#interrupt-cells":
    const: 3
    description:
      Interrupts consumers must use three arguments, first the number
      of the bank, then the pin number inside that bank, and finally
      the interrupts flags.

  compatible:
    enum:
      - allwinner,sun55i-a523-pinctrl
      - allwinner,sun55i-a523-r-pinctrl

  reg:
    maxItems: 1

  interrupts:
    minItems: 2
    maxItems: 10
    description:
      One interrupt per external interrupt bank supported on the
      controller, sorted by bank number ascending order.

  clocks:
    items:
      - description: Bus Clock
      - description: High Frequency Oscillator
      - description: Low Frequency Oscillator

  clock-names:
    items:
      - const: apb
      - const: hosc
      - const: losc

  gpio-controller: true
  interrupt-controller: true
  gpio-line-names: true

  input-debounce:
    description:
      Debouncing periods in microseconds, one period per interrupt
      bank found in the controller
    $ref: /schemas/types.yaml#/definitions/uint32-array
    minItems: 2
    maxItems: 10

patternProperties:
  # It's pretty scary, but the basic idea is that:
  #   - One node name can start with either s- or r- for PRCM nodes,
  #   - Then, the name itself can be any repetition of <string>- (to
  #     accommodate with nodes like uart4-rts-cts-pins), where each
  #     string can be either starting with 'p' but in a string longer
  #     than 3, or something that doesn't start with 'p',
  #   - Then, the bank name is optional and will be between pa and pm.
  #     Some pins groups that have several options will have the pin
  #     numbers then,
  #   - Finally, the name will end with either -pin or pins.

  "^([rs]-)?(([a-z0-9]{3,}|[a-oq-z][a-z0-9]*?)?-)+?(p[a-m][0-9]*?-)??pins?$":
    type: object

    properties:
      pins: true
      function: true
      bias-disable: true
      bias-pull-up: true
      bias-pull-down: true

      drive-strength:
        $ref: /schemas/types.yaml#/definitions/uint32
        enum: [10, 20, 30, 40]

      allwinner,pinmux:
        $ref: /schemas/types.yaml#/definitions/uint32-array
        description:
          Pinmux selector value, for each pin. Almost every time this value
          is the same for all pins, so any array shorter than the number of
          pins will repeat the last value, to allow just specifying a single
          cell, for all cells.

    required:
      - pins
      - allwinner,pinmux
      - function

    additionalProperties: false

  "^vcc-p[a-m]-supply$":
    description:
      Power supplies for pin banks.

required:
  - "#gpio-cells"
  - compatible
  - reg
  - clocks
  - clock-names
  - gpio-controller
  - "#interrupt-cells"
  - interrupts
  - interrupt-controller

allOf:
  - $ref: pinctrl.yaml#
  - if:
      properties:
        compatible:
          enum:
            - allwinner,sun55i-a523-pinctrl

    then:
      properties:
        interrupts:
          minItems: 10
          maxItems: 10

  - if:
      properties:
        compatible:
          enum:
            - allwinner,sun55i-a523-r-pinctrl

    then:
      properties:
        interrupts:
          minItems: 2
          maxItems: 2

additionalProperties: false

examples:
  - |
    r_pio: pinctrl@7022000 {
        compatible = "allwinner,sun55i-a523-r-pinctrl";
        reg = <0x7022000 0x800>;
        interrupts = <0 159 4>, <0 161 4>;
        clocks = <&r_ccu 1>, <&osc24M>, <&osc32k>;
        clock-names = "apb", "hosc", "losc";
        gpio-controller;
        #gpio-cells = <3>;
        interrupt-controller;
        #interrupt-cells = <3>;

        r_i2c_pins: r-i2c-pins {
            pins = "PL0", "PL1";
            allwinner,pinmux = <2>;
            function = "r_i2c0";
            bias-pull-up;
        };

        r_spi_pins: r-spi-pins {
            pins = "PL11" ,"PL12", "PL13";
            allwinner,pinmux = <6>;
            function = "r_spi";
        };
    };
