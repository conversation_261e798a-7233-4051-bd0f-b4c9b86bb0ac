# SPDX-License-Identifier: (GPL-2.0-only OR BSD-2-Clause)
%YAML 1.2
---
$id: http://devicetree.org/schemas/pinctrl/qcom,sm8650-lpass-lpi-pinctrl.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: Qualcomm SM8650 SoC LPASS LPI TLMM

maintainers:
  - <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> <krzysztof.koz<PERSON><PERSON>@linaro.org>
  - <PERSON><PERSON><PERSON> <<EMAIL>>

description:
  Top Level Mode Multiplexer pin controller in the Low Power Audio SubSystem
  (LPASS) Low Power Island (LPI) of Qualcomm SM8650 SoC.

properties:
  compatible:
    oneOf:
      - const: qcom,sm8650-lpass-lpi-pinctrl
      - items:
          - const: qcom,sm8750-lpass-lpi-pinctrl
          - const: qcom,sm8650-lpass-lpi-pinctrl

  reg:
    items:
      - description: LPASS LPI TLMM Control and Status registers

  clocks:
    items:
      - description: LPASS Core voting clock
      - description: LPASS Audio voting clock

  clock-names:
    items:
      - const: core
      - const: audio

patternProperties:
  "-state$":
    oneOf:
      - $ref: "#/$defs/qcom-sm8650-lpass-state"
      - patternProperties:
          "-pins$":
            $ref: "#/$defs/qcom-sm8650-lpass-state"
        additionalProperties: false

$defs:
  qcom-sm8650-lpass-state:
    type: object
    description:
      Pinctrl node's client devices use subnodes for desired pin configuration.
      Client device subnodes use below standard properties.
    $ref: qcom,lpass-lpi-common.yaml#/$defs/qcom-tlmm-state
    unevaluatedProperties: false

    properties:
      pins:
        description:
          List of gpio pins affected by the properties specified in this
          subnode.
        items:
          pattern: "^gpio([0-9]|1[0-9]|2[0-2])$"

      function:
        enum: [ dmic1_clk, dmic1_data, dmic2_clk, dmic2_data, dmic3_clk,
                dmic3_data, dmic4_clk, dmic4_data, ext_mclk1_a, ext_mclk1_b,
                ext_mclk1_c, ext_mclk1_d, ext_mclk1_e, gpio, i2s0_clk,
                i2s0_data, i2s0_ws, i2s1_clk, i2s1_data, i2s1_ws, i2s2_clk,
                i2s2_data, i2s2_ws, i2s3_clk, i2s3_data, i2s3_ws, i2s4_clk,
                i2s4_data, i2s4_ws, qca_swr_clk, qca_swr_data, slimbus_clk,
                slimbus_data, swr_rx_clk, swr_rx_data, swr_tx_clk, swr_tx_data,
                wsa_swr_clk, wsa_swr_data, wsa2_swr_clk, wsa2_swr_data ]
        description:
          Specify the alternative function to be configured for the specified
          pins.

allOf:
  - $ref: qcom,lpass-lpi-common.yaml#

required:
  - compatible
  - reg
  - clocks
  - clock-names

unevaluatedProperties: false

examples:
  - |
    #include <dt-bindings/sound/qcom,q6dsp-lpass-ports.h>

    lpass_tlmm: pinctrl@6e80000 {
        compatible = "qcom,sm8650-lpass-lpi-pinctrl";
        reg = <0x06e80000 0x20000>;

        clocks = <&q6prmcc LPASS_HW_MACRO_VOTE LPASS_CLK_ATTRIBUTE_COUPLE_NO>,
                 <&q6prmcc LPASS_HW_DCODEC_VOTE LPASS_CLK_ATTRIBUTE_COUPLE_NO>;
        clock-names = "core", "audio";

        gpio-controller;
        #gpio-cells = <2>;
        gpio-ranges = <&lpass_tlmm 0 0 23>;

        tx-swr-sleep-clk-state {
            pins = "gpio0";
            function = "swr_tx_clk";
            drive-strength = <2>;
            bias-pull-down;
        };
    };
