# SPDX-License-Identifier: GPL-2.0
%YAML 1.2
---
$id: http://devicetree.org/schemas/pinctrl/rockchip,pinctrl.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: Rockchip Pinmux Controller

maintainers:
  - <PERSON><PERSON> <<EMAIL>>

description: |
  The Rockchip Pinmux Controller enables the IC to share one PAD
  to several functional blocks. The sharing is done by multiplexing
  the PAD input/output signals. For each PAD there are several muxing
  options with option 0 being used as a GPIO.

  Please refer to pinctrl-bindings.txt in this directory for details of the
  common pinctrl bindings used by client devices, including the meaning of the
  phrase "pin configuration node".

  The Rockchip pin configuration node is a node of a group of pins which can be
  used for a specific device or function. This node represents both mux and
  config of the pins in that group. The 'pins' selects the function mode
  (also named pin mode) this pin can work on and the 'config' configures
  various pad settings such as pull-up, etc.

  The pins are grouped into up to 9 individual pin banks which need to be
  defined as gpio sub-nodes of the pinmux controller.

properties:
  compatible:
    enum:
      - rockchip,px30-pinctrl
      - rockchip,rk2928-pinctrl
      - rockchip,rk3036-pinctrl
      - rockchip,rk3066a-pinctrl
      - rockchip,rk3066b-pinctrl
      - rockchip,rk3128-pinctrl
      - rockchip,rk3188-pinctrl
      - rockchip,rk3228-pinctrl
      - rockchip,rk3288-pinctrl
      - rockchip,rk3308-pinctrl
      - rockchip,rk3328-pinctrl
      - rockchip,rk3368-pinctrl
      - rockchip,rk3399-pinctrl
      - rockchip,rk3528-pinctrl
      - rockchip,rk3562-pinctrl
      - rockchip,rk3568-pinctrl
      - rockchip,rk3576-pinctrl
      - rockchip,rk3588-pinctrl
      - rockchip,rv1108-pinctrl
      - rockchip,rv1126-pinctrl

  rockchip,grf:
    $ref: /schemas/types.yaml#/definitions/phandle
    description:
      The phandle of the syscon node for the GRF registers.

  rockchip,pmu:
    $ref: /schemas/types.yaml#/definitions/phandle
    description:
      The phandle of the syscon node for the PMU registers,
      as some SoCs carry parts of the iomux controller registers there.
      Required for at least rk3188 and rk3288. On the rk3368 this should
      point to the PMUGRF syscon.

  "#address-cells":
    enum: [1, 2]

  "#size-cells":
    enum: [1, 2]

  ranges: true

allOf:
  - $ref: pinctrl.yaml#

required:
  - compatible
  - rockchip,grf

patternProperties:
  "gpio@[0-9a-f]+$":
    type: object

    $ref: /schemas/gpio/rockchip,gpio-bank.yaml#
    deprecated: true

    unevaluatedProperties: false

  "pcfg-[a-z0-9-]+$":
    type: object
    properties:
      bias-disable: true

      bias-pull-down: true

      bias-pull-pin-default: true

      bias-pull-up: true

      drive-strength:
        minimum: 0
        maximum: 20

      input-enable: true

      input-schmitt-enable: true

      output-high: true

      output-low: true

    additionalProperties: false

additionalProperties:
  type: object
  additionalProperties:
    type: object
    additionalProperties: false

    properties:
      rockchip,pins:
        $ref: /schemas/types.yaml#/definitions/uint32-matrix
        minItems: 1
        items:
          items:
            - minimum: 0
              maximum: 8
              description:
                Pin bank.
            - minimum: 0
              maximum: 31
              description:
                Pin bank index.
            - minimum: 0
              maximum: 13
              description:
                Mux 0 means GPIO and mux 1 to N means
                the specific device function.
            - description:
                The phandle of a node contains the generic pinconfig options
                to use as described in pinctrl-bindings.txt.

examples:
  - |
    #include <dt-bindings/interrupt-controller/arm-gic.h>
    #include <dt-bindings/pinctrl/rockchip.h>

    pinctrl: pinctrl {
      compatible = "rockchip,rk3066a-pinctrl";
      rockchip,grf = <&grf>;

      #address-cells = <1>;
      #size-cells = <1>;
      ranges;

      gpio0: gpio@******** {
        compatible = "rockchip,gpio-bank";
        reg = <0x******** 0x100>;
        interrupts = <GIC_SPI 54 IRQ_TYPE_LEVEL_HIGH>;
        clocks = <&clk_gates8 9>;

        gpio-controller;
        #gpio-cells = <2>;

        interrupt-controller;
        #interrupt-cells = <2>;
      };

      pcfg_pull_default: pcfg-pull-default {
        bias-pull-pin-default;
      };

      uart2 {
        uart2_xfer: uart2-xfer {
          rockchip,pins = <1 RK_PB0 1 &pcfg_pull_default>,
                          <1 RK_PB1 1 &pcfg_pull_default>;
        };
      };
    };

    uart2: serial@******** {
      compatible = "snps,dw-apb-uart";
      reg = <0x******** 0x400>;
      interrupts = <GIC_SPI 36 IRQ_TYPE_LEVEL_HIGH>;
      clocks = <&mux_uart2>;
      pinctrl-0 = <&uart2_xfer>;
      pinctrl-names = "default";
      reg-io-width = <1>;
      reg-shift = <2>;
    };
