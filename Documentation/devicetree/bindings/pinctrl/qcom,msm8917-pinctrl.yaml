# SPDX-License-Identifier: (GPL-2.0-only OR BSD-2-Clause)
%YAML 1.2
---
$id: http://devicetree.org/schemas/pinctrl/qcom,msm8917-pinctrl.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: Qualcomm MSM8917 TLMM pin controller

maintainers:
  - <PERSON><PERSON><PERSON> <<EMAIL>>

description:
  Top Level Mode Multiplexer pin controller in Qualcomm MSM8917 SoC.

properties:
  compatible:
    const: qcom,msm8917-pinctrl

  reg:
    maxItems: 1

  interrupts:
    maxItems: 1

  gpio-reserved-ranges:
    minItems: 1
    maxItems: 66

  gpio-line-names:
    maxItems: 134

patternProperties:
  "-state$":
    oneOf:
      - $ref: "#/$defs/qcom-msm8917-tlmm-state"
      - patternProperties:
          "-pins$":
            $ref: "#/$defs/qcom-msm8917-tlmm-state"
        additionalProperties: false

$defs:
  qcom-msm8917-tlmm-state:
    type: object
    description:
      Pinctrl node's client devices use subnodes for desired pin configuration.
      Client device subnodes use below standard properties.
    $ref: qcom,tlmm-common.yaml#/$defs/qcom-tlmm-state
    unevaluatedProperties: false

    properties:
      pins:
        description:
          List of gpio pins affected by the properties specified in this
          subnode.
        items:
          oneOf:
            - pattern: "^gpio([0-9]|[1-9][0-9]|1[0-2][0-9]|13[0-3])$"
            - enum: [ sdc1_clk, sdc1_cmd, sdc1_data, sdc1_rclk, sdc2_clk,
                      sdc2_cmd, sdc2_data, qdsd_clk, qdsd_cmd, qdsd_data0,
                      qdsd_data1, qdsd_data2, qdsd_data3 ]
        minItems: 1
        maxItems: 16

      function:
        description:
          Specify the alternative function to be configured for the specified
          pins.

        enum: [ accel_int, adsp_ext, alsp_int, atest_bbrx0, atest_bbrx1,
                atest_char, atest_char0, atest_char1, atest_char2,
                atest_char3, atest_combodac_to_gpio_native,
                atest_gpsadc_dtest0_native, atest_gpsadc_dtest1_native,
                atest_tsens, atest_wlan0, atest_wlan1, audio_ref,
                audio_reset, bimc_dte0, bimc_dte1, blsp6_spi, blsp8_spi,
                blsp_i2c1, blsp_i2c2, blsp_i2c3, blsp_i2c4, blsp_i2c5,
                blsp_i2c6, blsp_i2c7, blsp_i2c8, blsp_spi1, blsp_spi2,
                blsp_spi3, blsp_spi4, blsp_spi5, blsp_spi6, blsp_spi7,
                blsp_spi8, blsp_uart1, blsp_uart2, blsp_uart3, blsp_uart4,
                blsp_uart5, blsp_uart6, blsp_uart7, blsp_uart8, cam0_ldo,
                cam1_rst, cam1_standby, cam2_rst, cam2_standby, cam_mclk,
                cci_async, cci_i2c, cci_timer0, cci_timer1, cdc_pdm0,
                codec_int1, codec_int2, codec_mad, coex_uart, cri_trng,
                cri_trng0, cri_trng1, dbg_out, dmic0_clk, dmic0_data,
                ebi_cdc, ebi_ch0, ext_lpass, forced_usb, fp_gpio, fp_int,
                gcc_gp1_clk_a, gcc_gp1_clk_b, gcc_gp2_clk_a, gcc_gp2_clk_b,
                gcc_gp3_clk_a, gcc_gp3_clk_b, gcc_plltest, gcc_tlmm, gpio,
                gsm0_tx, key_focus, key_snapshot, key_volp, ldo_en,
                ldo_update, lpass_slimbus, lpass_slimbus0, lpass_slimbus1,
                m_voc, mag_int, mdp_vsync, mipi_dsi0, modem_tsync, nav_pps,
                nav_pps_in_a, nav_pps_in_b, nav_tsync, nfc_pwr, ov_ldo,
                pa_indicator, pbs0, pbs1, pbs2, pri_mi2s, pri_mi2s_mclk_a,
                pri_mi2s_mclk_b, pri_mi2s_ws, prng_rosc,
                pwr_crypto_enabled_a, pwr_crypto_enabled_b,
                pwr_modem_enabled_a, pwr_modem_enabled_b, pwr_nav_enabled_a,
                pwr_nav_enabled_b, qdss_cti_trig_in_a0, qdss_cti_trig_in_a1,
                qdss_cti_trig_in_b0, qdss_cti_trig_in_b1,
                qdss_cti_trig_out_a0, qdss_cti_trig_out_a1,
                qdss_cti_trig_out_b0, qdss_cti_trig_out_b1, qdss_traceclk_a,
                qdss_traceclk_b, qdss_tracectl_a, qdss_tracectl_b,
                qdss_tracedata_a, qdss_tracedata_b, sd_write, sdcard_det,
                sec_mi2s, sec_mi2s_mclk_a, sec_mi2s_mclk_b, sensor_rst,
                smb_int, ssbi_wtr1, ts_resout, ts_sample, uim1_clk,
                uim1_data, uim1_present, uim1_reset, uim2_clk, uim2_data,
                uim2_present, uim2_reset, uim_batt, us_emitter, us_euro,
                wcss_bt, wcss_fm, wcss_wlan, wcss_wlan0, wcss_wlan1,
                wcss_wlan2, webcam_rst, webcam_standby, wsa_io, wsa_irq ]

    required:
      - pins

allOf:
  - $ref: /schemas/pinctrl/qcom,tlmm-common.yaml#

required:
  - compatible
  - reg

unevaluatedProperties: false

examples:
  - |
    #include <dt-bindings/interrupt-controller/arm-gic.h>

    tlmm: pinctrl@1000000 {
        compatible = "qcom,msm8917-pinctrl";
        reg = <0x01000000 0x300000>;
        interrupts = <GIC_SPI 208 IRQ_TYPE_LEVEL_HIGH>;
        gpio-controller;
        gpio-ranges = <&tlmm 0 0 134>;
        #gpio-cells = <2>;
        interrupt-controller;
        #interrupt-cells = <2>;

        blsp1-uart2-sleep-state {
            pins = "gpio4", "gpio5";
            function = "gpio";

            drive-strength = <2>;
            bias-pull-down;
        };

        spi1-default-state {
            spi-pins {
                pins = "gpio0", "gpio1", "gpio3";
                function = "blsp_spi1";

                drive-strength = <12>;
                bias-disable;
            };

            cs-pins {
                pins = "gpio2";
                function = "gpio";

                drive-strength = <16>;
                bias-disable;
                output-high;
            };
        };
    };
