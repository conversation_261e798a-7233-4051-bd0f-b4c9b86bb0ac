# SPDX-License-Identifier: (GPL-2.0-only OR BSD-2-Clause)
%YAML 1.2
---
$id: http://devicetree.org/schemas/pinctrl/mediatek,mt8196-pinctrl.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: MediaTek MT8196 Pin Controller

maintainers:
  - <PERSON><PERSON> <<EMAIL>>
  - <PERSON> <<EMAIL>>

description:
  The MediaTek's MT8196 Pin controller is used to control SoC pins.

properties:
  compatible:
    const: mediatek,mt8196-pinctrl

  reg:
    items:
      - description: gpio base
      - description: rt group IO
      - description: rm1 group IO
      - description: rm2 group IO
      - description: rb group IO
      - description: bm1 group IO
      - description: bm2 group IO
      - description: bm3 group IO
      - description: lt group IO
      - description: lm1 group IO
      - description: lm2 group IO
      - description: lb1 group IO
      - description: lb2 group IO
      - description: tm1 group IO
      - description: tm2 group IO
      - description: tm3 group IO
      - description: eint0 group IO
      - description: eint1 group IO
      - description: eint2 group IO
      - description: eint3 group IO
      - description: eint4 group IO

  reg-names:
    items:
      - const: base
      - const: rt
      - const: rm1
      - const: rm2
      - const: rb
      - const: bm1
      - const: bm2
      - const: bm3
      - const: lt
      - const: lm1
      - const: lm2
      - const: lb1
      - const: lb2
      - const: tm1
      - const: tm2
      - const: tm3
      - const: eint0
      - const: eint1
      - const: eint2
      - const: eint3
      - const: eint4

  interrupts:
    description: The interrupt outputs to sysirq.
    maxItems: 1

  interrupt-controller: true

  '#interrupt-cells':
    const: 2

  gpio-controller: true

  '#gpio-cells':
    description:
      Number of cells in GPIO specifier, should be two. The first cell is the
      pin number, the second cell is used to specify optional parameters which
      are defined in <dt-bindings/gpio/gpio.h>.
    const: 2

  gpio-ranges:
    maxItems: 1

  gpio-line-names: true

# PIN CONFIGURATION NODES
patternProperties:
  '-pins$':
    type: object
    additionalProperties: false

    patternProperties:
      '^pins':
        type: object
        $ref: /schemas/pinctrl/pincfg-node.yaml
        additionalProperties: false
        description:
          A pinctrl node should contain at least one subnode representing the
          pinctrl groups available on the machine. Each subnode will list the
          pins it needs, and how they should be configured, with regard to muxer
          configuration, pullups, drive strength, input enable/disable and input
          schmitt.

        properties:
          pinmux:
            description:
              Integer array, represents gpio pin number and mux setting.
              Supported pin number and mux varies for different SoCs, and are
              defined as macros in arch/arm64/boot/dts/mediatek/mt8196-pinfunc.h
              directly, for this SoC.

          drive-strength:
            enum: [2, 4, 6, 8, 10, 12, 14, 16]

          bias-pull-down:
            oneOf:
              - type: boolean
              - enum: [100, 101, 102, 103]
                description: mt8196 pull down PUPD/R0/R1 type define value.
              - enum: [75000, 5000]
                description: mt8196 pull down RSEL type si unit value(ohm).
            description: |
              For pull down type is normal, it doesn't need add R1R0 define
              and resistance value.
              For pull down type is PUPD/R0/R1 type, it can add R1R0 define to
              set different resistance. It can support "MTK_PUPD_SET_R1R0_00" &
              "MTK_PUPD_SET_R1R0_01" & "MTK_PUPD_SET_R1R0_10" &
              "MTK_PUPD_SET_R1R0_11" define in mt8196.
              For pull down type is PD/RSEL, it can add resistance value(ohm)
              to set different resistance by identifying property
              "mediatek,rsel-resistance-in-si-unit". It can support resistance
              value(ohm) "75000" & "5000" in mt8196.

          bias-pull-up:
            oneOf:
              - type: boolean
              - enum: [100, 101, 102, 103]
                description: mt8196 pull up PUPD/R0/R1 type define value.
              - enum: [1000, 1500, 2000, 3000, 4000, 5000, 75000]
                description: mt8196 pull up RSEL type si unit value(ohm).
            description: |
              For pull up type is normal, it don't need add R1R0 define
              and resistance value.
              For pull up type is PUPD/R0/R1 type, it can add R1R0 define to
              set different resistance. It can support "MTK_PUPD_SET_R1R0_00" &
              "MTK_PUPD_SET_R1R0_01" & "MTK_PUPD_SET_R1R0_10" &
              "MTK_PUPD_SET_R1R0_11" define in mt8196.
              For pull up type is PU/RSEL, it can add resistance value(ohm)
              to set different resistance by identifying property
              "mediatek,rsel-resistance-in-si-unit". It can support resistance
              value(ohm) "1000" & "1500" & "2000" & "3000" & "4000" & "5000" &
              "75000" in mt8196.

          bias-disable: true

          output-high: true

          output-low: true

          input-enable: true

          input-disable: true

          input-schmitt-enable: true

          input-schmitt-disable: true

        required:
          - pinmux

required:
  - compatible
  - reg
  - interrupts
  - interrupt-controller
  - '#interrupt-cells'
  - gpio-controller
  - '#gpio-cells'
  - gpio-ranges

additionalProperties: false

examples:
  - |
    #include <dt-bindings/pinctrl/mt65xx.h>
    #include <dt-bindings/interrupt-controller/arm-gic.h>
    #define PINMUX_GPIO99__FUNC_SCL0 (MTK_PIN_NO(99) | 1)
    #define PINMUX_GPIO100__FUNC_SDA0 (MTK_PIN_NO(100) | 1)

    pio: pinctrl@1002d000 {
        compatible = "mediatek,mt8196-pinctrl";
        reg = <0x1002d000 0x1000>,
              <0x12000000 0x1000>,
              <0x12020000 0x1000>,
              <0x12040000 0x1000>,
              <0x12060000 0x1000>,
              <0x12820000 0x1000>,
              <0x12840000 0x1000>,
              <0x12860000 0x1000>,
              <0x13000000 0x1000>,
              <0x13020000 0x1000>,
              <0x13040000 0x1000>,
              <0x130f0000 0x1000>,
              <0x13110000 0x1000>,
              <0x13800000 0x1000>,
              <0x13820000 0x1000>,
              <0x13860000 0x1000>,
              <0x12080000 0x1000>,
              <0x12880000 0x1000>,
              <0x13080000 0x1000>,
              <0x13880000 0x1000>,
              <0x1c54a000 0x1000>;
        reg-names = "base", "rt", "rm1", "rm2", "rb" , "bm1",
                    "bm2", "bm3", "lt", "lm1", "lm2", "lb1",
                    "lb2", "tm1", "tm2", "tm3", "eint0", "eint1",
                    "eint2", "eint3", "eint4";
        gpio-controller;
        #gpio-cells = <2>;
        gpio-ranges = <&pio 0 0 271>;
        interrupt-controller;
        interrupts = <GIC_SPI 396 IRQ_TYPE_LEVEL_HIGH 0>;
        #interrupt-cells = <2>;

        i2c0-pins {
            pins {
                pinmux = <PINMUX_GPIO99__FUNC_SCL0>,
                         <PINMUX_GPIO100__FUNC_SDA0>;
                bias-disable;
            };
        };
    };
