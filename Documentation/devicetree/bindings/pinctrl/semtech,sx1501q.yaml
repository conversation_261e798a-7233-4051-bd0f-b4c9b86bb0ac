# SPDX-License-Identifier: (GPL-2.0-only OR BSD-2-Clause)
# Copyright 2022 Linaro Ltd.
%YAML 1.2
---
$id: http://devicetree.org/schemas/pinctrl/semtech,sx1501q.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: Semtech SX150x GPIO expander

maintainers:
  - <PERSON> <<EMAIL>>

properties:
  compatible:
    enum:
      - semtech,sx1501q
      - semtech,sx1502q
      - semtech,sx1503q
      - semtech,sx1504q
      - semtech,sx1505q
      - semtech,sx1506q
      - semtech,sx1507q
      - semtech,sx1508q
      - semtech,sx1509q

  reg:
    maxItems: 1

  gpio-line-names:
    minItems: 5
    maxItems: 17

  interrupts:
    maxItems: 1

  '#interrupt-cells':
    const: 2

  interrupt-controller: true

  '#gpio-cells':
    const: 2

  gpio-controller: true

  semtech,probe-reset:
    description: Will trigger a reset of the GPIO expander on probe
    type: boolean

patternProperties:
  '-cfg$':
    type: object
    properties:
      pins: true

      bias-disable: true
      bias-pull-up: true
      bias-pull-down: true
      bias-pull-pin-default: true
      drive-push-pull: true
      output-low: true
      output-high: true
      drive-open-drain: true

    required:
      - pins

    allOf:
      - $ref: pincfg-node.yaml#
      - $ref: pinmux-node.yaml#
      - if:
          properties:
            pins:
              contains:
                const: oscio
        then:
          properties:
            bias-disable: false
            bias-pull-up: false
            bias-pull-down: false
            bias-pull-pin-default: false
            drive-open-drain: false

    additionalProperties: false

required:
  - compatible
  - reg
  - '#gpio-cells'
  - gpio-controller

allOf:
  - $ref: pinctrl.yaml#
  - if:
      properties:
        compatible:
          contains:
            enum:
              - semtech,sx1501q
              - semtech,sx1504q
              - semtech,sx1507q
    then:
      properties:
        gpio-line-names:
          minItems: 5
          maxItems: 5
  - if:
      properties:
        compatible:
          contains:
            enum:
              - semtech,sx1502q
              - semtech,sx1505q
              - semtech,sx1508q
    then:
      properties:
        gpio-line-names:
          minItems: 9
          maxItems: 9
  - if:
      properties:
        compatible:
          contains:
            enum:
              - semtech,sx1503q
              - semtech,sx1506q
              - semtech,sx1509q
    then:
      properties:
        gpio-line-names:
          minItems: 17
          maxItems: 17
  - if:
      not:
        properties:
          compatible:
            contains:
              enum:
                - semtech,sx1507q
                - semtech,sx1508q
                - semtech,sx1509q
    then:
      properties:
        semtech,probe-reset: false
  - if:
      properties:
        compatible:
          contains:
            enum:
              - semtech,sx1501q
              - semtech,sx1504q
    then:
      patternProperties:
        '-cfg$':
          properties:
            pins:
              items:
                pattern: '^gpio[0-3]$'
  - if:
      properties:
        compatible:
          contains:
            enum:
              - semtech,sx1502q
              - semtech,sx1505q
    then:
      patternProperties:
        '-cfg$':
          properties:
            pins:
              items:
                pattern: '^gpio[0-7]$'
  - if:
      properties:
        compatible:
          contains:
            enum:
              - semtech,sx1503q
              - semtech,sx1506q
    then:
      patternProperties:
        '-cfg$':
          properties:
            pins:
              items:
                pattern: '^(gpio[0-9]|gpio1[0-5])$'
  - if:
      properties:
        compatible:
          contains:
            const: semtech,sx1507q
    then:
      patternProperties:
        '-cfg$':
          properties:
            pins:
              items:
                pattern: '^(oscio|gpio[0-3])$'
  - if:
      properties:
        compatible:
          contains:
            const: semtech,sx1508q
    then:
      patternProperties:
        '-cfg$':
          properties:
            pins:
              items:
                pattern: '^(oscio|gpio[0-7])$'
  - if:
      properties:
        compatible:
          contains:
            const: semtech,sx1509q
    then:
      patternProperties:
        '-cfg$':
          properties:
            pins:
              items:
                pattern: '^(oscio|gpio[0-9]|gpio1[0-5])$'

additionalProperties: false

examples:
  - |
    #include <dt-bindings/interrupt-controller/irq.h>
    i2c@1000 {
        reg = <0x1000 0x80>;
        #address-cells = <1>;
        #size-cells = <0>;

        pinctrl@20 {
            compatible = "semtech,sx1501q";
            reg = <0x20>;

            #gpio-cells = <2>;
            #interrupt-cells = <2>;

            interrupts = <16 IRQ_TYPE_EDGE_FALLING>;

            gpio-controller;
            interrupt-controller;

            gpio1-cfg {
                  pins = "gpio1";
                  bias-pull-up;
            };
        };
    };
