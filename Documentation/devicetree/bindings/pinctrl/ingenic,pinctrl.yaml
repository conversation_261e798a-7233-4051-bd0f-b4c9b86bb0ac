# SPDX-License-Identifier: (GPL-2.0-only OR BSD-2-Clause)
%YAML 1.2
---
$id: http://devicetree.org/schemas/pinctrl/ingenic,pinctrl.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: Ingenic SoCs pin controller

description: >
  Please refer to pinctrl-bindings.txt in this directory for details of the
  common pinctrl bindings used by client devices, including the meaning of the
  phrase "pin configuration node".

  For the Ingenic SoCs, pin control is tightly bound with GPIO ports. All pins
  may be used as GPIOs, multiplexed device functions are configured within the
  GPIO port configuration registers and it is typical to refer to pins using the
  naming scheme "PxN" where x is a character identifying the GPIO port with
  which the pin is associated and N is an integer from 0 to 31 identifying the
  pin within that GPIO port. For example PA0 is the first pin in GPIO port A,
  and <PERSON><PERSON><PERSON> is the last pin in GPIO port B. The JZ4730, the JZ4740, the JZ4725B,
  the X1000 and the X1830 contains 4 GPIO ports, PA to PD, for a total of 128
  pins. The X2000 and the X2100 contains 5 GPIO ports, PA to PE, for a total of
  160 pins. The JZ4750, the JZ4755 the JZ4760, the JZ4770 and the JZ4780 contains
  6 GPIO ports, PA to PF, for a total of 192 pins. The JZ4775 contains 7 GPIO
  ports, PA to PG, for a total of 224 pins.

maintainers:
  - Paul Cercueil <<EMAIL>>

properties:
  compatible:
    oneOf:
      - enum:
          - ingenic,jz4730-pinctrl
          - ingenic,jz4740-pinctrl
          - ingenic,jz4725b-pinctrl
          - ingenic,jz4750-pinctrl
          - ingenic,jz4755-pinctrl
          - ingenic,jz4760-pinctrl
          - ingenic,jz4770-pinctrl
          - ingenic,jz4775-pinctrl
          - ingenic,jz4780-pinctrl
          - ingenic,x1000-pinctrl
          - ingenic,x1500-pinctrl
          - ingenic,x1600-pinctrl
          - ingenic,x1830-pinctrl
          - ingenic,x2000-pinctrl
          - ingenic,x2100-pinctrl
      - items:
          - const: ingenic,jz4760b-pinctrl
          - const: ingenic,jz4760-pinctrl
      - items:
          - const: ingenic,x1000e-pinctrl
          - const: ingenic,x1000-pinctrl
      - items:
          - const: ingenic,x2000e-pinctrl
          - const: ingenic,x2000-pinctrl

  reg:
    maxItems: 1

  "#address-cells":
    const: 1

  "#size-cells":
    const: 0

patternProperties:
  "^gpio@[0-9]$":
    type: object
    properties:
      compatible:
        enum:
          - ingenic,jz4730-gpio
          - ingenic,jz4740-gpio
          - ingenic,jz4725b-gpio
          - ingenic,jz4750-gpio
          - ingenic,jz4755-gpio
          - ingenic,jz4760-gpio
          - ingenic,jz4770-gpio
          - ingenic,jz4775-gpio
          - ingenic,jz4780-gpio
          - ingenic,x1000-gpio
          - ingenic,x1500-gpio
          - ingenic,x1600-gpio
          - ingenic,x1830-gpio
          - ingenic,x2000-gpio
          - ingenic,x2100-gpio

      reg:
        items:
          - description: The GPIO bank number

      gpio-controller: true

      "#gpio-cells":
        const: 2

      gpio-ranges:
        maxItems: 1

      interrupt-controller: true

      "#interrupt-cells":
        const: 2
        description:
          Refer to ../interrupt-controller/interrupts.txt for more details.

      interrupts:
        maxItems: 1

    required:
      - compatible
      - reg
      - gpio-controller
      - "#gpio-cells"
      - interrupts
      - interrupt-controller
      - "#interrupt-cells"

    additionalProperties: false

allOf:
  - $ref: pinctrl.yaml#

required:
  - compatible
  - reg
  - "#address-cells"
  - "#size-cells"

additionalProperties:
  anyOf:
    - type: object
      allOf:
        - $ref: pincfg-node.yaml#
        - $ref: pinmux-node.yaml#

      properties:
        function: true
        groups: true
        pins: true
        bias-disable: true
        bias-pull-up: true
        bias-pull-down: true
        output-low: true
        output-high: true
      additionalProperties: false

    - type: object
      additionalProperties:
        type: object
        allOf:
          - $ref: pincfg-node.yaml#
          - $ref: pinmux-node.yaml#

        properties:
          function: true
          groups: true
          pins: true
          bias-disable: true
          bias-pull-up: true
          bias-pull-down: true
          output-low: true
          output-high: true
        additionalProperties: false

examples:
  - |
    pinctrl@10010000 {
      compatible = "ingenic,jz4770-pinctrl";
      reg = <0x10010000 0x600>;

      #address-cells = <1>;
      #size-cells = <0>;

      gpio@0 {
        compatible = "ingenic,jz4770-gpio";
        reg = <0>;

        gpio-controller;
        gpio-ranges = <&pinctrl 0 0 32>;
        #gpio-cells = <2>;

        interrupt-controller;
        #interrupt-cells = <2>;

        interrupt-parent = <&intc>;
        interrupts = <17>;
      };
    };
