# SPDX-License-Identifier: (GPL-2.0 OR BSD-2-Clause)
%YAML 1.2
---
$id: http://devicetree.org/schemas/pinctrl/airoha,en7581-pinctrl.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: Airoha EN7581 Pin Controller

maintainers:
  - <PERSON> <<EMAIL>>

description:
  The Airoha's EN7581 Pin controller is used to control SoC pins.

properties:
  compatible:
    const: airoha,en7581-pinctrl

  interrupts:
    maxItems: 1

  gpio-controller: true

  '#gpio-cells':
    const: 2

  gpio-ranges:
    maxItems: 1

  interrupt-controller: true

  '#interrupt-cells':
    const: 2

allOf:
  - $ref: pinctrl.yaml#

required:
  - compatible
  - interrupts
  - gpio-controller
  - "#gpio-cells"
  - interrupt-controller
  - "#interrupt-cells"

patternProperties:
  '-pins$':
    type: object

    patternProperties:
      '^mux(-|$)':
        type: object

        description:
          pinmux configuration nodes.

        $ref: /schemas/pinctrl/pinmux-node.yaml

        properties:
          function:
            description:
              A string containing the name of the function to mux to the group.
            enum: [pon, tod_1pps, sipo, mdio, uart, i2c, jtag, pcm, spi,
                   pcm_spi, i2s, emmc, pnand, pcie_reset, pwm, phy1_led0,
                   phy2_led0, phy3_led0, phy4_led0, phy1_led1, phy2_led1,
                   phy3_led1, phy4_led1]

          groups:
            description:
              An array of strings. Each string contains the name of a group.

        required:
          - function
          - groups

        allOf:
          - if:
              properties:
                function:
                  const: pon
            then:
              properties:
                groups:
                  enum: [pon]
          - if:
              properties:
                function:
                  const: tod_1pps
            then:
              properties:
                groups:
                  enum: [pon_tod_1pps, gsw_tod_1pps]
          - if:
              properties:
                function:
                  const: sipo
            then:
              properties:
                groups:
                  enum: [sipo, sipo_rclk]
          - if:
              properties:
                function:
                  const: mdio
            then:
              properties:
                groups:
                  enum: [mdio]
          - if:
              properties:
                function:
                  const: uart
            then:
              properties:
                groups:
                  items:
                    enum: [uart2, uart2_cts_rts, hsuart, hsuart_cts_rts,
                           uart4, uart5]
                  maxItems: 2
          - if:
              properties:
                function:
                  const: i2c
            then:
              properties:
                groups:
                  enum: [i2c1]
          - if:
              properties:
                function:
                  const: jtag
            then:
              properties:
                groups:
                  enum: [jtag_udi, jtag_dfd]
          - if:
              properties:
                function:
                  const: pcm
            then:
              properties:
                groups:
                  enum: [pcm1, pcm2]
          - if:
              properties:
                function:
                  const: spi
            then:
              properties:
                groups:
                  items:
                    enum: [spi_quad, spi_cs1]
                  maxItems: 2
          - if:
              properties:
                function:
                  const: pcm_spi
            then:
              properties:
                groups:
                  items:
                    enum: [pcm_spi, pcm_spi_int, pcm_spi_rst, pcm_spi_cs1,
                           pcm_spi_cs2_p156, pcm_spi_cs2_p128, pcm_spi_cs3,
                           pcm_spi_cs4]
                  maxItems: 7
          - if:
              properties:
                function:
                  const: i2c
            then:
              properties:
                groups:
                  enum: [i2s]
          - if:
              properties:
                function:
                  const: emmc
            then:
              properties:
                groups:
                  enum: [emmc]
          - if:
              properties:
                function:
                  const: pnand
            then:
              properties:
                groups:
                  enum: [pnand]
          - if:
              properties:
                function:
                  const: pcie_reset
            then:
              properties:
                groups:
                  enum: [pcie_reset0, pcie_reset1, pcie_reset2]
          - if:
              properties:
                function:
                  const: pwm
            then:
              properties:
                groups:
                  enum: [gpio0, gpio1, gpio2, gpio3, gpio4, gpio5, gpio6,
                         gpio7, gpio8, gpio9, gpio10, gpio11, gpio12, gpio13,
                         gpio14, gpio15, gpio16, gpio17, gpio18, gpio19,
                         gpio20, gpio21, gpio22, gpio23, gpio24, gpio25,
                         gpio26, gpio27, gpio28, gpio29, gpio30, gpio31,
                         gpio36, gpio37, gpio38, gpio39, gpio40, gpio41,
                         gpio42, gpio43, gpio44, gpio45, gpio46, gpio47]
          - if:
              properties:
                function:
                  const: phy1_led0
            then:
              properties:
                groups:
                  enum: [gpio33, gpio34, gpio35, gpio42]
          - if:
              properties:
                function:
                  const: phy2_led0
            then:
              properties:
                groups:
                  enum: [gpio33, gpio34, gpio35, gpio42]
          - if:
              properties:
                function:
                  const: phy3_led0
            then:
              properties:
                groups:
                  enum: [gpio33, gpio34, gpio35, gpio42]
          - if:
              properties:
                function:
                  const: phy4_led0
            then:
              properties:
                groups:
                  enum: [gpio33, gpio34, gpio35, gpio42]
          - if:
              properties:
                function:
                  const: phy1_led1
            then:
              properties:
                groups:
                  enum: [gpio43, gpio44, gpio45, gpio46]
          - if:
              properties:
                function:
                  const: phy2_led1
            then:
              properties:
                groups:
                  enum: [gpio43, gpio44, gpio45, gpio46]
          - if:
              properties:
                function:
                  const: phy3_led1
            then:
              properties:
                groups:
                  enum: [gpio43, gpio44, gpio45, gpio46]
          - if:
              properties:
                function:
                  const: phy4_led1
            then:
              properties:
                groups:
                  enum: [gpio43, gpio44, gpio45, gpio46]

        additionalProperties: false

      '^conf(-|$)':
        type: object

        description:
          pinconf configuration nodes.

        $ref: /schemas/pinctrl/pincfg-node.yaml

        properties:
          pins:
            description:
              An array of strings. Each string contains the name of a pin.
            items:
              enum: [uart1_txd, uart1_rxd, i2c_scl, i2c_sda, spi_cs0, spi_clk,
                     spi_mosi, spi_miso, gpio0, gpio1, gpio2, gpio3, gpio4,
                     gpio5, gpio6, gpio7, gpio8, gpio9, gpio10, gpio11, gpio12,
                     gpio13, gpio14, gpio15, gpio16, gpio17, gpio18, gpio19,
                     gpio20, gpio21, gpio22, gpio23, gpio24, gpio25, gpio26,
                     gpio27, gpio28, gpio29, gpio30, gpio31, gpio32, gpio33,
                     gpio34, gpio35, gpio36, gpio37, gpio38, gpio39, gpio40,
                     gpio41, gpio42, gpio43, gpio44, gpio45, gpio46,
                     pcie_reset0, pcie_reset1, pcie_reset2]
            minItems: 1
            maxItems: 58

          bias-disable: true

          bias-pull-up: true

          bias-pull-down: true

          input-enable: true

          output-enable: true

          output-low: true

          output-high: true

          drive-open-drain: true

          drive-strength:
            description:
              Selects the drive strength for MIO pins, in mA.
            enum: [2, 4, 6, 8]

        required:
          - pins

        additionalProperties: false

    additionalProperties: false

additionalProperties: false

examples:
  - |
    #include <dt-bindings/interrupt-controller/arm-gic.h>

    pinctrl {
      compatible = "airoha,en7581-pinctrl";

      interrupt-parent = <&gic>;
      interrupts = <GIC_SPI 26 IRQ_TYPE_LEVEL_HIGH>;

      gpio-controller;
      #gpio-cells = <2>;

      interrupt-controller;
      #interrupt-cells = <2>;

      pcie1-rst-pins {
        conf {
          pins = "pcie_reset1";
          drive-open-drain = <1>;
        };
      };

      pwm-pins {
        mux {
          function = "pwm";
          groups = "gpio18";
        };
      };

      spi-pins {
        mux {
          function = "spi";
          groups = "spi_quad", "spi_cs1";
        };
      };

      uart2-pins {
        mux {
          function = "uart";
          groups = "uart2", "uart2_cts_rts";
        };
      };

      uar5-pins {
        mux {
          function = "uart";
          groups = "uart5";
        };
      };

      mmc-pins {
        mux {
          function = "emmc";
          groups = "emmc";
        };
      };

      mdio-pins {
        mux {
          function = "mdio";
          groups = "mdio";
        };

        conf {
          pins = "gpio2";
          output-enable;
        };
      };
    };
