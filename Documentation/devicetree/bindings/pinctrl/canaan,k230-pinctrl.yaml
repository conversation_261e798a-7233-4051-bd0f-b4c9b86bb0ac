# SPDX-License-Identifier: GPL-2.0-only OR BSD-2-Clause
%YAML 1.2
---
$id: http://devicetree.org/schemas/pinctrl/canaan,k230-pinctrl.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: Canaan Kendryte K230 Pin Controller

maintainers:
  - Z<PERSON> Huang <<EMAIL>>

description:
  The Canaan Kendryte K230 platform includes 64 IO pins, each capable of
  multiplexing up to 5 different functions. Pin function configuration is
  performed on a per-pin basis.

properties:
  compatible:
    const: canaan,k230-pinctrl

  reg:
    maxItems: 1

patternProperties:
  '-pins$':
    type: object
    additionalProperties: false
    description:
      A pinctrl node should contain at least one subnode representing the
      pinctrl groups available on the machine.

    patternProperties:
      '-cfg$':
        type: object
        allOf:
          - $ref: /schemas/pinctrl/pincfg-node.yaml
          - $ref: /schemas/pinctrl/pinmux-node.yaml
        additionalProperties: false
        description:
          Each subnode will list the pins it needs, and how they should
          be configured, with regard to muxer configuration, bias, input
          enable/disable, input schmitt trigger, slew-rate enable/disable,
          slew-rate, drive strength.

        properties:
          pinmux:
            description:
              The list of GPIOs and their mux settings that properties in
              the node apply to. This should be set with the macro
              'K230_PINMUX(pin, mode)'

          bias-disable: true

          bias-pull-up: true

          bias-pull-down: true

          drive-strength:
            minimum: 0
            maximum: 15

          input-enable: true

          output-enable: true

          input-schmitt-enable: true

          slew-rate:
            description: |
              slew rate control enable
              0: disable
              1: enable

            enum: [0, 1]

          power-source:
            description: |
              Specifies the power source voltage for the IO bank that the
              pin belongs to. Each bank of IO pins operate at a specific,
              fixed voltage levels. Incorrect voltage configuration can
              damage the chip. The defined constants represent the
              possible voltage configurations:

              - K230_MSC_3V3 (value 0): 3.3V power supply
              - K230_MSC_1V8 (value 1): 1.8V power supply

              The following banks have the corresponding voltage
              configurations:

              - bank IO0 to IO1: Fixed at 1.8V
              - bank IO2 to IO13: Fixed at 1.8V
              - bank IO14 to IO25: Fixed at 1.8V
              - bank IO26 to IO37: Fixed at 1.8V
              - bank IO38 to IO49: Fixed at 1.8V
              - bank IO50 to IO61: Fixed at 3.3V
              - bank IO62 to IO63: Fixed at 1.8V

            enum: [0, 1]

        required:
          - pinmux

required:
  - compatible
  - reg

additionalProperties: false

examples:
  - |
    pinctrl@******** {
        compatible = "canaan,k230-pinctrl";
        reg = <0x******** 0x100>;

        uart2-pins {
            uart2-pins-cfg {
                pinmux = <0x503>, /* uart2 txd */
                         <0x603>; /* uart2 rxd */
                slew-rate = <0>;
                drive-strength = <4>;
                power-source = <1>;
                input-enable;
                output-enable;
                bias-disable;
            };
        };
    };
