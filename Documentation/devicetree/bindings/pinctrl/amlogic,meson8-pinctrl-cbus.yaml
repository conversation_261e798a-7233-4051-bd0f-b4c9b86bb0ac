# SPDX-License-Identifier: (GPL-2.0-only OR BSD-2-Clause)
%YAML 1.2
---
$id: http://devicetree.org/schemas/pinctrl/amlogic,meson8-pinctrl-cbus.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: Amlogic Meson8 CBUS pinmux controller

maintainers:
  - <PERSON> <<EMAIL>>

allOf:
  - $ref: amlogic,meson-pinctrl-common.yaml#

properties:
  compatible:
    oneOf:
      - enum:
          - amlogic,meson8-cbus-pinctrl
          - amlogic,meson8b-cbus-pinctrl
          - amlogic,meson-gxbb-periphs-pinctrl
          - amlogic,meson-gxl-periphs-pinctrl
          - amlogic,meson-axg-periphs-pinctrl
      - items:
          - const: amlogic,meson8m2-cbus-pinctrl
          - const: amlogic,meson8-cbus-pinctrl

required:
  - compatible

patternProperties:
  "^bank@[0-9a-f]+$":
    $ref: amlogic,meson-pinctrl-common.yaml#/$defs/meson-gpio

    unevaluatedProperties: false

    properties:
      reg:
        maxItems: 4

      reg-names:
        items:
          - const: mux
          - const: pull
          - const: pull-enable
          - const: gpio

      gpio-line-names:
        minItems: 83 # Meson8b
        maxItems: 120 # Meson8

unevaluatedProperties:
  type: object
  $ref: amlogic,meson-pinctrl-common.yaml#/$defs/meson-pins

examples:
  - |
    pinctrl_cbus: pinctrl {
      compatible = "amlogic,meson8-cbus-pinctrl";
      #address-cells = <1>;
      #size-cells = <1>;
      ranges;

      bank@80b0 {
        reg = <0x80b0 0x28>,
              <0x80e8 0x18>,
              <0x8120 0x18>,
              <0x8030 0x30>;
        reg-names = "mux", "pull", "pull-enable", "gpio";
        gpio-controller;
        #gpio-cells = <2>;
        gpio-ranges = <&pinctrl_cbus 0 0 120>;
      };

      cec_ao_a_h_pins: cec_ao_a_h {
        mux {
          groups = "cec_ao_a_h";
          function = "cec_ao_a_h";
          bias-disable;
        };
      };
    };
