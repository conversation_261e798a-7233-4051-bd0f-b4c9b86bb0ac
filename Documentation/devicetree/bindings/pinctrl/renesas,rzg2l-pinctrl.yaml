# SPDX-License-Identifier: (GPL-2.0-only OR BSD-2-Clause)
%YAML 1.2
---
$id: http://devicetree.org/schemas/pinctrl/renesas,rzg2l-pinctrl.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: Renesas RZ/{G2L,V2L} combined Pin and GPIO controller

maintainers:
  - <PERSON><PERSON> <<EMAIL>>
  - La<PERSON> <<EMAIL>>

description:
  The Renesas SoCs of the RZ/{G2L,V2L} alike series feature a combined Pin and
  GPIO controller.
  Pin multiplexing and GPIO configuration is performed on a per-pin basis.
  Each port features up to 8 pins, each of them configurable for GPIO function
  (port mode) or in alternate function mode.
  Up to 8 different alternate function modes exist for each single pin.

properties:
  compatible:
    oneOf:
      - items:
          - enum:
              - renesas,r9a07g043-pinctrl # RZ/G2UL{Type-1,Type-2} and RZ/Five
              - renesas,r9a07g044-pinctrl # RZ/G2{L,LC}
              - renesas,r9a08g045-pinctrl # RZ/G3S
              - renesas,r9a09g047-pinctrl # RZ/G3E
              - renesas,r9a09g056-pinctrl # RZ/V2N
              - renesas,r9a09g057-pinctrl # RZ/V2H(P)

      - items:
          - enum:
              - renesas,r9a07g054-pinctrl     # RZ/V2L
          - const: renesas,r9a07g044-pinctrl  # RZ/G2{L,LC} fallback for RZ/V2L

  reg:
    maxItems: 1

  gpio-controller: true

  '#gpio-cells':
    const: 2
    description:
      The first cell contains the global GPIO port index, constructed using the
      RZG2L_GPIO() helper macro in <dt-bindings/pinctrl/rzg2l-pinctrl.h> and the
      second cell represents consumer flag as mentioned in ../gpio/gpio.txt
      E.g. "RZG2L_GPIO(39, 1)" for P39_1.

  gpio-ranges:
    maxItems: 1

  interrupt-controller: true

  '#interrupt-cells':
    const: 2
    description:
      The first cell contains the global GPIO port index, constructed using the
      RZG2L_GPIO() helper macro in <dt-bindings/pinctrl/rzg2l-pinctrl.h> and the
      second cell is used to specify the flag.
      E.g. "interrupts = <RZG2L_GPIO(43, 0) IRQ_TYPE_EDGE_FALLING>;" if P43_0 is
      being used as an interrupt.

  clocks:
    maxItems: 1

  power-domains:
    maxItems: 1

  resets:
    oneOf:
      - items:
          - description: GPIO_RSTN signal
          - description: GPIO_PORT_RESETN signal
          - description: GPIO_SPARE_RESETN signal
      - items:
          - description: PFC main reset
          - description: Reset for the control register related to WDTUDFCA and WDTUDFFCM pins

additionalProperties:
  anyOf:
    - type: object
      additionalProperties: false
      allOf:
        - $ref: pincfg-node.yaml#
        - $ref: pinmux-node.yaml#

      description:
        Pin controller client devices use pin configuration subnodes (children
        and grandchildren) for desired pin configuration.
        Client device subnodes use below standard properties.

      properties:
        pinmux:
          description:
            Values are constructed from GPIO port number, pin number, and
            alternate function configuration number using the RZG2L_PORT_PINMUX()
            helper macro in <dt-bindings/pinctrl/rzg2l-pinctrl.h>.
        pins: true
        drive-strength:
          enum: [ 2, 4, 8, 12 ]
        drive-strength-microamp:
          enum: [ 1900, 2200, 4000, 4400, 4500, 4700, 5200, 5300, 5700,
                  5800, 6000, 6050, 6100, 6550, 6800, 7000, 8000, 9000,
                  10000 ]
        output-impedance-ohms:
          enum: [ 33, 50, 66, 100 ]
        power-source:
          description: I/O voltage in millivolt.
          enum: [ 1800, 2500, 3300 ]
        slew-rate: true
        gpio-hog: true
        gpios: true
        input: true
        input-enable: true
        output-enable: true
        output-high: true
        output-low: true
        line-name: true
        bias-disable: true
        bias-pull-down: true
        bias-pull-up: true
        input-schmitt-enable: true
        input-schmitt-disable: true
        drive-open-drain: true
        drive-push-pull: true
        renesas,output-impedance:
          description:
            Output impedance for pins on the RZ/{G3E,V2H(P)} SoC. The value provided by this
            property corresponds to register bit values that can be set in the PFC_IOLH_mn
            register, which adjusts the drive strength value and is pin-dependent.
          $ref: /schemas/types.yaml#/definitions/uint32
          enum: [0, 1, 2, 3]

    - type: object
      additionalProperties:
        $ref: "#/additionalProperties/anyOf/0"

allOf:
  - $ref: pinctrl.yaml#

  - if:
      properties:
        compatible:
          contains:
            enum:
              - renesas,r9a09g047-pinctrl
              - renesas,r9a09g056-pinctrl
              - renesas,r9a09g057-pinctrl
    then:
      properties:
        resets:
          maxItems: 2
    else:
      properties:
        resets:
          minItems: 3

required:
  - compatible
  - reg
  - gpio-controller
  - '#gpio-cells'
  - gpio-ranges
  - interrupt-controller
  - '#interrupt-cells'
  - clocks
  - power-domains
  - resets

examples:
  - |
    #include <dt-bindings/pinctrl/rzg2l-pinctrl.h>
    #include <dt-bindings/clock/r9a07g044-cpg.h>

    pinctrl: pinctrl@11030000 {
            compatible = "renesas,r9a07g044-pinctrl";
            reg = <0x11030000 0x10000>;

            gpio-controller;
            #gpio-cells = <2>;
            gpio-ranges = <&pinctrl 0 0 392>;
            interrupt-controller;
            #interrupt-cells = <2>;
            clocks = <&cpg CPG_MOD R9A07G044_GPIO_HCLK>;
            resets = <&cpg R9A07G044_GPIO_RSTN>,
                     <&cpg R9A07G044_GPIO_PORT_RESETN>,
                     <&cpg R9A07G044_GPIO_SPARE_RESETN>;
            power-domains = <&cpg>;

            scif0_pins: serial0 {
                    pinmux = <RZG2L_PORT_PINMUX(38, 0, 1)>, /* Tx */
                             <RZG2L_PORT_PINMUX(38, 1, 1)>; /* Rx */
            };

            i2c1_pins: i2c1 {
                    pins = "RIIC1_SDA", "RIIC1_SCL";
                    input-enable;
            };

            sd1-pwr-en-hog {
                    gpio-hog;
                    gpios = <RZG2L_GPIO(39, 2) 0>;
                    output-high;
                    line-name = "sd1_pwr_en";
            };

            sdhi1_pins: sd1 {
                    sd1_mux {
                            pinmux = <RZG2L_PORT_PINMUX(19, 0, 1)>, /* CD */
                                     <RZG2L_PORT_PINMUX(19, 1, 1)>; /* WP */
                            power-source = <3300>;
                    };

                    sd1_data {
                            pins = "SD1_DATA0", "SD1_DATA1", "SD1_DATA2", "SD1_DATA3";
                            power-source = <3300>;
                    };

                    sd1_ctrl {
                            pins = "SD1_CLK", "SD1_CMD";
                            power-source = <3300>;
                    };
            };
    };
