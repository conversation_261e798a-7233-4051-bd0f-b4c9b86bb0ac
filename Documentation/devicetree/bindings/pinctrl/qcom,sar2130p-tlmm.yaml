# SPDX-License-Identifier: (GPL-2.0-only OR BSD-2-Clause)
%YAML 1.2
---
$id: http://devicetree.org/schemas/pinctrl/qcom,sar2130p-tlmm.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: Qualcomm Technologies, Inc. SAR2130P TLMM block

maintainers:
  - <PERSON> <dmitry.bar<PERSON>h<PERSON>@linaro.org>

description:
  Top Level Mode Multiplexer pin controller in Qualcomm SAR2130P SoC.

allOf:
  - $ref: /schemas/pinctrl/qcom,tlmm-common.yaml#

properties:
  compatible:
    const: qcom,sar2130p-tlmm

  reg:
    maxItems: 1

  interrupts:
    maxItems: 1

  gpio-reserved-ranges:
    minItems: 1
    maxItems: 78

  gpio-line-names:
    maxItems: 156

patternProperties:
  "-state$":
    oneOf:
      - $ref: "#/$defs/qcom-sar2130p-tlmm-state"
      - patternProperties:
          "-pins$":
            $ref: "#/$defs/qcom-sar2130p-tlmm-state"
        additionalProperties: false

$defs:
  qcom-sar2130p-tlmm-state:
    type: object
    description:
      Pinctrl node's client devices use subnodes for desired pin configuration.
      Client device subnodes use below standard properties.
    $ref: qcom,tlmm-common.yaml#/$defs/qcom-tlmm-state
    unevaluatedProperties: false

    properties:
      pins:
        description:
          List of gpio pins affected by the properties specified in this
          subnode.
        items:
          oneOf:
            - pattern: "^gpio([0-9]|[1-9][0-9]|1[0-4][0-9]|15[0-5])$"
            - enum: [ sdc1_clk, sdc1_cmd, sdc1_data, sdc1_rclk ]
        minItems: 1
        maxItems: 36

      function:
        description:
          Specify the alternative function to be configured for the specified
          pins.
        enum: [ aoss_cti, atest_char, atest_char0, atest_char1, atest_char2,
                atest_char3, atest_usb0, atest_usb00, atest_usb01, atest_usb02,
                atest_usb03, audio_ref, cam_mclk, cci_async, cci_i2c,
                cci_timer0, cci_timer1, cci_timer2, cci_timer3, cci_timer4,
                cri_trng, cri_trng0, cri_trng1, dbg_out, ddr_bist, ddr_pxi0,
                ddr_pxi1, ddr_pxi2, ddr_pxi3, dp0_hot, ext_mclk0, ext_mclk1,
                gcc_gp1, gcc_gp2, gcc_gp3, gpio, host2wlan_sol, i2s0_data0,
                i2s0_data1, i2s0_sck, i2s0_ws, ibi_i3c, jitter_bist, mdp_vsync,
                mdp_vsync0, mdp_vsync1, mdp_vsync2, mdp_vsync3, pcie0_clkreqn,
                pcie1_clkreqn, phase_flag0, phase_flag1, phase_flag10,
                phase_flag11, phase_flag12, phase_flag13, phase_flag14,
                phase_flag15, phase_flag16, phase_flag17, phase_flag18,
                phase_flag19, phase_flag2, phase_flag20, phase_flag21,
                phase_flag22, phase_flag23, phase_flag24, phase_flag25,
                phase_flag26, phase_flag27, phase_flag28, phase_flag29,
                phase_flag3, phase_flag30, phase_flag31, phase_flag4,
                phase_flag5, phase_flag6, phase_flag7, phase_flag8,
                phase_flag9, pll_bist, pll_clk, prng_rosc0, prng_rosc1,
                prng_rosc2, prng_rosc3, qdss_cti, qdss_gpio, qdss_gpio0,
                qdss_gpio1, qdss_gpio10, qdss_gpio11, qdss_gpio12, qdss_gpio13,
                qdss_gpio14, qdss_gpio15, qdss_gpio2, qdss_gpio3, qdss_gpio4,
                qdss_gpio5, qdss_gpio6, qdss_gpio7, qdss_gpio8, qdss_gpio9,
                qspi0, qspi1, qspi2, qspi3, qspi_clk, qspi_cs0, qspi_cs1, qup0,
                qup1, qup2, qup3, qup4, qup5, qup6, qup7, qup8, qup9, qup10,
                qup11, tb_trig, tgu_ch0, tgu_ch1, tgu_ch2, tgu_ch3,
                tmess_prng0, tmess_prng1, tmess_prng2, tmess_prng3,
                tsense_pwm1, tsense_pwm2, usb0_phy, vsense_trigger ]

    required:
      - pins

required:
  - compatible
  - reg

unevaluatedProperties: false

examples:
  - |
    #include <dt-bindings/interrupt-controller/arm-gic.h>
    pinctrl@f100000 {
        compatible = "qcom,sar2130p-tlmm";
        reg = <0x0f100000 0x300000>;
        gpio-controller;
        #gpio-cells = <2>;
        gpio-ranges = <&tlmm 0 0 156>;
        interrupt-controller;
        #interrupt-cells = <2>;
        interrupts = <GIC_SPI 208 IRQ_TYPE_LEVEL_HIGH>;

        gpio-wo-state {
            pins = "gpio1";
            function = "gpio";
        };

        uart-w-state {
            rx-pins {
                pins = "gpio26";
                function = "qup7";
                bias-pull-up;
            };

            tx-pins {
                pins = "gpio27";
                function = "qup7";
                bias-disable;
            };
        };
    };
...
