# SPDX-License-Identifier: (GPL-2.0-only OR BSD-2-Clause)
%YAML 1.2
---
$id: http://devicetree.org/schemas/pinctrl/qcom,ipq5424-tlmm.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: Qualcomm IPQ5424 TLMM pin controller

maintainers:
  - <PERSON><PERSON><PERSON> <and<PERSON><PERSON>@kernel.org>

description:
  Top Level Mode Multiplexer pin controller in Qualcomm IPQ5424 SoC.

allOf:
  - $ref: /schemas/pinctrl/qcom,tlmm-common.yaml#

properties:
  compatible:
    const: qcom,ipq5424-tlmm

  reg:
    maxItems: 1

  interrupts:
    maxItems: 1

  gpio-reserved-ranges:
    minItems: 1
    maxItems: 25

  gpio-line-names:
    maxItems: 50

patternProperties:
  "-state$":
    oneOf:
      - $ref: "#/$defs/qcom-ipq5424-tlmm-state"
      - patternProperties:
          "-pins$":
            $ref: "#/$defs/qcom-ipq5424-tlmm-state"
        additionalProperties: false

$defs:
  qcom-ipq5424-tlmm-state:
    type: object
    description:
      Pinctrl node's client devices use subnodes for desired pin configuration.
      Client device subnodes use below standard properties.
    $ref: qcom,tlmm-common.yaml#/$defs/qcom-tlmm-state
    unevaluatedProperties: false

    properties:
      pins:
        description:
          List of gpio pins affected by the properties specified in this
          subnode.
        items:
          pattern: "^gpio([0-9]|[1-4][0-9])$"
        minItems: 1
        maxItems: 50

      function:
        description:
          Specify the alternative function to be configured for the specified
          pins.

        enum: [ atest_char, atest_char0, atest_char1, atest_char2, atest_char3,
                atest_tic, audio_pri, audio_pri0, audio_pri1, audio_sec,
                audio_sec0, audio_sec1, core_voltage, cri_trng0, cri_trng1,
                cri_trng2, cri_trng3, cxc_clk, cxc_data, dbg_out, gcc_plltest,
                gcc_tlmm, gpio, i2c0_scl, i2c0_sda, i2c1_scl, i2c1_sda, i2c11,
                mac0, mac1, mdc_mst, mdc_slv, mdio_mst, mdio_slv, pcie0_clk,
                pcie0_wake, pcie1_clk, pcie1_wake, pcie2_clk, pcie2_wake,
                pcie3_clk, pcie3_wake, pll_test, prng_rosc0, prng_rosc1,
                prng_rosc2, prng_rosc3, PTA0_0, PTA0_1, PTA0_2, PTA10, PTA11,
                pwm0, pwm1, pwm2, qdss_cti_trig_in_a0, qdss_cti_trig_out_a0,
                qdss_cti_trig_in_a1, qdss_cti_trig_out_a1, qdss_cti_trig_in_b0,
                qdss_cti_trig_out_b0, qdss_cti_trig_in_b1, qdss_cti_trig_out_b1,
                qdss_traceclk_a, qdss_tracectl_a, qdss_tracedata_a, qspi_clk,
                qspi_cs, qspi_data, resout, rx0, rx1, rx2, sdc_clk, sdc_cmd,
                sdc_data, spi0_cs, spi0_clk, spi0_miso, spi0_mosi, spi1, spi10,
                spi11, tsens_max, uart0, uart1, wci_txd, wci_rxd, wsi_clk, wsi_data ]

    required:
      - pins

required:
  - compatible
  - reg

unevaluatedProperties: false

examples:
  - |
    #include <dt-bindings/interrupt-controller/arm-gic.h>

    tlmm: pinctrl@1000000 {
        compatible = "qcom,ipq5424-tlmm";
        reg = <0x01000000 0x300000>;
        gpio-controller;
        #gpio-cells = <0x2>;
        gpio-ranges = <&tlmm 0 0 50>;
        interrupts = <GIC_SPI 84 IRQ_TYPE_LEVEL_HIGH>;
        interrupt-controller;
        #interrupt-cells = <0x2>;

        uart1_pins: uart1-state {
            pins = "gpio43", "gpio44";
            function = "uart1";
            drive-strength = <8>;
            bias-pull-up;
        };
    };
