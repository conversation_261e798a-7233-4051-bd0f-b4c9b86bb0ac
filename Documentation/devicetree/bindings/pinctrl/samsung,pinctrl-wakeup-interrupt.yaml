# SPDX-License-Identifier: (GPL-2.0-only OR BSD-2-Clause)
%YAML 1.2
---
$id: http://devicetree.org/schemas/pinctrl/samsung,pinctrl-wakeup-interrupt.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: Samsung S3C/S5P/Exynos SoC pin controller - wake-up interrupt controller

maintainers:
  - <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> <<EMAIL>>
  - <PERSON><PERSON><PERSON><PERSON> Nawrock<PERSON> <<EMAIL>>
  - <PERSON><PERSON> <<EMAIL>>

description: |
  This is a part of device tree bindings for Samsung S3C/S5P/Exynos SoC pin
  controller.

  External wake-up interrupts for Samsung S3C/S5P/Exynos SoC pin controller.
  For S3C24xx, S3C64xx, S5PV210 and Exynos4210 compatible wake-up interrupt
  controllers, only one pin-controller device node can include external wake-up
  interrupts child node (in other words, only one External wake-up interrupts
  pin-controller is supported).
  For newer controllers, multiple pin-controller device node can include
  external wake-up interrupts child node.

  See also Documentation/devicetree/bindings/pinctrl/samsung,pinctrl.yaml for
  additional information and example.

properties:
  compatible:
    oneOf:
      - enum:
          - samsung,s3c2410-wakeup-eint
          - samsung,s3c2412-wakeup-eint
          - samsung,s3c64xx-wakeup-eint
          - samsung,s5pv210-wakeup-eint
          - samsung,exynos4210-wakeup-eint
          - samsung,exynos7-wakeup-eint
          - samsung,exynosautov920-wakeup-eint
      - items:
          - enum:
              - samsung,exynos5433-wakeup-eint
              - samsung,exynos7870-wakeup-eint
              - samsung,exynos7885-wakeup-eint
              - samsung,exynos850-wakeup-eint
              - samsung,exynos8895-wakeup-eint
          - const: samsung,exynos7-wakeup-eint
      - items:
          - enum:
              - google,gs101-wakeup-eint
              - samsung,exynos2200-wakeup-eint
              - samsung,exynos9810-wakeup-eint
              - samsung,exynos990-wakeup-eint
              - samsung,exynosautov9-wakeup-eint
          - const: samsung,exynos850-wakeup-eint
          - const: samsung,exynos7-wakeup-eint

  interrupts:
    description:
      Interrupt used by multiplexed external wake-up interrupts.
    minItems: 1
    maxItems: 6

required:
  - compatible

allOf:
  - if:
      properties:
        compatible:
          contains:
            enum:
              - samsung,s3c2410-wakeup-eint
              - samsung,s3c2412-wakeup-eint
    then:
      properties:
        interrupts:
          minItems: 6
          maxItems: 6
      required:
        - interrupts

  - if:
      properties:
        compatible:
          contains:
            const: samsung,s3c64xx-wakeup-eint
    then:
      properties:
        interrupts:
          minItems: 4
          maxItems: 4
      required:
        - interrupts

  - if:
      properties:
        compatible:
          oneOf:
            # Match without "contains", to skip newer variants which are still
            # compatible with samsung,exynos7-wakeup-eint
            - enum:
                - samsung,exynos4210-wakeup-eint
                - samsung,exynos7-wakeup-eint
                - samsung,s5pv210-wakeup-eint
            - contains:
                enum:
                  - samsung,exynos5433-wakeup-eint
                  - samsung,exynos7870-wakeup-eint
                  - samsung,exynos7885-wakeup-eint
                  - samsung,exynos8895-wakeup-eint
    then:
      properties:
        interrupts:
          minItems: 1
          maxItems: 1
      required:
        - interrupts

  - if:
      properties:
        compatible:
          contains:
            enum:
              - samsung,exynos850-wakeup-eint
              - samsung,exynosautov920-wakeup-eint
    then:
      properties:
        interrupts: false

additionalProperties: false
