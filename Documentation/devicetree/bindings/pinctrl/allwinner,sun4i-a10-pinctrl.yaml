# SPDX-License-Identifier: GPL-2.0
%YAML 1.2
---
$id: http://devicetree.org/schemas/pinctrl/allwinner,sun4i-a10-pinctrl.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: Allwinner A10 Pin Controller

maintainers:
  - <PERSON><PERSON><PERSON> <<EMAIL>>
  - <PERSON><PERSON> <<EMAIL>>

properties:
  "#gpio-cells":
    const: 3
    description:
      GPIO consumers must use three arguments, first the number of the
      bank, then the pin number inside that bank, and finally the GPIO
      flags.

  "#interrupt-cells":
    const: 3
    description:
      Interrupts consumers must use three arguments, first the number
      of the bank, then the pin number inside that bank, and finally
      the interrupts flags.

  compatible:
    enum:
      - allwinner,sun4i-a10-pinctrl
      - allwinner,sun5i-a10s-pinctrl
      - allwinner,sun5i-a13-pinctrl
      - allwinner,sun6i-a31-pinctrl
      - allwinner,sun6i-a31-r-pinctrl
      - allwinner,sun6i-a31s-pinctrl
      - allwinner,sun7i-a20-pinctrl
      - allwinner,sun8i-a23-pinctrl
      - allwinner,sun8i-a23-r-pinctrl
      - allwinner,sun8i-a33-pinctrl
      - allwinner,sun8i-a83t-pinctrl
      - allwinner,sun8i-a83t-r-pinctrl
      - allwinner,sun8i-h3-pinctrl
      - allwinner,sun8i-h3-r-pinctrl
      - allwinner,sun8i-r40-pinctrl
      - allwinner,sun8i-v3-pinctrl
      - allwinner,sun8i-v3s-pinctrl
      - allwinner,sun8i-v853-pinctrl
      - allwinner,sun9i-a80-pinctrl
      - allwinner,sun9i-a80-r-pinctrl
      - allwinner,sun20i-d1-pinctrl
      - allwinner,sun50i-a64-pinctrl
      - allwinner,sun50i-a64-r-pinctrl
      - allwinner,sun50i-a100-pinctrl
      - allwinner,sun50i-a100-r-pinctrl
      - allwinner,sun50i-h5-pinctrl
      - allwinner,sun50i-h6-pinctrl
      - allwinner,sun50i-h6-r-pinctrl
      - allwinner,sun50i-h616-pinctrl
      - allwinner,sun50i-h616-r-pinctrl
      - allwinner,suniv-f1c100s-pinctrl
      - nextthing,gr8-pinctrl

  reg:
    maxItems: 1

  interrupts:
    minItems: 1
    maxItems: 8
    description:
      One interrupt per external interrupt bank supported on the
      controller, sorted by bank number ascending order.

  clocks:
    items:
      - description: Bus Clock
      - description: High Frequency Oscillator
      - description: Low Frequency Oscillator

  clock-names:
    items:
      - const: apb
      - const: hosc
      - const: losc

  gpio-controller: true
  interrupt-controller: true
  gpio-line-names: true

  input-debounce:
    description:
      Debouncing periods in microseconds, one period per interrupt
      bank found in the controller
    $ref: /schemas/types.yaml#/definitions/uint32-array
    minItems: 1
    maxItems: 8

patternProperties:
  # It's pretty scary, but the basic idea is that:
  #   - One node name can start with either s- or r- for PRCM nodes,
  #   - Then, the name itself can be any repetition of <string>- (to
  #     accommodate with nodes like uart4-rts-cts-pins), where each
  #     string can be either starting with 'p' but in a string longer
  #     than 3, or something that doesn't start with 'p',
  #   - Then, the bank name is optional and will be between pa and pg,
  #     pl or pm. Some pins groups that have several options will have
  #     the pin numbers then,
  #   - Finally, the name will end with either -pin or pins.

  "^([rs]-)?(([a-z0-9]{3,}|[a-oq-z][a-z0-9]*?)?-)+?(p[a-ilm][0-9]*?-)??pins?$":
    type: object

    properties:
      pins: true
      function: true
      bias-disable: true
      bias-pull-up: true
      bias-pull-down: true

      drive-strength:
        $ref: /schemas/types.yaml#/definitions/uint32
        enum: [10, 20, 30, 40]

    required:
      - pins
      - function

    additionalProperties: false

  "^vcc-p[a-ilm]-supply$":
    description:
      Power supplies for pin banks.

required:
  - "#gpio-cells"
  - compatible
  - reg
  - clocks
  - clock-names
  - gpio-controller

allOf:
  # FIXME: We should have the pin bank supplies here, but not a lot of
  # boards are defining it at the moment so it would generate a lot of
  # warnings.

  - $ref: pinctrl.yaml#
  - if:
      not:
        properties:
          compatible:
            enum:
              - allwinner,sun50i-h616-r-pinctrl

    then:
      required:
        - "#interrupt-cells"
        - interrupts
        - interrupt-controller

  - if:
      properties:
        compatible:
          enum:
            - allwinner,sun50i-h616-pinctrl

    then:
      properties:
        interrupts:
          minItems: 8

  - if:
      properties:
        compatible:
          enum:
            - allwinner,sun50i-a100-pinctrl

    then:
      properties:
        interrupts:
          minItems: 7
          maxItems: 7

  - if:
      properties:
        compatible:
          enum:
            - allwinner,sun8i-v853-pinctrl

    then:
      properties:
        interrupts:
          minItems: 8
          maxItems: 8

  - if:
      properties:
        compatible:
          enum:
            - allwinner,sun20i-d1-pinctrl

    then:
      properties:
        interrupts:
          minItems: 6
          maxItems: 6

  - if:
      properties:
        compatible:
          enum:
            - allwinner,sun9i-a80-pinctrl

    then:
      properties:
        interrupts:
          minItems: 5
          maxItems: 5

  - if:
      properties:
        compatible:
          enum:
            - allwinner,sun6i-a31-pinctrl
            - allwinner,sun6i-a31s-pinctrl
            - allwinner,sun50i-h6-pinctrl

    then:
      properties:
        interrupts:
          minItems: 4
          maxItems: 4

  - if:
      properties:
        compatible:
          enum:
            - allwinner,sun8i-a23-pinctrl
            - allwinner,sun8i-a83t-pinctrl
            - allwinner,sun50i-a64-pinctrl
            - allwinner,sun50i-h5-pinctrl
            - allwinner,suniv-f1c100s-pinctrl

    then:
      properties:
        interrupts:
          minItems: 3
          maxItems: 3

  - if:
      properties:
        compatible:
          enum:
            - allwinner,sun6i-a31-r-pinctrl
            - allwinner,sun8i-a33-pinctrl
            - allwinner,sun8i-h3-pinctrl
            - allwinner,sun8i-v3-pinctrl
            - allwinner,sun8i-v3s-pinctrl
            - allwinner,sun9i-a80-r-pinctrl
            - allwinner,sun50i-h6-r-pinctrl

    then:
      properties:
        interrupts:
          minItems: 2
          maxItems: 2

  - if:
      properties:
        compatible:
          enum:
            - allwinner,sun4i-a10-pinctrl
            - allwinner,sun5i-a10s-pinctrl
            - allwinner,sun5i-a13-pinctrl
            - allwinner,sun7i-a20-pinctrl
            - allwinner,sun8i-a23-r-pinctrl
            - allwinner,sun8i-a83t-r-pinctrl
            - allwinner,sun8i-h3-r-pinctrl
            - allwinner,sun8i-r40-pinctrl
            - allwinner,sun50i-a64-r-pinctrl
            - allwinner,sun50i-a100-r-pinctrl
            - nextthing,gr8-pinctrl

    then:
      properties:
        interrupts:
          minItems: 1
          maxItems: 1

additionalProperties: false

examples:
  - |
    #include <dt-bindings/clock/sun5i-ccu.h>

    pio: pinctrl@1c20800 {
        compatible = "allwinner,sun5i-a13-pinctrl";
        reg = <0x01c20800 0x400>;
        interrupts = <28>;
        clocks = <&ccu CLK_APB0_PIO>, <&osc24M>, <&osc32k>;
        clock-names = "apb", "hosc", "losc";
        gpio-controller;
        interrupt-controller;
        #interrupt-cells = <3>;
        #gpio-cells = <3>;

        uart1_pe_pins: uart1-pe-pins {
            pins = "PE10", "PE11";
            function = "uart1";
        };

        uart1_pg_pins: uart1-pg-pins {
            pins = "PG3", "PG4";
            function = "uart1";
        };
    };
