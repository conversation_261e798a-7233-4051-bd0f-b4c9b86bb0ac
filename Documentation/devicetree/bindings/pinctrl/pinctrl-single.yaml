# SPDX-License-Identifier: (GPL-2.0-only OR BSD-2-Clause)
%YAML 1.2
---
$id: http://devicetree.org/schemas/pinctrl/pinctrl-single.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: Generic Pin Controller with a Single Register for One or More Pins

maintainers:
  - <PERSON> <<EMAIL>>

description:
  Some pin controller devices use a single register for one or more pins. The
  range of pin control registers can vary from one to many for each controller
  instance. Some SoCs from Altera, Broadcom, HiSilicon, Ralink, and TI have this
  kind of pin controller instances.

properties:
  compatible:
    oneOf:
      - enum:
          - pinctrl-single
          - pinconf-single
      - items:
          - enum:
              - ti,am437-padconf
              - ti,am654-padconf
              - ti,dra7-padconf
              - ti,omap2420-padconf
              - ti,omap2430-padconf
              - ti,omap3-padconf
              - ti,omap4-padconf
              - ti,omap5-padconf
              - ti,j7200-padconf
          - const: pinctrl-single
      - items:
          - enum:
              - marvell,pxa1908-padconf
          - const: pinconf-single

  reg:
    maxItems: 1

  interrupt-controller: true

  '#interrupt-cells':
    const: 1

  '#address-cells':
    const: 1

  '#size-cells':
    const: 0

  '#pinctrl-cells':
    description:
      Number of cells. Usually 2, consisting of register offset, pin configuration
      value, and pinmux mode. Some controllers may use 1 for just offset and value.
    enum: [ 1, 2 ]

  pinctrl-single,bit-per-mux:
    description: Optional flag to indicate register controls more than one pin
    type: boolean

  pinctrl-single,function-mask:
    description: Mask of the allowed register bits
    $ref: /schemas/types.yaml#/definitions/uint32

  pinctrl-single,function-off:
    description: Optional function off mode for disabled state
    $ref: /schemas/types.yaml#/definitions/uint32

  pinctrl-single,register-width:
    description: Width of pin specific bits in the register
    $ref: /schemas/types.yaml#/definitions/uint32
    enum: [ 8, 16, 32 ]

  pinctrl-single,gpio-range:
    description: Optional list of pin base, nr pins & gpio function
    $ref: /schemas/types.yaml#/definitions/phandle-array
    items:
      items:
        - description: phandle of a gpio-range node
        - description: pin base
        - description: number of pins
        - description: gpio function

  '#gpio-range-cells':
    description: No longer needed, may exist in older files for gpio-ranges
    deprecated: true
    const: 3

  gpio-range:
    description: Optional node for gpio range cells
    type: object
    additionalProperties: false
    properties:
      '#pinctrl-single,gpio-range-cells':
        description: Number of gpio range cells
        const: 3
        $ref: /schemas/types.yaml#/definitions/uint32

patternProperties:
  '-pins(-[0-9]+)?$|-pin$':
    description:
      Pin group node name using naming ending in -pins followed by an optional
      instance number
    type: object
    additionalProperties: false

    properties:
      pinctrl-single,pins:
        description:
          Array of pins as described in pinmux-node.yaml for pinctrl-pin-array
        $ref: /schemas/types.yaml#/definitions/uint32-array

      pinctrl-single,bits:
        description: Register bit configuration for pinctrl-single,bit-per-mux
        $ref: /schemas/types.yaml#/definitions/uint32-array
        items:
          - description: register offset
          - description: value
          - description: pin bitmask in the register

      pinctrl-single,bias-pullup:
        description: Optional bias pull up configuration
        $ref: /schemas/types.yaml#/definitions/uint32-array
        items:
          - description: input
          - description: enabled pull up bits
          - description: disabled pull up bits
          - description: bias pull up mask

      pinctrl-single,bias-pulldown:
        description: Optional bias pull down configuration
        $ref: /schemas/types.yaml#/definitions/uint32-array
        items:
          - description: input
          - description: enabled pull down bits
          - description: disabled pull down bits
          - description: bias pull down mask

      pinctrl-single,drive-strength:
        description: Optional drive strength configuration
        $ref: /schemas/types.yaml#/definitions/uint32-array
        items:
          - description: drive strength current
          - description: drive strength mask

      pinctrl-single,input-schmitt:
        description: Optional schmitt strength configuration
        $ref: /schemas/types.yaml#/definitions/uint32-array
        items:
          - description: schmitt strength current
          - description: schmitt strength mask

      pinctrl-single,input-schmitt-enable:
        description: Optional input schmitt configuration
        $ref: /schemas/types.yaml#/definitions/uint32-array
        items:
          - description: input
          - description: enable bits
          - description: disable bits
          - description: input schmitt mask

      pinctrl-single,low-power-mode:
        description: Optional low power mode configuration
        $ref: /schemas/types.yaml#/definitions/uint32-array
        items:
          - description: low power mode value
          - description: low power mode mask

      pinctrl-single,slew-rate:
        description: Optional slew rate configuration
        $ref: /schemas/types.yaml#/definitions/uint32-array
        items:
          - description: slew rate
          - description: slew rate mask

allOf:
  - $ref: pinctrl.yaml#

required:
  - compatible
  - reg
  - pinctrl-single,register-width

additionalProperties: false

examples:
  - |
    soc {
      #address-cells = <1>;
      #size-cells = <1>;

      pinmux@4a100040 {
        compatible = "pinctrl-single";
        reg = <0x4a100040 0x0196>;
        #address-cells = <1>;
        #size-cells = <0>;
        #pinctrl-cells = <2>;
        #interrupt-cells = <1>;
        interrupt-controller;
        pinctrl-single,register-width = <16>;
        pinctrl-single,function-mask = <0xffff>;
        pinctrl-single,gpio-range = <&range 0 3 0>;
        range: gpio-range {
          #pinctrl-single,gpio-range-cells = <3>;
        };

        uart2-pins {
          pinctrl-single,pins =
            <0xd8 0x118>,
            <0xda 0>,
            <0xdc 0x118>,
            <0xde 0>;
        };
      };
    };
