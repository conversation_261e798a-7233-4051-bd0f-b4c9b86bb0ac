# SPDX-License-Identifier: (GPL-2.0-only OR BSD-2-Clause)
%YAML 1.2
---
$id: http://devicetree.org/schemas/pinctrl/amlogic,pinctrl-a4.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: Amlogic pinmux controller

maintainers:
  - <PERSON><PERSON><PERSON> <<EMAIL>>

allOf:
  - $ref: pinctrl.yaml#

properties:
  compatible:
    oneOf:
      - const: amlogic,pinctrl-a4
      - items:
          - enum:
              - amlogic,pinctrl-a5
          - const: amlogic,pinctrl-a4

  "#address-cells":
    const: 2

  "#size-cells":
    const: 2

  ranges: true

patternProperties:
  "^gpio@[0-9a-f]+$":
    type: object

    additionalProperties: false
    properties:
      reg:
        minItems: 1
        items:
          - description: pin config register
          - description: pin mux setting register (some special pin fixed function)
          - description: pin drive strength register (optional)

      reg-names:
        minItems: 1
        items:
          - const: gpio
          - const: mux
          - const: ds

      gpio-controller: true

      "#gpio-cells":
        const: 2

      gpio-ranges:
        maxItems: 1

    required:
      - reg
      - reg-names
      - gpio-controller
      - "#gpio-cells"
      - gpio-ranges

  "^func-[0-9a-z-]+$":
    type: object
    additionalProperties: false
    patternProperties:
      "^group-[0-9a-z-]+$":
        type: object
        unevaluatedProperties: false
        allOf:
          - $ref: /schemas/pinctrl/pincfg-node.yaml
          - $ref: /schemas/pinctrl/pinmux-node.yaml

        required:
          - pinmux

required:
  - compatible
  - "#address-cells"
  - "#size-cells"
  - ranges

additionalProperties: false

examples:
  - |
    #include <dt-bindings/pinctrl/amlogic,pinctrl.h>
    apb {
      #address-cells = <2>;
      #size-cells = <2>;
      periphs_pinctrl: pinctrl {
        compatible = "amlogic,pinctrl-a4";
        #address-cells = <2>;
        #size-cells = <2>;
        ranges;

        gpio@4240 {
          reg = <0 0x4240 0 0x40>, <0 0x4000 0 0x8>;
          reg-names = "gpio", "mux";
          gpio-controller;
          #gpio-cells = <2>;
          gpio-ranges = <&periphs_pinctrl 0 8 10>;
        };

        func-uart-b {
          group-default {
            pinmux = <AML_PINMUX(AMLOGIC_GPIO_B, 1, 4)>;
            bias-pull-up;
            drive-strength-microamp = <4000>;
          };

          group-pins1 {
            pinmux = <AML_PINMUX(AMLOGIC_GPIO_B, 5, 2)>;
            bias-pull-up;
            drive-strength-microamp = <4000>;
          };
        };

        func-uart-c {
          group-default {
            pinmux = <AML_PINMUX(AMLOGIC_GPIO_B, 3, 1)>,
                     <AML_PINMUX(AMLOGIC_GPIO_B, 2, 1)>;
            bias-pull-up;
            drive-strength-microamp = <4000>;
          };
        };
      };
    };
