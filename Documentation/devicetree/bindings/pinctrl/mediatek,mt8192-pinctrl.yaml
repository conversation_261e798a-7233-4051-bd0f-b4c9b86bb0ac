# SPDX-License-Identifier: (GPL-2.0-only OR BSD-2-Clause)
%YAML 1.2
---
$id: http://devicetree.org/schemas/pinctrl/mediatek,mt8192-pinctrl.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: MediaTek MT8192 Pin Controller

maintainers:
  - <PERSON> <<EMAIL>>

description:
  The MediaTek's MT8192 Pin controller is used to control SoC pins.

properties:
  compatible:
    const: mediatek,mt8192-pinctrl

  gpio-controller: true

  '#gpio-cells':
    description:
      Number of cells in GPIO specifier. Since the generic GPIO binding is used,
      the amount of cells must be specified as 2. See the below mentioned gpio
      binding representation for description of particular cells.
    const: 2

  gpio-ranges:
    description: GPIO valid number range.
    maxItems: 1

  gpio-line-names: true

  reg:
    description:
      Physical address base for GPIO base registers. There are 11 GPIO physical
      address base in mt8192.
    maxItems: 11

  reg-names:
    description:
      GPIO base register names.
    maxItems: 11

  interrupt-controller: true

  '#interrupt-cells':
    const: 2

  interrupts:
    description: The interrupt outputs to sysirq.
    maxItems: 1

# PIN CONFIGURATION NODES
patternProperties:
  '-pins$':
    type: object
    additionalProperties: false
    patternProperties:
      '^pins':
        type: object
        description:
          A pinctrl node should contain at least one subnodes representing the
          pinctrl groups available on the machine. Each subnode will list the
          pins it needs, and how they should be configured, with regard to muxer
          configuration, pullups, drive strength, input enable/disable and input
          schmitt.
        $ref: pinmux-node.yaml

        properties:
          pinmux:
            description:
              Integer array, represents gpio pin number and mux setting.
              Supported pin number and mux varies for different SoCs, and are
              defined as macros in dt-bindings/pinctrl/<soc>-pinfunc.h directly.

          drive-strength:
            description:
              It can support some arguments, such as MTK_DRIVE_4mA,
              MTK_DRIVE_6mA, etc. See dt-bindings/pinctrl/mt65xx.h. It can only
              support 2/4/6/8/10/12/14/16mA in mt8192.
            enum: [2, 4, 6, 8, 10, 12, 14, 16]

          drive-strength-microamp:
            enum: [125, 250, 500, 1000]

          bias-pull-down:
            oneOf:
              - type: boolean
                description: normal pull down.
              - enum: [100, 101, 102, 103]
                description: PUPD/R1/R0 pull down type. See MTK_PUPD_SET_R1R0_
                  defines in dt-bindings/pinctrl/mt65xx.h.
              - enum: [200, 201, 202, 203]
                description: RSEL pull down type. See MTK_PULL_SET_RSEL_ defines
                  in dt-bindings/pinctrl/mt65xx.h.

          bias-pull-up:
            oneOf:
              - type: boolean
                description: normal pull up.
              - enum: [100, 101, 102, 103]
                description: PUPD/R1/R0 pull up type. See MTK_PUPD_SET_R1R0_
                  defines in dt-bindings/pinctrl/mt65xx.h.
              - enum: [200, 201, 202, 203]
                description: RSEL pull up type. See MTK_PULL_SET_RSEL_ defines
                  in dt-bindings/pinctrl/mt65xx.h.

          bias-disable: true

          output-high: true

          output-low: true

          input-enable: true

          input-disable: true

          input-schmitt-enable: true

          input-schmitt-disable: true

        required:
          - pinmux

        additionalProperties: false

allOf:
  - $ref: pinctrl.yaml#

required:
  - compatible
  - reg
  - interrupts
  - interrupt-controller
  - '#interrupt-cells'
  - gpio-controller
  - '#gpio-cells'
  - gpio-ranges

additionalProperties: false

examples:
  - |
    #include <dt-bindings/pinctrl/mt8192-pinfunc.h>
    #include <dt-bindings/interrupt-controller/arm-gic.h>
    pio: pinctrl@10005000 {
        compatible = "mediatek,mt8192-pinctrl";
        reg = <0x10005000 0x1000>,
              <0x11c20000 0x1000>,
              <0x11d10000 0x1000>,
              <0x11d30000 0x1000>,
              <0x11d40000 0x1000>,
              <0x11e20000 0x1000>,
              <0x11e70000 0x1000>,
              <0x11ea0000 0x1000>,
              <0x11f20000 0x1000>,
              <0x11f30000 0x1000>,
              <0x1000b000 0x1000>;
        reg-names = "iocfg0", "iocfg_rm", "iocfg_bm",
              "iocfg_bl", "iocfg_br", "iocfg_lm",
              "iocfg_lb", "iocfg_rt", "iocfg_lt",
              "iocfg_tl", "eint";
        gpio-controller;
        #gpio-cells = <2>;
        gpio-ranges = <&pio 0 0 220>;
        interrupt-controller;
        interrupts = <GIC_SPI 212 IRQ_TYPE_LEVEL_HIGH 0>;
        #interrupt-cells = <2>;

        spi1-default-pins {
            pins-cs-mosi-clk {
                pinmux = <PINMUX_GPIO157__FUNC_SPI1_A_CSB>,
                         <PINMUX_GPIO159__FUNC_SPI1_A_MO>,
                         <PINMUX_GPIO156__FUNC_SPI1_A_CLK>;
                bias-disable;
            };

            pins-miso {
                pinmux = <PINMUX_GPIO158__FUNC_SPI1_A_MI>;
                bias-pull-down;
            };
        };
    };
