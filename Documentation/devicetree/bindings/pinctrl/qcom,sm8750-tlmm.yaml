# SPDX-License-Identifier: (GPL-2.0-only OR BSD-2-Clause)
%YAML 1.2
---
$id: http://devicetree.org/schemas/pinctrl/qcom,sm8750-tlmm.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: Qualcomm Technologies, Inc. SM8750 TLMM block

maintainers:
  - <PERSON> <<EMAIL>>

description:
  Top Level Mode Multiplexer pin controller in Qualcomm SM8750 SoC.

allOf:
  - $ref: /schemas/pinctrl/qcom,tlmm-common.yaml#

properties:
  compatible:
    const: qcom,sm8750-tlmm

  reg:
    maxItems: 1

  interrupts:
    maxItems: 1

  gpio-reserved-ranges:
    minItems: 1
    maxItems: 108

  gpio-line-names:
    maxItems: 215

patternProperties:
  "-state$":
    oneOf:
      - $ref: "#/$defs/qcom-sm8750-tlmm-state"
      - patternProperties:
          "-pins$":
            $ref: "#/$defs/qcom-sm8750-tlmm-state"
        additionalProperties: false

$defs:
  qcom-sm8750-tlmm-state:
    type: object
    description:
      Pinctrl node's client devices use subnodes for desired pin configuration.
      Client device subnodes use below standard properties.
    $ref: qcom,tlmm-common.yaml#/$defs/qcom-tlmm-state
    unevaluatedProperties: false

    properties:
      pins:
        description:
          List of gpio pins affected by the properties specified in this
          subnode.
        items:
          oneOf:
            - pattern: "^gpio([0-9]|[1-9][0-9]|1[0-9][0-9]|20[0-9]|21[0-4])$"
            - enum: [ ufs_reset, sdc2_clk, sdc2_cmd, sdc2_data ]
        minItems: 1
        maxItems: 36

      function:
        description:
          Specify the alternative function to be configured for the specified
          pins.
        enum: [ gpio, aoss_cti, atest_char, atest_usb, audio_ext_mclk0,
                audio_ext_mclk1, audio_ref_clk, cam_aon_mclk2, cam_aon_mclk4,
                cam_mclk, cci_async_in, cci_i2c_scl, cci_i2c_sda, cci_timer,
                cmu_rng, coex_uart1_rx, coex_uart1_tx, coex_uart2_rx,
                coex_uart2_tx, dbg_out_clk, ddr_bist_complete, ddr_bist_fail,
                ddr_bist_start, ddr_bist_stop, ddr_pxi0, ddr_pxi1, ddr_pxi2,
                ddr_pxi3, dp_hot, egpio, gcc_gp1, gcc_gp2, gcc_gp3, gnss_adc0,
                gnss_adc1, i2chub0_se0, i2chub0_se1, i2chub0_se2, i2chub0_se3,
                i2chub0_se4, i2chub0_se5, i2chub0_se6, i2chub0_se7, i2chub0_se8,
                i2chub0_se9, i2s0_data0, i2s0_data1, i2s0_sck, i2s0_ws,
                i2s1_data0, i2s1_data1, i2s1_sck, i2s1_ws, ibi_i3c, jitter_bist,
                mdp_esync0_out, mdp_esync1_out, mdp_vsync, mdp_vsync0_out,
                mdp_vsync1_out, mdp_vsync2_out, mdp_vsync3_out, mdp_vsync5_out,
                mdp_vsync_e, nav_gpio0, nav_gpio1, nav_gpio2, nav_gpio3,
                pcie0_clk_req_n, phase_flag, pll_bist_sync, pll_clk_aux,
                prng_rosc0, prng_rosc1, prng_rosc2, prng_rosc3, qdss_cti,
                qlink_big_enable, qlink_big_request, qlink_little_enable,
                qlink_little_request, qlink_wmss, qspi0, qspi1, qspi2, qspi3,
                qspi_clk, qspi_cs, qup1_se0, qup1_se1, qup1_se2, qup1_se3,
                qup1_se4, qup1_se5, qup1_se6, qup1_se7, qup2_se0, qup2_se1,
                qup2_se2, qup2_se3, qup2_se4, qup2_se5, qup2_se6, qup2_se7,
                sd_write_protect, sdc40, sdc41, sdc42, sdc43, sdc4_clk,
                sdc4_cmd, tb_trig_sdc2, tb_trig_sdc4, tmess_prng0, tmess_prng1,
                tmess_prng2, tmess_prng3, tsense_pwm1, tsense_pwm2, tsense_pwm3,
                tsense_pwm4, uim0_clk, uim0_data, uim0_present, uim0_reset,
                uim1_clk, uim1_data, uim1_present, uim1_reset, usb1_hs, usb_phy,
                vfr_0, vfr_1, vsense_trigger_mirnat, wcn_sw, wcn_sw_ctrl ]

    required:
      - pins

required:
  - compatible
  - reg

unevaluatedProperties: false

examples:
  - |
    #include <dt-bindings/interrupt-controller/arm-gic.h>
    tlmm: pinctrl@f100000 {
        compatible = "qcom,sm8750-tlmm";
        reg = <0x0f100000 0x300000>;
        gpio-controller;
        #gpio-cells = <2>;
        gpio-ranges = <&tlmm 0 0 216>;
        interrupt-controller;
        #interrupt-cells = <2>;
        interrupts = <GIC_SPI 208 IRQ_TYPE_LEVEL_HIGH>;

        gpio-wo-state {
            pins = "gpio1";
            function = "gpio";
        };

        uart-w-state {
            rx-pins {
                pins = "gpio60";
                function = "qup1_se7";
                bias-pull-up;
            };

            tx-pins {
                pins = "gpio61";
                function = "qup1_se7";
                bias-disable;
            };
        };
    };
...
