# SPDX-License-Identifier: (GPL-2.0 OR BSD-2-Clause)
%YAML 1.2
---
$id: http://devicetree.org/schemas/pinctrl/qcom,pmic-gpio.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: Qualcomm PMIC GPIO block

maintainers:
  - <PERSON><PERSON><PERSON> <bjorn.and<PERSON><PERSON>@linaro.org>

description:
  This binding describes the GPIO block(s) found in the 8xxx series of
  PMIC's from Qualcomm.

properties:
  compatible:
    items:
      - enum:
          - qcom,pm2250-gpio
          - qcom,pm660-gpio
          - qcom,pm660l-gpio
          - qcom,pm6125-gpio
          - qcom,pm6150-gpio
          - qcom,pm6150l-gpio
          - qcom,pm6350-gpio
          - qcom,pm6450-gpio
          - qcom,pm7250b-gpio
          - qcom,pm7325-gpio
          - qcom,pm7550ba-gpio
          - qcom,pm8005-gpio
          - qcom,pm8018-gpio
          - qcom,pm8019-gpio
          - qcom,pm8038-gpio
          - qcom,pm8058-gpio
          - qcom,pm8150-gpio
          - qcom,pm8150b-gpio
          - qcom,pm8150l-gpio
          - qcom,pm8226-gpio
          - qcom,pm8350-gpio
          - qcom,pm8350b-gpio
          - qcom,pm8350c-gpio
          - qcom,pm8450-gpio
          - qcom,pm8550-gpio
          - qcom,pm8550b-gpio
          - qcom,pm8550ve-gpio
          - qcom,pm8550vs-gpio
          - qcom,pm8916-gpio
          - qcom,pm8917-gpio
          - qcom,pm8921-gpio
          - qcom,pm8937-gpio
          - qcom,pm8941-gpio
          - qcom,pm8950-gpio
          - qcom,pm8953-gpio
          - qcom,pm8994-gpio
          - qcom,pm8998-gpio
          - qcom,pma8084-gpio
          - qcom,pmc8180-gpio
          - qcom,pmc8180c-gpio
          - qcom,pmc8380-gpio
          - qcom,pmd8028-gpio
          - qcom,pmi632-gpio
          - qcom,pmi8950-gpio
          - qcom,pmi8994-gpio
          - qcom,pmi8998-gpio
          - qcom,pmih0108-gpio
          - qcom,pmk8350-gpio
          - qcom,pmk8550-gpio
          - qcom,pmm8155au-gpio
          - qcom,pmm8654au-gpio
          - qcom,pmp8074-gpio
          - qcom,pmr735a-gpio
          - qcom,pmr735b-gpio
          - qcom,pmr735d-gpio
          - qcom,pms405-gpio
          - qcom,pmx55-gpio
          - qcom,pmx65-gpio
          - qcom,pmx75-gpio
          - qcom,pmxr2230-gpio

      - enum:
          - qcom,spmi-gpio
          - qcom,ssbi-gpio

  reg:
    maxItems: 1

  interrupt-controller: true

  '#interrupt-cells':
    const: 2

  gpio-controller: true

  gpio-ranges:
    maxItems: 1

  gpio-line-names:
    minItems: 2
    maxItems: 44

  gpio-reserved-ranges:
    minItems: 1
    # maxItems as half of total number of GPIOs, as there has to be at
    # least one usable GPIO between each reserved range.
    maxItems: 22

  '#gpio-cells':
    const: 2
    description:
      The first cell will be used to define gpio number and the
      second denotes the flags for this gpio

additionalProperties: false

required:
  - compatible
  - reg
  - gpio-controller
  - '#gpio-cells'
  - gpio-ranges
  - interrupt-controller

allOf:
  - if:
      properties:
        compatible:
          contains:
            enum:
              - qcom,pmi8950-gpio
              - qcom,pmr735d-gpio
    then:
      properties:
        gpio-line-names:
          minItems: 2
          maxItems: 2
        gpio-reserved-ranges:
          maxItems: 1

  - if:
      properties:
        compatible:
          contains:
            enum:
              - qcom,pm8005-gpio
              - qcom,pm8450-gpio
              - qcom,pm8916-gpio
              - qcom,pmd8028-gpio
              - qcom,pmk8350-gpio
              - qcom,pmr735a-gpio
              - qcom,pmr735b-gpio
    then:
      properties:
        gpio-line-names:
          minItems: 4
          maxItems: 4
        gpio-reserved-ranges:
          minItems: 1
          maxItems: 2

  - if:
      properties:
        compatible:
          contains:
            enum:
              - qcom,pm8018-gpio
              - qcom,pm8019-gpio
              - qcom,pm8550vs-gpio
              - qcom,pmk8550-gpio
    then:
      properties:
        gpio-line-names:
          minItems: 6
          maxItems: 6
        gpio-reserved-ranges:
          minItems: 1
          maxItems: 3

  - if:
      properties:
        compatible:
          contains:
            enum:
              - qcom,pm7550ba-gpio
              - qcom,pm8226-gpio
              - qcom,pm8350b-gpio
              - qcom,pm8550ve-gpio
              - qcom,pm8937-gpio
              - qcom,pm8950-gpio
              - qcom,pm8953-gpio
              - qcom,pmi632-gpio
    then:
      properties:
        gpio-line-names:
          minItems: 8
          maxItems: 8
        gpio-reserved-ranges:
          minItems: 1
          maxItems: 4

  - if:
      properties:
        compatible:
          contains:
            enum:
              - qcom,pm6350-gpio
              - qcom,pm6450-gpio
              - qcom,pm8350c-gpio
    then:
      properties:
        gpio-line-names:
          minItems: 9
          maxItems: 9
        gpio-reserved-ranges:
          minItems: 1
          maxItems: 5

  - if:
      properties:
        compatible:
          contains:
            enum:
              - qcom,pm2250-gpio
              - qcom,pm6150-gpio
              - qcom,pm7325-gpio
              - qcom,pm8150-gpio
              - qcom,pm8350-gpio
              - qcom,pmc8180-gpio
              - qcom,pmc8380-gpio
              - qcom,pmi8994-gpio
              - qcom,pmm8155au-gpio
    then:
      properties:
        gpio-line-names:
          minItems: 10
          maxItems: 10
        gpio-reserved-ranges:
          minItems: 1
          maxItems: 5

  - if:
      properties:
        compatible:
          contains:
            enum:
              - qcom,pmx55-gpio
    then:
      properties:
        gpio-line-names:
          minItems: 11
          maxItems: 11
        gpio-reserved-ranges:
          minItems: 1
          maxItems: 6

  - if:
      properties:
        compatible:
          contains:
            enum:
              - qcom,pm660l-gpio
              - qcom,pm6150l-gpio
              - qcom,pm7250b-gpio
              - qcom,pm8038-gpio
              - qcom,pm8150b-gpio
              - qcom,pm8150l-gpio
              - qcom,pm8550-gpio
              - qcom,pm8550b-gpio
              - qcom,pmc8180c-gpio
              - qcom,pmp8074-gpio
              - qcom,pms405-gpio
              - qcom,pmxr2230-gpio
    then:
      properties:
        gpio-line-names:
          minItems: 12
          maxItems: 12
        gpio-reserved-ranges:
          minItems: 1
          maxItems: 6

  - if:
      properties:
        compatible:
          contains:
            enum:
              - qcom,pm660-gpio
    then:
      properties:
        gpio-line-names:
          minItems: 13
          maxItems: 13
        gpio-reserved-ranges:
          minItems: 1
          maxItems: 7

  - if:
      properties:
        compatible:
          contains:
            enum:
              - qcom,pmi8998-gpio
    then:
      properties:
        gpio-line-names:
          minItems: 14
          maxItems: 14
        gpio-reserved-ranges:
          minItems: 1
          maxItems: 7

  - if:
      properties:
        compatible:
          contains:
            enum:
              - qcom,pmih0108-gpio
    then:
      properties:
        gpio-line-names:
          minItems: 18
          maxItems: 18
        gpio-reserved-ranges:
          minItems: 1
          maxItems: 9

  - if:
      properties:
        compatible:
          contains:
            enum:
              - qcom,pmx65-gpio
              - qcom,pmx75-gpio
    then:
      properties:
        gpio-line-names:
          minItems: 16
          maxItems: 16
        gpio-reserved-ranges:
          minItems: 1
          maxItems: 8

  - if:
      properties:
        compatible:
          contains:
            enum:
              - qcom,pm8994-gpio
              - qcom,pma8084-gpio
    then:
      properties:
        gpio-line-names:
          minItems: 22
          maxItems: 22
        gpio-reserved-ranges:
          minItems: 1
          maxItems: 11

  - if:
      properties:
        compatible:
          contains:
            enum:
              - qcom,pm8998-gpio
    then:
      properties:
        gpio-line-names:
          minItems: 26
          maxItems: 26
        gpio-reserved-ranges:
          minItems: 1
          maxItems: 13

  - if:
      properties:
        compatible:
          contains:
            enum:
              - qcom,pm8941-gpio
    then:
      properties:
        gpio-line-names:
          minItems: 36
          maxItems: 36
        gpio-reserved-ranges:
          minItems: 1
          maxItems: 18

  - if:
      properties:
        compatible:
          contains:
            enum:
              - qcom,pm8917-gpio
    then:
      properties:
        gpio-line-names:
          minItems: 38
          maxItems: 38
        gpio-reserved-ranges:
          minItems: 1
          maxItems: 19

  - if:
      properties:
        compatible:
          contains:
            enum:
              - qcom,pm8058-gpio
              - qcom,pm8921-gpio
    then:
      properties:
        gpio-line-names:
          minItems: 44
          maxItems: 44
        gpio-reserved-ranges:
          minItems: 1
          maxItems: 22

patternProperties:
  '-state$':
    oneOf:
      - $ref: "#/$defs/qcom-pmic-gpio-state"
      - patternProperties:
          "(pinconf|-pins)$":
            $ref: "#/$defs/qcom-pmic-gpio-state"
        additionalProperties: false

  "-hog(-[0-9]+)?$":
    type: object
    required:
      - gpio-hog

$defs:
  qcom-pmic-gpio-state:
    type: object
    allOf:
      - $ref: pinmux-node.yaml
      - $ref: pincfg-node.yaml
    properties:
      pins:
        description:
          List of gpio pins affected by the properties specified in
          this subnode.  Valid pins are
                 - gpio1-gpio9 for pm6125
                 - gpio1-gpio10 for pm6150
                 - gpio1-gpio12 for pm6150l
                 - gpio1-gpio9 for pm6350
                 - gpio1-gpio9 for pm6450
                 - gpio1-gpio12 for pm7250b
                 - gpio1-gpio10 for pm7325
                 - gpio1-gpio8 for pm7550ba
                 - gpio1-gpio4 for pm8005
                 - gpio1-gpio6 for pm8018
                 - gpio1-gpio12 for pm8038
                 - gpio1-gpio40 for pm8058
                 - gpio1-gpio10 for pm8150 (holes on gpio2, gpio5,
                                            gpio7 and gpio8)
                 - gpio1-gpio12 for pm8150b (holes on gpio3, gpio4
                                             and gpio7)
                 - gpio1-gpio12 for pm8150l (hole on gpio7)
                 - gpio1-gpio4 for pm8916
                 - gpio1-gpio10 for pm8350
                 - gpio1-gpio8 for pm8350b
                 - gpio1-gpio9 for pm8350c
                 - gpio1-gpio4 for pm8450
                 - gpio1-gpio12 for pm8550
                 - gpio1-gpio12 for pm8550b
                 - gpio1-gpio8 for pm8550ve
                 - gpio1-gpio6 for pm8550vs
                 - gpio1-gpio38 for pm8917
                 - gpio1-gpio44 for pm8921
                 - gpio1-gpio8 for pm8937 (hole on gpio3, gpio4 and gpio6)
                 - gpio1-gpio36 for pm8941
                 - gpio1-gpio8 for pm8950 (hole on gpio3)
                 - gpio1-gpio8 for pm8953 (hole on gpio3 and gpio6)
                 - gpio1-gpio22 for pm8994
                 - gpio1-gpio26 for pm8998
                 - gpio1-gpio22 for pma8084
                 - gpio1-gpio4 for pmd8028
                 - gpio1-gpio8 for pmi632
                 - gpio1-gpio2 for pmi8950
                 - gpio1-gpio10 for pmi8994
                 - gpio1-gpio18 for pmih0108
                 - gpio1-gpio4 for pmk8350
                 - gpio1-gpio6 for pmk8550
                 - gpio1-gpio10 for pmm8155au
                 - gpio1-gpio12 for pmm8654au
                 - gpio1-gpio12 for pmp8074 (holes on gpio1 and gpio12)
                 - gpio1-gpio4 for pmr735a
                 - gpio1-gpio4 for pmr735b
                 - gpio1-gpio2 for pmr735d
                 - gpio1-gpio12 for pms405 (holes on gpio1, gpio9
                                            and gpio10)
                 - gpio1-gpio11 for pmx55 (holes on gpio3, gpio7, gpio10
                                            and gpio11)
                 - gpio1-gpio16 for pmx65
                 - gpio1-gpio16 for pmx75
                 - gpio1-gpio12 for pmxr2230

        items:
          pattern: "^gpio([0-9]+)$"

      function:
        items:
          - enum:
              - normal
              - paired
              - func1
              - func2
              - dtest1
              - dtest2
              - dtest3
              - dtest4
              - func3  # supported by LV/MV GPIO subtypes
              - func4  # supported by LV/MV GPIO subtypes

      bias-disable: true
      bias-pull-down: true
      bias-pull-up: true

      qcom,pull-up-strength:
        $ref: /schemas/types.yaml#/definitions/uint32
        description:
          Specifies the strength to use for pull up, if selected.
          Valid values are defined in
          <dt-bindings/pinctrl/qcom,pmic-gpio.h>
          If this property is omitted 30uA strength will be used
          if pull up is selected
        enum: [0, 1, 2, 3]

      bias-high-impedance: true
      input-enable: true
      input-disable: true
      output-high: true
      output-low: true
      output-enable: true
      output-disable: true
      power-source: true

      qcom,drive-strength:
        $ref: /schemas/types.yaml#/definitions/uint32
        description:
          Selects the drive strength for the specified pins
          Valid drive strength values are defined in
          <dt-bindings/pinctrl/qcom,pmic-gpio.h>
        enum: [0, 1, 2, 3]

      drive-push-pull: true
      drive-open-drain: true
      drive-open-source: true

      qcom,analog-pass:
        $ref: /schemas/types.yaml#/definitions/flag
        description:
          The specified pins are configured in
          analog-pass-through mode.

      qcom,atest:
        $ref: /schemas/types.yaml#/definitions/uint32
        description:
          Selects ATEST rail to route to GPIO when it's
          configured in analog-pass-through mode.
        enum: [1, 2, 3, 4]

      qcom,dtest-buffer:
        $ref: /schemas/types.yaml#/definitions/uint32
        description:
          Selects DTEST rail to route to GPIO when it's
          configured as digital input.
        enum: [1, 2, 3, 4]

    required:
      - pins
      - function

    additionalProperties: false

examples:
  - |
    #include <dt-bindings/gpio/gpio.h>
    #include <dt-bindings/pinctrl/qcom,pmic-gpio.h>

    pm8921_gpio: gpio@150 {
      compatible = "qcom,pm8921-gpio", "qcom,ssbi-gpio";
      reg = <0x150 0x160>;
      interrupt-controller;
      #interrupt-cells = <2>;
      gpio-controller;
      gpio-ranges = <&pm8921_gpio 0 0 44>;
      #gpio-cells = <2>;

      pm8921_gpio_keys: gpio-keys-state {
        volume-keys-pins {
          pins = "gpio20", "gpio21";
          function = "normal";

          input-enable;
          bias-pull-up;
          drive-push-pull;
          qcom,drive-strength = <PMIC_GPIO_STRENGTH_NO>;
          power-source = <PM8921_GPIO_S4>;
        };
      };

      otg-hog {
        gpio-hog;
        gpios = <35 GPIO_ACTIVE_HIGH>;
        output-high;
        line-name = "otg-gpio";
      };
    };
...
