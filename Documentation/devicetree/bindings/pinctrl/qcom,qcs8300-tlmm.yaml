# SPDX-License-Identifier: (GPL-2.0-only OR BSD-2-Clause)
%YAML 1.2
---
$id: http://devicetree.org/schemas/pinctrl/qcom,qcs8300-tlmm.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: Qualcomm Technologies, Inc. QCS8300 TLMM block

maintainers:
  - <PERSON><PERSON> <<EMAIL>>

description: |
  Top Level Mode Multiplexer pin controller in Qualcomm QCS8300 SoC.

allOf:
  - $ref: /schemas/pinctrl/qcom,tlmm-common.yaml#

properties:
  compatible:
    const: qcom,qcs8300-tlmm

  reg:
    maxItems: 1

  interrupts:
    maxItems: 1

  gpio-reserved-ranges:
    minItems: 1
    maxItems: 67

  gpio-line-names:
    maxItems: 133

patternProperties:
  "-state$":
    oneOf:
      - $ref: "#/$defs/qcom-qcs8300-tlmm-state"
      - patternProperties:
          "-pins$":
            $ref: "#/$defs/qcom-qcs8300-tlmm-state"
        additionalProperties: false

$defs:
  qcom-qcs8300-tlmm-state:
    type: object
    description:
      Pinctrl node's client devices use subnodes for desired pin configuration.
      Client device subnodes use below standard properties.
    $ref: qcom,tlmm-common.yaml#/$defs/qcom-tlmm-state
    unevaluatedProperties: false

    properties:
      pins:
        description:
          List of gpio pins affected by the properties specified in this
          subnode.
        items:
          oneOf:
            - pattern: "^gpio([0-9]|[1-9][0-9]|1[0-2][0-9]|13[0-2])$"
            - enum: [ ufs_reset, sdc1_rclk, sdc1_clk, sdc1_cmd, sdc1_data ]
        minItems: 1
        maxItems: 36

      function:
        description:
          Specify the alternative function to be configured for the specified
          pins.

        enum: [ aoss_cti, atest_char, atest_usb2, audio_ref, cam_mclk,
                cci_async, cci_i2c_scl, cci_i2c_sda, cci_timer, cri_trng,
                dbg_out, ddr_bist, ddr_pxi0, ddr_pxi1, ddr_pxi2, ddr_pxi3,
                edp0_hot, edp0_lcd, edp1_lcd, egpio, emac0_mcg0, emac0_mcg1,
                emac0_mcg2, emac0_mcg3, emac0_mdc, emac0_mdio, emac0_ptp_aux,
                emac0_ptp_pps, gcc_gp1, gcc_gp2, gcc_gp3, gcc_gp4, gcc_gp5,
                gpio, hs0_mi2s, hs1_mi2s, hs2_mi2s, ibi_i3c, jitter_bist,
                mdp0_vsync0, mdp0_vsync1, mdp0_vsync3, mdp0_vsync6, mdp0_vsync7,
                mdp_vsync, mi2s1_data0, mi2s1_data1, mi2s1_sck, mi2s1_ws,
                mi2s2_data0, mi2s2_data1, mi2s2_sck, mi2s2_ws, mi2s_mclk0,
                mi2s_mclk1, pcie0_clkreq, pcie1_clkreq, phase_flag, pll_bist,
                pll_clk, prng_rosc0, prng_rosc1, prng_rosc2, prng_rosc3,
                qdss_cti, qdss_gpio, qup0_se0, qup0_se1, qup0_se2, qup0_se3,
                qup0_se4, qup0_se5, qup0_se6, qup0_se7, qup1_se0, qup1_se1,
                qup1_se2, qup1_se3, qup1_se4, qup1_se5, qup1_se6, qup1_se7,
                qup2_se0, sailss_emac0, sailss_ospi, sail_top, sgmii_phy,
                tb_trig, tgu_ch0, tgu_ch1, tgu_ch2, tgu_ch3, tsense_pwm1,
                tsense_pwm2, tsense_pwm3, tsense_pwm4, usb2phy_ac,
                vsense_trigger ]

    required:
      - pins

required:
  - compatible
  - reg

unevaluatedProperties: false

examples:
  - |
    #include <dt-bindings/interrupt-controller/arm-gic.h>

    tlmm: pinctrl@f100000 {
        compatible = "qcom,qcs8300-tlmm";
        reg = <0x0f100000 0x300000>;
        interrupts = <GIC_SPI 208 IRQ_TYPE_LEVEL_HIGH>;
        gpio-controller;
        #gpio-cells = <2>;
        gpio-ranges = <&tlmm 0 0 134>;
        interrupt-controller;
        #interrupt-cells = <2>;

        qup-uart7-state {
            pins = "gpio43", "gpio44";
            function = "qup0_se7";
        };
    };
...
