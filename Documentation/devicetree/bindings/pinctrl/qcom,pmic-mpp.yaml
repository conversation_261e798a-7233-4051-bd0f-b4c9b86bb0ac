# SPDX-License-Identifier: (GPL-2.0 OR BSD-2-Clause)
%YAML 1.2
---
$id: http://devicetree.org/schemas/pinctrl/qcom,pmic-mpp.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: Qualcomm PMIC Multi-Purpose Pin (MPP) block

maintainers:
  - <PERSON><PERSON><PERSON> <bjorn.and<PERSON><PERSON>@linaro.org>

description:
  This binding describes the MPP block(s) found in the 8xxx series of
  PMIC's from Qualcomm.

properties:
  compatible:
    oneOf:
      - items:
          - enum:
              - qcom,pm8019-mpp
              - qcom,pm8226-mpp
              - qcom,pm8841-mpp
              - qcom,pm8916-mpp
              - qcom,pm8937-mpp
              - qcom,pm8941-mpp
              - qcom,pm8950-mpp
              - qcom,pmi8950-mpp
              - qcom,pm8994-mpp
              - qcom,pma8084-mpp
              - qcom,pmi8994-mpp
          - const: qcom,spmi-mpp
      - items:
          - enum:
              - qcom,pm8018-mpp
              - qcom,pm8038-mpp
              - qcom,pm8058-mpp
              - qcom,pm8821-mpp
              - qcom,pm8901-mpp
              - qcom,pm8917-mpp
              - qcom,pm8921-mpp
          - const: qcom,ssbi-mpp

  reg:
    maxItems: 1

  interrupt-controller: true

  '#interrupt-cells':
    const: 2

  gpio-controller: true
  gpio-line-names: true

  gpio-ranges:
    maxItems: 1

  '#gpio-cells':
    const: 2
    description:
      The first cell will be used to define gpio number and the
      second denotes the flags for this gpio

additionalProperties: false

required:
  - compatible
  - reg
  - gpio-controller
  - '#gpio-cells'
  - gpio-ranges
  - interrupt-controller

patternProperties:
  '-state$':
    oneOf:
      - $ref: "#/$defs/qcom-pmic-mpp-state"
      - patternProperties:
          '-pins$':
            $ref: "#/$defs/qcom-pmic-mpp-state"
        additionalProperties: false

$defs:
  qcom-pmic-mpp-state:
    type: object
    allOf:
      - $ref: pinmux-node.yaml
      - $ref: pincfg-node.yaml
    properties:
      pins:
        description:
          List of gpio pins affected by the properties specified in
          this subnode.  Valid pins are
                 - mpp1-mpp4 for pm8841
                 - mpp1-mpp4 for pm8916
                 - mpp1-mpp4 for pm8937
                 - mpp1-mpp8 for pm8941
                 - mpp1-mpp4 for pm8950
                 - mpp1-mpp4 for pmi8950
                 - mpp1-mpp4 for pma8084

        items:
          pattern: "^mpp([0-9]+)$"

      function:
        items:
          - enum:
              - digital
              - analog
              - sink

      bias-disable: true
      bias-pull-up: true
      bias-high-impedance: true
      input-enable: true
      output-high: true
      output-low: true
      power-source: true

      qcom,analog-level:
        $ref: /schemas/types.yaml#/definitions/uint32
        description:
          Selects the source for analog output. Valued values are defined in
          <dt-binding/pinctrl/qcom,pmic-mpp.h> PMIC_MPP_AOUT_LVL_*
        enum: [0, 1, 2, 3, 4, 5, 6, 7]

      qcom,atest:
        $ref: /schemas/types.yaml#/definitions/uint32
        description:
          Selects ATEST rail to route to GPIO when it's
          configured in analog-pass-through mode.
        enum: [1, 2, 3, 4]

      qcom,dtest:
        $ref: /schemas/types.yaml#/definitions/uint32
        description:
          Selects DTEST rail to route to GPIO when it's
          configured as digital input.
        enum: [1, 2, 3, 4]

      qcom,amux-route:
        $ref: /schemas/types.yaml#/definitions/uint32
        description:
          Selects the source for analog input. Valid values are defined in
          <dt-bindings/pinctrl/qcom,pmic-mpp.h> PMIC_MPP_AMUX_ROUTE_CH5,
          PMIC_MPP_AMUX_ROUTE_CH6...
        enum: [0, 1, 2, 3, 4, 5, 6, 7]

      qcom,paired:
        type: boolean
        description:
          Indicates that the pin should be operating in paired mode.

    required:
      - pins
      - function

    additionalProperties: false

examples:
  - |
    #include <dt-bindings/pinctrl/qcom,pmic-mpp.h>

    pmic {
        #address-cells = <1>;
        #size-cells = <0>;

        pm8841_mpp: mpps@a000 {
            compatible = "qcom,pm8841-mpp", "qcom,spmi-mpp";
            reg = <0xa000>;

            gpio-controller;
            #gpio-cells = <2>;
            gpio-ranges = <&pm8841_mpp 0 0 4>;
            gpio-line-names = "VDD_PX_BIAS", "WLAN_LED_CTRL",
                              "BT_LED_CTRL", "GPIO-F";
            interrupt-controller;
            #interrupt-cells = <2>;

            pinctrl-names = "default";
            pinctrl-0 = <&pm8841_default>;

            mpp1-state {
                pins = "mpp1";
                function = "digital";
                input-enable;
                power-source = <PM8841_MPP_S3>;
            };

            default-state {
                gpio-pins {
                    pins = "mpp1", "mpp2", "mpp3", "mpp4";
                    function = "digital";
                    input-enable;
                    power-source = <PM8841_MPP_S3>;
                };
            };
        };
    };
...
