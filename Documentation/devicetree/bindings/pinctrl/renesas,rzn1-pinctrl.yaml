# SPDX-License-Identifier: (GPL-2.0-only OR BSD-2-Clause)
%YAML 1.2
---
$id: http://devicetree.org/schemas/pinctrl/renesas,rzn1-pinctrl.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: Renesas RZ/N1 Pin Controller

maintainers:
  - <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>
  - <PERSON><PERSON> <<EMAIL>>

properties:
  compatible:
    items:
      - const: renesas,r9a06g032-pinctrl # RZ/N1D
      - const: renesas,rzn1-pinctrl   # Generic RZ/N1

  reg:
    items:
      - description: GPIO Multiplexing Level1 Register Block
      - description: GPIO Multiplexing Level2 Register Block

  clocks:
    maxItems: 1

  clock-names:
    const: bus
    description:
      The bus clock, sometimes described as pclk, for register accesses.

allOf:
  - $ref: pinctrl.yaml#

required:
  - compatible
  - reg
  - clocks
  - clock-names

additionalProperties:
  anyOf:
    - type: object
      allOf:
        - $ref: pincfg-node.yaml#
        - $ref: pinmux-node.yaml#

      description:
        A pin multiplexing sub-node describes how to configure a set of (or a
        single) pin in some desired alternate function mode.
        A single sub-node may define several pin configurations.

      properties:
        pinmux:
          description: |
            Integer array representing pin number and pin multiplexing
            configuration.
            When a pin has to be configured in alternate function mode, use
            this property to identify the pin by its global index, and provide
            its alternate function configuration number along with it.
            When multiple pins are required to be configured as part of the
            same alternate function they shall be specified as members of the
            same argument list of a single "pinmux" property.
            Integers values in the "pinmux" argument list are assembled as:
            (PIN | MUX_FUNC << 8)
            where PIN directly corresponds to the pl_gpio pin number and
            MUX_FUNC is one of the alternate function identifiers defined in:
            <include/dt-bindings/pinctrl/rzn1-pinctrl.h>
            These identifiers collapse the IO Multiplex Configuration Level 1
            and Level 2 numbers that are detailed in the hardware reference
            manual into a single number. The identifiers for Level 2 are simply
            offset by 10.  Additional identifiers are provided to specify the
            MDIO source peripheral.

        bias-disable: true
        bias-pull-up:
          description: Pull up the pin with 50 kOhm
        bias-pull-down:
          description: Pull down the pin with 50 kOhm
        bias-high-impedance: true
        drive-strength:
          enum: [ 4, 6, 8, 12 ]

      required:
        - pinmux

      additionalProperties:
        $ref: "#/additionalProperties/anyOf/0"

    - type: object
      additionalProperties:
        $ref: "#/additionalProperties/anyOf/0"

examples:
  - |
    #include <dt-bindings/clock/r9a06g032-sysctrl.h>
    #include <dt-bindings/pinctrl/rzn1-pinctrl.h>
    pinctrl: pinctrl@40067000 {
            compatible = "renesas,r9a06g032-pinctrl", "renesas,rzn1-pinctrl";
            reg = <0x40067000 0x1000>, <0x51000000 0x480>;
            clocks = <&sysctrl R9A06G032_HCLK_PINCONFIG>;
            clock-names = "bus";

            /*
             * A serial communication interface with a TX output pin and an RX
             * input pin.
             */
            pins_uart0: pins_uart0 {
                    pinmux = <
                            RZN1_PINMUX(103, RZN1_FUNC_UART0_I) /* UART0_TXD */
                            RZN1_PINMUX(104, RZN1_FUNC_UART0_I) /* UART0_RXD */
                    >;
            };

            /*
             * Set the pull-up on the RXD pin of the UART.
             */
            pins_uart0_alt: pins_uart0_alt {
                    pinmux = <RZN1_PINMUX(103, RZN1_FUNC_UART0_I)>;

                    pins_uart6_rx {
                            pinmux = <RZN1_PINMUX(104, RZN1_FUNC_UART0_I)>;
                            bias-pull-up;
                    };
            };
    };
