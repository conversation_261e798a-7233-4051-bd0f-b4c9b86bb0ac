# SPDX-License-Identifier: (GPL-2.0-only OR BSD-2-Clause)
%YAML 1.2
---
$id: http://devicetree.org/schemas/pinctrl/fsl,imx7ulp-iomuxc1.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: Freescale i.MX7ULP IOMUX Controller

description: |
  i.MX 7ULP has three IOMUXC instances: IOMUXC0 for M4 ports, IOMUXC1 for A7
  ports and IOMUXC DDR for DDR interface.

  Note: This binding doc is only for the IOMUXC1 support in A7 Domain and it
  only supports generic pin config.

  Please refer to fsl,imx-pinctrl.txt in this directory for common binding
  part and usage.

maintainers:
  - <PERSON> <<EMAIL>>

properties:
  compatible:
    const: fsl,imx7ulp-iomuxc1

  reg:
    maxItems: 1

patternProperties:
  'grp$':
    type: object
    description:
      Pinctrl node's client devices use subnodes for desired pin configuration.
      Client device subnodes use below standard properties.

    properties:
      fsl,pins:
        description:
          Each entry consists of 5 integers which represents the mux
          and config setting for one pin. The first 4 integers
          <mux_conf_reg input_reg mux_mode input_val> are specified
          using a PIN_FUNC_ID macro, which can be found in
          imx7ulp-pinfunc.h in the device tree source folder.
          The last integer CONFIG is the pad setting value like
          pull-up on this pin.

          Please refer to i.MX7ULP Reference Manual for detailed
          CONFIG settings.
        $ref: /schemas/types.yaml#/definitions/uint32-matrix
        items:
          items:
            - description: |
                "mux_conf_reg" indicates the offset of mux register.
            - description: |
                "input_reg" indicates the offset of select input register.
            - description: |
                "mux_mode" indicates the mux value to be applied.
            - description: |
                "input_val" indicates the select input value to be applied.
            - description: |
                CONFIG bits definition:
                  PAD_CTL_OBE           (1 << 17)
                  PAD_CTL_IBE           (1 << 16)
                  PAD_CTL_LK            (1 << 16)
                  PAD_CTL_DSE_HI        (1 << 6)
                  PAD_CTL_DSE_STD       (0 << 6)
                  PAD_CTL_ODE           (1 << 5)
                  PAD_CTL_PUSH_PULL     (0 << 5)
                  PAD_CTL_SRE_SLOW      (1 << 2)
                  PAD_CTL_SRE_STD       (0 << 2)
                  PAD_CTL_PE            (1 << 0)

    required:
      - fsl,pins

    additionalProperties: false

required:
  - compatible
  - reg

allOf:
  - $ref: pinctrl.yaml#

unevaluatedProperties: false

examples:
  - |
    pinctrl@40ac0000 {
        compatible = "fsl,imx7ulp-iomuxc1";
        reg = <0x40ac0000 0x1000>;

        lpuart4grp {
            fsl,pins = <
                0x000c 0x0248 0x4 0x1 0x1
                0x0008 0x024c 0x4 0x1 0x1
            >;
        };
    };
