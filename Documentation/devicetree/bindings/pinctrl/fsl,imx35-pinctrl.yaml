# SPDX-License-Identifier: GPL-2.0-only OR BSD-2-Clause
%YAML 1.2
---
$id: http://devicetree.org/schemas/pinctrl/fsl,imx35-pinctrl.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: Freescale IMX35/IMX5x/IMX6 IOMUX Controller

maintainers:
  - <PERSON> <<EMAIL>>

description:
  Please refer to fsl,imx-pinctrl.txt and pinctrl-bindings.txt in this directory
  for common binding part and usage.

allOf:
  - $ref: pinctrl.yaml#

properties:
  compatible:
    oneOf:
      - enum:
          - fsl,imx35-iomuxc
          - fsl,imx51-iomuxc
          - fsl,imx53-iomuxc
          - fsl,imx6dl-iomuxc
          - fsl,imx6q-iomuxc
          - fsl,imx6sl-iomuxc
          - fsl,imx6sll-iomuxc
          - fsl,imx6sx-iomuxc
          - fsl,imx6ul-iomuxc
          - fsl,imx6ull-iomuxc-snvs
      - items:
          - const: fsl,imx50-iomuxc
          - const: fsl,imx53-iomuxc

  reg:
    maxItems: 1

# Client device subnode's properties
patternProperties:
  'grp$':
    type: object
    description:
      Pinctrl node's client devices use subnodes for desired pin configuration.
      Client device subnodes use below standard properties.

    properties:
      fsl,pins:
        description:
          each entry consists of 6 integers and represents the mux and config
          setting for one pin. The first 5 integers <mux_reg conf_reg input_reg
          mux_val input_val> are specified using a PIN_FUNC_ID macro, which can
          be found in <arch/arm/boot/dts/nxp/imx/imx*-pinfunc.h>. The last integer
          CONFIG is the pad setting value like pull-up on this pin. Please
          refer to matching i.MX Reference Manual for detailed CONFIG settings.
        $ref: /schemas/types.yaml#/definitions/uint32-matrix
        items:
          items:
            - description: |
                "mux_reg" indicates the offset of mux register.
            - description: |
                "conf_reg" indicates the offset of pad configuration register.
            - description: |
                "input_reg" indicates the offset of select input register.
            - description: |
                "mux_val" indicates the mux value to be applied.
            - description: |
                "input_val" indicates the select input value to be applied.
            - description: |
                "pad_setting" indicates the pad configuration value to be applied.
                Common i.MX35
                  PAD_CTL_DRIVE_VOLAGAGE_18       (1 << 13)
                  PAD_CTL_DRIVE_VOLAGAGE_33       (0 << 13)
                  PAD_CTL_HYS                     (1 << 8)
                  PAD_CTL_PKE                     (1 << 7)
                  PAD_CTL_PUE                     (1 << 6)
                  PAD_CTL_PUS_100K_DOWN           (0 << 4)
                  PAD_CTL_PUS_47K_UP              (1 << 4)
                  PAD_CTL_PUS_100K_UP             (2 << 4)
                  PAD_CTL_PUS_22K_UP              (3 << 4)
                  PAD_CTL_ODE_CMOS                (0 << 3)
                  PAD_CTL_ODE_OPENDRAIN           (1 << 3)
                  PAD_CTL_DSE_NOMINAL             (0 << 1)
                  PAD_CTL_DSE_HIGH                (1 << 1)
                  PAD_CTL_DSE_MAX                 (2 << 1)
                  PAD_CTL_SRE_FAST                (1 << 0)
                  PAD_CTL_SRE_SLOW                (0 << 0)
                Common i.MX50/i.MX51/i.MX53 bits
                  PAD_CTL_HVE                     (1 << 13)
                  PAD_CTL_HYS                     (1 << 8)
                  PAD_CTL_PKE                     (1 << 7)
                  PAD_CTL_PUE                     (1 << 6)
                  PAD_CTL_PUS_100K_DOWN           (0 << 4)
                  PAD_CTL_PUS_47K_UP              (1 << 4)
                  PAD_CTL_PUS_100K_UP             (2 << 4)
                  PAD_CTL_PUS_22K_UP              (3 << 4)
                  PAD_CTL_ODE                     (1 << 3)
                  PAD_CTL_DSE_LOW                 (0 << 1)
                  PAD_CTL_DSE_MED                 (1 << 1)
                  PAD_CTL_DSE_HIGH                (2 << 1)
                  PAD_CTL_DSE_MAX                 (3 << 1)
                  PAD_CTL_SRE_FAST                (1 << 0)
                  PAD_CTL_SRE_SLOW                (0 << 0)
                Common i.MX6 bits
                  PAD_CTL_HYS                     (1 << 16)
                  PAD_CTL_PUS_100K_DOWN           (0 << 14)
                  PAD_CTL_PUS_47K_UP              (1 << 14)
                  PAD_CTL_PUS_100K_UP             (2 << 14)
                  PAD_CTL_PUS_22K_UP              (3 << 14)
                  PAD_CTL_PUE                     (1 << 13)
                  PAD_CTL_PKE                     (1 << 12)
                  PAD_CTL_ODE                     (1 << 11)
                  PAD_CTL_SPEED_LOW               (0 << 6)
                  PAD_CTL_SPEED_MED               (1 << 6)
                  PAD_CTL_SPEED_HIGH              (3 << 6)
                  PAD_CTL_DSE_DISABLE             (0 << 3)
                  PAD_CTL_SRE_FAST                (1 << 0)
                  PAD_CTL_SRE_SLOW                (0 << 0)
                i.MX6SL/MX6SLL specific bits
                  PAD_CTL_LVE                     (1 << 22) (MX6SL/SLL only)
                i.MX6SLL/i.MX6SX/i.MX6UL/i.MX6ULL specific bits
                  PAD_CTL_DSE_260ohm              (1 << 3)
                  PAD_CTL_DSE_130ohm              (2 << 3)
                  PAD_CTL_DSE_87ohm               (3 << 3)
                  PAD_CTL_DSE_65ohm               (4 << 3)
                  PAD_CTL_DSE_52ohm               (5 << 3)
                  PAD_CTL_DSE_43ohm               (6 << 3)
                  PAD_CTL_DSE_37ohm               (7 << 3)
                i.MX6DL/i.MX6Q/i.MX6SL specific bits
                  PAD_CTL_DSE_240ohm              (1 << 3)
                  PAD_CTL_DSE_120ohm              (2 << 3)
                  PAD_CTL_DSE_80ohm               (3 << 3)
                  PAD_CTL_DSE_60ohm               (4 << 3)
                  PAD_CTL_DSE_48ohm               (5 << 3)
                  PAD_CTL_DSE_40ohm               (6 << 3)
                  PAD_CTL_DSE_34ohm               (7 << 3)

    required:
      - fsl,pins

    additionalProperties: false

required:
  - compatible
  - reg

additionalProperties: false

examples:
  - |
    iomuxc: pinctrl@20e0000 {
      compatible = "fsl,imx6ul-iomuxc";
      reg = <0x020e0000 0x4000>;

      mux_uart: uartgrp {
        fsl,pins = <
          0x0084 0x0310 0x0000 0 0 0x1b0b1
          0x0088 0x0314 0x0624 0 3 0x1b0b1
        >;
      };
    };
  - |
    iomuxc_snvs: pinctrl@2290000 {
      compatible = "fsl,imx6ull-iomuxc-snvs";
      reg = <0x02290000 0x4000>;

      pinctrl_snvs_usbc_det: snvsusbcdetgrp {
        fsl,pins = <
          0x0010 0x0054 0x0000 0x5 0x0 0x130b0
        >;
      };
    };
  - |
    iomuxc_mx6q: pinctrl@20e0000 {
        compatible = "fsl,imx6q-iomuxc";
        reg = <0x20e0000 0x4000>;

        pinctrl_uart4: uart4grp {
            fsl,pins =
                <0x288 0x658 0x000 0x3 0x0	0x140>,
                <0x28c 0x65c 0x938 0x3 0x3	0x140>;
        };
    };
