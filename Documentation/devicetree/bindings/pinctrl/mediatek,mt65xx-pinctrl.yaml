# SPDX-License-Identifier: (GPL-2.0-only OR BSD-2-Clause)
%YAML 1.2
---
$id: http://devicetree.org/schemas/pinctrl/mediatek,mt65xx-pinctrl.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: MediaTek MT65xx Pin Controller

maintainers:
  - <PERSON> <<EMAIL>>

description:
  The MediaTek's MT65xx Pin controller is used to control SoC pins.

properties:
  compatible:
    enum:
      - mediatek,mt2701-pinctrl
      - mediatek,mt2712-pinctrl
      - mediatek,mt6397-pinctrl
      - mediatek,mt7623-pinctrl
      - mediatek,mt8127-pinctrl
      - mediatek,mt8135-pinctrl
      - mediatek,mt8167-pinctrl
      - mediatek,mt8173-pinctrl
      - mediatek,mt8516-pinctrl

  reg:
    maxItems: 1

  pins-are-numbered:
    $ref: /schemas/types.yaml#/definitions/flag
    description:
      Specify the subnodes are using numbered pinmux to specify pins. (UNUSED)
    deprecated: true

  gpio-controller: true

  "#gpio-cells":
    const: 2
    description:
      Number of cells in GPIO specifier. Since the generic GPIO binding is used,
      the amount of cells must be specified as 2. See the below mentioned gpio
      binding representation for description of particular cells.

  mediatek,pctl-regmap:
    $ref: /schemas/types.yaml#/definitions/phandle-array
    items:
      maxItems: 1
    minItems: 1
    maxItems: 2
    description:
      Should be phandles of the syscfg node.

  interrupt-controller: true

  interrupts:
    minItems: 1
    maxItems: 3

  "#interrupt-cells":
    const: 2

required:
  - compatible
  - gpio-controller
  - "#gpio-cells"

allOf:
  - $ref: pinctrl.yaml#

patternProperties:
  'pins$':
    type: object
    additionalProperties: false
    patternProperties:
      '(^pins|pins?$)':
        type: object
        additionalProperties: false
        description:
          A pinctrl node should contain at least one subnodes representing the
          pinctrl groups available on the machine. Each subnode will list the
          pins it needs, and how they should be configured, with regard to muxer
          configuration, pullups, drive strength, input enable/disable and input
          schmitt.
        $ref: /schemas/pinctrl/pincfg-node.yaml

        properties:
          pinmux:
            description:
              Integer array, represents gpio pin number and mux setting.
              Supported pin number and mux varies for different SoCs, and are
              defined as macros in dt-bindings/pinctrl/<soc>-pinfunc.h directly.

          bias-disable: true

          bias-pull-up:
            description:
              Besides generic pinconfig options, it can be used as the pull up
              settings for 2 pull resistors, R0 and R1. User can configure those
              special pins. Some macros have been defined for this usage, such
              as MTK_PUPD_SET_R1R0_00. See dt-bindings/pinctrl/mt65xx.h for
              valid arguments.

          bias-pull-down: true

          input-enable: true

          input-disable: true

          output-low: true

          output-high: true

          input-schmitt-enable: true

          input-schmitt-disable: true

          drive-strength:
            description:
              Can support some arguments, such as MTK_DRIVE_4mA, MTK_DRIVE_6mA,
              etc. See dt-bindings/pinctrl/mt65xx.h for valid arguments.

        required:
          - pinmux

additionalProperties: false

examples:
  - |
    #include <dt-bindings/interrupt-controller/irq.h>
    #include <dt-bindings/interrupt-controller/arm-gic.h>
    #include <dt-bindings/pinctrl/mt8135-pinfunc.h>

    soc {
        #address-cells = <2>;
        #size-cells = <2>;

        pinctrl@1c20800 {
            compatible = "mediatek,mt8135-pinctrl";
            reg = <0 0x1000B000 0 0x1000>;
            mediatek,pctl-regmap = <&syscfg_pctl_a>, <&syscfg_pctl_b>;
            gpio-controller;
            #gpio-cells = <2>;
            interrupt-controller;
            #interrupt-cells = <2>;
            interrupts = <GIC_SPI 116 IRQ_TYPE_LEVEL_HIGH>,
                         <GIC_SPI 117 IRQ_TYPE_LEVEL_HIGH>,
                         <GIC_SPI 118 IRQ_TYPE_LEVEL_HIGH>;

            i2c0_pins_a: i2c0-pins {
                pins1 {
                    pinmux = <MT8135_PIN_100_SDA0__FUNC_SDA0>,
                             <MT8135_PIN_101_SCL0__FUNC_SCL0>;
                    bias-disable;
                };
            };

            i2c1_pins_a: i2c1-pins {
                pins {
                    pinmux = <MT8135_PIN_195_SDA1__FUNC_SDA1>,
                             <MT8135_PIN_196_SCL1__FUNC_SCL1>;
                    bias-pull-up = <MTK_PUPD_SET_R1R0_01>;
                };
            };

            i2c2_pins_a: i2c2-pins {
                pins1 {
                    pinmux = <MT8135_PIN_193_SDA2__FUNC_SDA2>;
                    bias-pull-down;
                };

                pins2 {
                    pinmux = <MT8135_PIN_49_WATCHDOG__FUNC_GPIO49>;
                    bias-pull-up;
                };
            };
        };
    };
