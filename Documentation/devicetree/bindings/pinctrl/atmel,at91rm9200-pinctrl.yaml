# SPDX-License-Identifier: (GPL-2.0-only OR BSD-2-Clause)
%YAML 1.2
---
$id: http://devicetree.org/schemas/pinctrl/atmel,at91rm9200-pinctrl.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: Microchip PIO3 Pinmux Controller

maintainers:
  - Manikandan <PERSON> <<EMAIL>>

description:
  The AT91 Pinmux Controller, enables the IC to share one PAD to several
  functional blocks. The sharing is done by multiplexing the PAD input/output
  signals. For each PAD there are up to 8 muxing options (called periph modes).
  Since different modules require different PAD settings (like pull up, keeper,
  etc) the controller controls also the PAD settings parameters.

properties:
  compatible:
    oneOf:
      - items:
          - enum:
              - atmel,at91rm9200-pinctrl
              - atmel,at91sam9x5-pinctrl
              - atmel,sama5d3-pinctrl
              - microchip,sam9x60-pinctrl
          - const: simple-mfd
      - items:
          - enum:
              - microchip,sam9x7-pinctrl
          - const: microchip,sam9x60-pinctrl
          - const: simple-mfd

  '#address-cells':
    const: 1

  '#size-cells':
    const: 1

  ranges: true

  atmel,mux-mask:
    $ref: /schemas/types.yaml#/definitions/uint32-matrix
    description: |
      Array of mask (periph per bank) to describe if a pin can be
      configured in this periph mode. All the periph and bank need to
      be described.

      #How to create such array:

      Each column will represent the possible peripheral of the pinctrl
      Each line will represent a pio bank

      #Example:

      In at91sam9260.dtsi,
      Peripheral: 2 ( A and B)
      Bank: 3 (A, B and C)

      #    A          B
      0xffffffff 0xffc00c3b  # pioA
      0xffffffff 0x7fff3ccf  # pioB
      0xffffffff 0x007fffff  # pioC

      For each peripheral/bank we will describe in a u32 if a pin can be
      configured in it by putting 1 to the pin bit (1 << pin)

      Let's take the pioA on peripheral B whose value is 0xffc00c3b
      From the datasheet Table 10-2.
      Peripheral B
      PA0     MCDB0
      PA1     MCCDB
      PA2
      PA3     MCDB3
      PA4     MCDB2
      PA5     MCDB1
      PA6
      PA7
      PA8
      PA9
      PA10    ETX2
      PA11    ETX3
      PA12
      PA13
      PA14
      PA15
      PA16
      PA17
      PA18
      PA19
      PA20
      PA21
      PA22    ETXER
      PA23    ETX2
      PA24    ETX3
      PA25    ERX2
      PA26    ERX3
      PA27    ERXCK
      PA28    ECRS
      PA29    ECOL
      PA30    RXD4
      PA31    TXD4

allOf:
  - $ref: pinctrl.yaml#

required:
  - compatible
  - ranges
  - "#address-cells"
  - "#size-cells"
  - atmel,mux-mask

patternProperties:
  'gpio@[0-9a-f]+$':
    $ref: /schemas/gpio/atmel,at91rm9200-gpio.yaml
    unevaluatedProperties: false

additionalProperties:
  type: object
  additionalProperties:
    type: object
    additionalProperties: false

    properties:
      atmel,pins:
        $ref: /schemas/types.yaml#/definitions/uint32-matrix
        description: |
          Each entry consists of 4 integers and represents the pins
          mux and config setting.The format is
          atmel,pins = <PIN_BANK PIN_BANK_NUM PERIPH CONFIG>.
          Supported pin number and mux varies for different SoCs, and
          are defined in <include/dt-bindings/pinctrl/at91.h>.
          items:
            items:
              - description:
                  Pin bank
              - description:
                  Pin bank index
              - description:
                  Peripheral function
              - description:
                  Pad configuration

examples:
  - |
    #include <dt-bindings/clock/at91.h>
    #include <dt-bindings/interrupt-controller/irq.h>
    #include <dt-bindings/pinctrl/at91.h>

    pinctrl@fffff400 {
        #address-cells = <1>;
        #size-cells = <1>;
        compatible = "atmel,at91rm9200-pinctrl", "simple-mfd";
        ranges = <0xfffff400 0xfffff400 0x600>;

        atmel,mux-mask = <
          /*    A         B     */
          0xffffffff 0xffc00c3b  /* pioA */
          0xffffffff 0x7fff3ccf  /* pioB */
          0xffffffff 0x007fffff  /* pioC */
          >;

        dbgu {
            dbgu-0 {
                atmel,pins =
                  <AT91_PIOB 14 AT91_PERIPH_A AT91_PINCTRL_PULL_UP
                   AT91_PIOB 15 AT91_PERIPH_A AT91_PINCTRL_NONE>;
            };
        };

        gpio@fffff400 {
            compatible = "atmel,at91rm9200-gpio";
            reg = <0xfffff400 0x200>;
            interrupts = <2 IRQ_TYPE_LEVEL_HIGH 1>;
            #gpio-cells = <2>;
            gpio-controller;
            interrupt-controller;
            #interrupt-cells = <2>;
            clocks = <&pmc PMC_TYPE_PERIPHERAL 2>;
        };
    };
...
