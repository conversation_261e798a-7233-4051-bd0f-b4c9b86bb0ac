# SPDX-License-Identifier: (GPL-2.0-only OR BSD-2-Clause)
%YAML 1.2
---
$id: http://devicetree.org/schemas/pinctrl/mediatek,mt7622-pinctrl.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: MediaTek MT7622 Pin Controller

maintainers:
  - <PERSON> <<EMAIL>>

description:
  The MediaTek's MT7622 Pin controller is used to control SoC pins.

properties:
  compatible:
    enum:
      - mediatek,mt7622-pinctrl
      - mediatek,mt7629-pinctrl

  reg:
    maxItems: 1

  reg-names:
    items:
      - const: eint

  gpio-controller: true

  "#gpio-cells":
    const: 2
    description:
      Number of cells in GPIO specifier. Since the generic GPIO binding is used,
      the amount of cells must be specified as 2. See the below mentioned gpio
      binding representation for description of particular cells.

  gpio-ranges:
    maxItems: 1

  interrupt-controller: true

  interrupts:
    maxItems: 1

  "#interrupt-cells":
    const: 2

allOf:
  - $ref: pinctrl.yaml#

required:
  - compatible
  - reg
  - gpio-controller
  - "#gpio-cells"

if:
  required:
    - interrupt-controller
then:
  required:
    - reg-names
    - interrupts
    - "#interrupt-cells"

patternProperties:
  '-pins(-[a-z]+)?$':
    type: object
    additionalProperties: false
    patternProperties:
      '^mux(-|$)':
        type: object
        additionalProperties: false
        description:
          pinmux configuration nodes.
        $ref: /schemas/pinctrl/pinmux-node.yaml
        properties:
          function:
            description:
              A string containing the name of the function to mux to the group.
            enum: [antsel, emmc, eth, i2c, i2s, ir, led, flash, pcie, pmic, pwm,
                   sd, spi, tdm, uart, watchdog, wifi]

          groups:
            description:
              An array of strings. Each string contains the name of a group.

          drive-strength:
            enum: [4, 8, 12, 16]

        required:
          - groups
          - function

        allOf:
          - if:
              properties:
                function:
                  const: antsel
            then:
              properties:
                groups:
                  items:
                    enum: [antsel0, antsel1, antsel2, antsel3, antsel4, antsel5,
                           antsel6, antsel7, antsel8, antsel9, antsel10,
                           antsel11, antsel12, antsel13, antsel14, antsel15,
                           antsel16, antsel17, antsel18, antsel19, antsel20,
                           antsel21, antsel22, antsel23, antsel24, antsel25,
                           antsel26, antsel27, antsel28, antsel29]
          - if:
              properties:
                function:
                  const: emmc
            then:
              properties:
                groups:
                  items:
                    enum: [emmc, emmc_rst]
          - if:
              properties:
                function:
                  const: eth
            then:
              properties:
                groups:
                  items:
                    enum: [esw, esw_p0_p1, esw_p2_p3_p4, rgmii_via_esw,
                           rgmii_via_gmac1, rgmii_via_gmac2, mdc_mdio]
          - if:
              properties:
                function:
                  const: i2c
            then:
              properties:
                groups:
                  enum: [i2c0, i2c_0, i2c_1, i2c1_0, i2c1_1, i2c1_2, i2c2_0,
                         i2c2_1, i2c2_2]
          - if:
              properties:
                function:
                  const: i2s
            then:
              properties:
                groups:
                  items:
                    enum: [i2s_in_mclk_bclk_ws, i2s1_in_data, i2s2_in_data,
                           i2s3_in_data, i2s4_in_data, i2s_out_mclk_bclk_ws,
                           i2s1_out_data, i2s2_out_data, i2s3_out_data,
                           i2s4_out_data]
          - if:
              properties:
                function:
                  const: ir
            then:
              properties:
                groups:
                  enum: [ir_0_tx, ir_1_tx, ir_2_tx, ir_0_rx, ir_1_rx, ir_2_rx]
          - if:
              properties:
                function:
                  const: led
            then:
              properties:
                groups:
                  enum: [ephy_leds, ephy0_led, ephy1_led, ephy2_led, ephy3_led,
                         ephy4_led, wled, wf2g_led, wf5g_led]
          - if:
              properties:
                function:
                  const: flash
            then:
              properties:
                groups:
                  enum: [par_nand, snfi, spi_nor]
          - if:
              properties:
                function:
                  const: pcie
            then:
              properties:
                groups:
                  items:
                    enum: [pcie0_0_waken, pcie0_1_waken, pcie1_0_waken,
                           pcie0_0_clkreq, pcie0_1_clkreq, pcie1_0_clkreq,
                           pcie0_pad_perst, pcie1_pad_perst, pcie_pereset,
                           pcie_wake, pcie_clkreq]
          - if:
              properties:
                function:
                  const: pmic
            then:
              properties:
                groups:
                  enum: [pmic_bus]
          - if:
              properties:
                function:
                  const: pwm
            then:
              properties:
                groups:
                  items:
                    enum: [pwm_ch1_0, pwm_ch1_1, pwm_ch1_2, pwm_ch2_0, pwm_ch2_1,
                           pwm_ch2_2, pwm_ch3_0, pwm_ch3_1, pwm_ch3_2, pwm_ch4_0,
                           pwm_ch4_1, pwm_ch4_2, pwm_ch4_3, pwm_ch5_0, pwm_ch5_1,
                           pwm_ch5_2, pwm_ch6_0, pwm_ch6_1, pwm_ch6_2, pwm_ch6_3,
                           pwm_ch7_0, pwm_0, pwm_1]
          - if:
              properties:
                function:
                  const: sd
            then:
              properties:
                groups:
                  enum: [sd_0, sd_1]
          - if:
              properties:
                function:
                  const: spi
            then:
              properties:
                groups:
                  enum: [spic0_0, spic0_1, spic1_0, spic1_1, spic2_0_wp_hold,
                         spic2_0, spi_0, spi_1, spi_wp, spi_hold]
          - if:
              properties:
                function:
                  const: tdm
            then:
              properties:
                groups:
                  enum: [tdm_0_out_mclk_bclk_ws, tdm_0_in_mclk_bclk_ws,
                         tdm_0_out_data, tdm_0_in_data, tdm_1_out_mclk_bclk_ws,
                         tdm_1_in_mclk_bclk_ws, tdm_1_out_data, tdm_1_in_data]
          - if:
              properties:
                function:
                  const: uart
            then:
              properties:
                groups:
                  enum: [uart0_0_tx_rx, uart1_0_tx_rx, uart1_0_rts_cts,
                         uart1_1_tx_rx, uart1_1_rts_cts, uart2_0_tx_rx,
                         uart2_0_rts_cts, uart2_1_tx_rx, uart2_1_rts_cts,
                         uart2_2_tx_rx, uart2_2_rts_cts, uart2_3_tx_rx,
                         uart3_0_tx_rx, uart3_1_tx_rx, uart3_1_rts_cts,
                         uart4_0_tx_rx, uart4_1_tx_rx, uart4_1_rts_cts,
                         uart4_2_tx_rx, uart4_2_rts_cts, uart0_txd_rxd,
                         uart1_0_txd_rxd, uart1_0_cts_rts, uart1_1_txd_rxd,
                         uart1_1_cts_rts, uart2_0_txd_rxd, uart2_0_cts_rts,
                         uart2_1_txd_rxd, uart2_1_cts_rts]
          - if:
              properties:
                function:
                  const: watchdog
            then:
              properties:
                groups:
                  enum: [watchdog]
          - if:
              properties:
                function:
                  const: wifi
            then:
              properties:
                groups:
                  enum: [wf0_2g, wf0_5g]

      '^conf(-|$)':
        type: object
        additionalProperties: false
        description:
          pinconf configuration nodes.
        $ref: /schemas/pinctrl/pincfg-node.yaml

        properties:
          groups:
            description:
              An array of strings. Each string contains the name of a group.
              Valid values are the same as the pinmux node.

          pins:
            description:
              An array of strings. Each string contains the name of a pin.
            items:
              enum: [GPIO_A, I2S1_IN, I2S1_OUT, I2S_BCLK, I2S_WS, I2S_MCLK, TXD0,
                     RXD0, SPI_WP, SPI_HOLD, SPI_CLK, SPI_MOSI, SPI_MISO, SPI_CS,
                     I2C_SDA, I2C_SCL, I2S2_IN, I2S3_IN, I2S4_IN, I2S2_OUT,
                     I2S3_OUT, I2S4_OUT, GPIO_B, MDC, MDIO, G2_TXD0, G2_TXD1,
                     G2_TXD2, G2_TXD3, G2_TXEN, G2_TXC, G2_RXD0, G2_RXD1, G2_RXD2,
                     G2_RXD3, G2_RXDV, G2_RXC, NCEB, NWEB, NREB, NDL4, NDL5, NDL6,
                     NDL7, NRB, NCLE, NALE, NDL0, NDL1, NDL2, NDL3, MDI_TP_P0,
                     MDI_TN_P0, MDI_RP_P0, MDI_RN_P0, MDI_TP_P1, MDI_TN_P1,
                     MDI_RP_P1, MDI_RN_P1, MDI_RP_P2, MDI_RN_P2, MDI_TP_P2,
                     MDI_TN_P2, MDI_TP_P3, MDI_TN_P3, MDI_RP_P3, MDI_RN_P3,
                     MDI_RP_P4, MDI_RN_P4, MDI_TP_P4, MDI_TN_P4, PMIC_SCL,
                     PMIC_SDA, SPIC1_CLK, SPIC1_MOSI, SPIC1_MISO, SPIC1_CS,
                     GPIO_D, WATCHDOG, RTS3_N, CTS3_N, TXD3, RXD3, PERST0_N,
                     PERST1_N, WLED_N, EPHY_LED0_N, AUXIN0, AUXIN1, AUXIN2,
                     AUXIN3, TXD4, RXD4, RTS4_N, CST4_N, PWM1, PWM2, PWM3, PWM4,
                     PWM5, PWM6, PWM7, GPIO_E, TOP_5G_CLK, TOP_5G_DATA,
                     WF0_5G_HB0, WF0_5G_HB1, WF0_5G_HB2, WF0_5G_HB3, WF0_5G_HB4,
                     WF0_5G_HB5, WF0_5G_HB6, XO_REQ, TOP_RST_N, SYS_WATCHDOG,
                     EPHY_LED0_N_JTDO, EPHY_LED1_N_JTDI, EPHY_LED2_N_JTMS,
                     EPHY_LED3_N_JTCLK, EPHY_LED4_N_JTRST_N, WF2G_LED_N,
                     WF5G_LED_N, GPIO_9, GPIO_10, GPIO_11, GPIO_12, UART1_TXD,
                     UART1_RXD, UART1_CTS, UART1_RTS, UART2_TXD, UART2_RXD,
                     UART2_CTS, UART2_RTS, SMI_MDC, SMI_MDIO, PCIE_PERESET_N,
                     PWM_0, GPIO_0, GPIO_1, GPIO_2, GPIO_3, GPIO_4, GPIO_5,
                     GPIO_6, GPIO_7, GPIO_8, UART0_TXD, UART0_RXD, TOP_2G_CLK,
                     TOP_2G_DATA, WF0_2G_HB0, WF0_2G_HB1, WF0_2G_HB2, WF0_2G_HB3,
                     WF0_2G_HB4, WF0_2G_HB5, WF0_2G_HB6]

          bias-disable: true

          bias-pull-up: true

          bias-pull-down: true

          input-enable: true

          input-disable: true

          output-enable: true

          output-low: true

          output-high: true

          input-schmitt-enable: true

          input-schmitt-disable: true

          drive-strength:
            enum: [4, 8, 12, 16]

          slew-rate:
            enum: [0, 1]

          mediatek,tdsel:
            description:
              An integer describing the steps for output level shifter duty
              cycle when asserted (high pulse width adjustment). Valid arguments
              are from 0 to 15.
            $ref: /schemas/types.yaml#/definitions/uint32

          mediatek,rdsel:
            description:
              An integer describing the steps for input level shifter duty cycle
              when asserted (high pulse width adjustment). Valid arguments are
              from 0 to 63.
            $ref: /schemas/types.yaml#/definitions/uint32

        required:
          - pins

additionalProperties: false

examples:
  - |
    #include <dt-bindings/interrupt-controller/irq.h>
    #include <dt-bindings/interrupt-controller/arm-gic.h>

    soc {
        #address-cells = <2>;
        #size-cells = <2>;

        pio: pinctrl@10211000 {
            compatible = "mediatek,mt7622-pinctrl";
            reg = <0 0x10211000 0 0x1000>;
            gpio-controller;
            #gpio-cells = <2>;

            pinctrl_eth_default: eth-pins {
                mux-mdio {
                    groups = "mdc_mdio";
                    function = "eth";
                    drive-strength = <12>;
                };

                mux-gmac2 {
                    groups = "rgmii_via_gmac2";
                    function = "eth";
                    drive-strength = <12>;
                };

                mux-esw {
                    groups = "esw";
                    function = "eth";
                    drive-strength = <8>;
                };

                conf-mdio {
                    pins = "MDC";
                    bias-pull-up;
                };
            };
        };
    };
