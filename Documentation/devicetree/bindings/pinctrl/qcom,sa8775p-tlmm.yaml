# SPDX-License-Identifier: (GPL-2.0-only OR BSD-2-Clause)
%YAML 1.2
---
$id: http://devicetree.org/schemas/pinctrl/qcom,sa8775p-tlmm.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: Qualcomm Technologies, Inc. SA8775P TLMM block

maintainers:
  - <PERSON><PERSON><PERSON> <bartosz.go<PERSON><PERSON>@linaro.org>

description: |
  Top Level Mode Multiplexer pin controller in Qualcomm SA8775P SoC.

allOf:
  - $ref: /schemas/pinctrl/qcom,tlmm-common.yaml#

properties:
  compatible:
    oneOf:
      - items:
          - enum:
              - qcom,sa8255p-tlmm
          - const: qcom,sa8775p-tlmm
      - items:
          - const: qcom,sa8775p-tlmm

  reg:
    maxItems: 1

  interrupts:
    maxItems: 1

  gpio-reserved-ranges:
    minItems: 1
    maxItems: 74

  gpio-line-names:
    maxItems: 148

patternProperties:
  "-state$":
    oneOf:
      - $ref: "#/$defs/qcom-sa8775p-tlmm-state"
      - patternProperties:
          "-pins$":
            $ref: "#/$defs/qcom-sa8775p-tlmm-state"
        additionalProperties: false

$defs:
  qcom-sa8775p-tlmm-state:
    type: object
    description:
      Pinctrl node's client devices use subnodes for desired pin configuration.
      Client device subnodes use below standard properties.
    $ref: qcom,tlmm-common.yaml#/$defs/qcom-tlmm-state
    unevaluatedProperties: false

    properties:
      pins:
        description:
          List of gpio pins affected by the properties specified in this
          subnode.
        items:
          oneOf:
            - pattern: "^gpio([0-9]|[1-9][0-9]|1[0-3][0-9]|14[0-7])$"
            - enum: [ sdc1_clk, sdc1_cmd, sdc1_data, sdc1_rclk, ufs_reset ]
        minItems: 1
        maxItems: 16

      function:
        description:
          Specify the alternative function to be configured for the specified
          pins.

        enum: [ atest_char, atest_usb2, audio_ref, cam_mclk, cci_async, cci_i2c,
                cci_timer0, cci_timer1, cci_timer2, cci_timer3, cci_timer4,
                cci_timer5, cci_timer6, cci_timer7, cci_timer8, cci_timer9,
                cri_trng, cri_trng0, cri_trng1, dbg_out, ddr_bist, ddr_pxi0,
                ddr_pxi1, ddr_pxi2, ddr_pxi3, ddr_pxi4, ddr_pxi5, edp0_hot,
                edp0_lcd, edp1_hot, edp1_lcd, edp2_hot, edp2_lcd, edp3_hot,
                edp3_lcd, egpio, emac0_mcg0, emac0_mcg1, emac0_mcg2, emac0_mcg3,
                emac0_mdc, emac0_mdio, emac0_ptp_aux, emac0_ptp_pps, emac1_mcg0,
                emac1_mcg1, emac1_mcg2, emac1_mcg3, emac1_mdc, emac1_mdio,
                emac1_ptp_aux, emac1_ptp_pps, gcc_gp1, gcc_gp2, gcc_gp3,
                gcc_gp4, gcc_gp5, gpio, hs0_mi2s, hs1_mi2s, hs2_mi2s, ibi_i3c,
                jitter_bist, mdp0_vsync0, mdp0_vsync1, mdp0_vsync2, mdp0_vsync3,
                mdp0_vsync4, mdp0_vsync5, mdp0_vsync6, mdp0_vsync7, mdp0_vsync8,
                mdp1_vsync0, mdp1_vsync1, mdp1_vsync2, mdp1_vsync3, mdp1_vsync4,
                mdp1_vsync5, mdp1_vsync6, mdp1_vsync7, mdp1_vsync8, mdp_vsync,
                mi2s1_data0, mi2s1_data1, mi2s1_sck, mi2s1_ws, mi2s2_data0,
                mi2s2_data1, mi2s2_sck, mi2s2_ws, mi2s_mclk0, mi2s_mclk1,
                pcie0_clkreq, pcie1_clkreq, phase_flag, pll_bist, pll_clk,
                prng_rosc0, prng_rosc1, prng_rosc2, prng_rosc3, qdss_cti,
                qdss_gpio, qup0_se0, qup0_se1, qup0_se2, qup0_se3, qup0_se4,
                qup0_se5, qup1_se0, qup1_se1, qup1_se2, qup1_se3, qup1_se4,
                qup1_se5, qup1_se6, qup2_se0, qup2_se1, qup2_se2, qup2_se3,
                qup2_se4, qup2_se5, qup2_se6, qup3_se0, sailss_emac0,
                sailss_ospi, sail_top, sgmii_phy, tb_trig, tgu_ch0, tgu_ch1,
                tgu_ch2, tgu_ch3, tgu_ch4, tgu_ch5, tsense_pwm1, tsense_pwm2,
                tsense_pwm3, tsense_pwm4, usb2phy_ac, vsense_trigger ]

    required:
      - pins

required:
  - compatible
  - reg

unevaluatedProperties: false

examples:
  - |
    #include <dt-bindings/interrupt-controller/arm-gic.h>

    tlmm: pinctrl@f000000 {
        compatible = "qcom,sa8775p-tlmm";
        reg = <0xf000000 0x1000000>;
        interrupts = <GIC_SPI 208 IRQ_TYPE_LEVEL_HIGH>;
        gpio-controller;
        #gpio-cells = <2>;
        interrupt-controller;
        #interrupt-cells = <2>;
        gpio-ranges = <&tlmm 0 0 148>;

        qup-uart10-state {
            pins = "gpio46", "gpio47";
            function = "qup1_se3";
        };
    };
...
