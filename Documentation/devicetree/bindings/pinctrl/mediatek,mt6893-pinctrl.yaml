# SPDX-License-Identifier: (GPL-2.0-only OR BSD-2-Clause)
%YAML 1.2
---
$id: http://devicetree.org/schemas/pinctrl/mediatek,mt6893-pinctrl.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: MediaTek MT6893 Pin Controller

maintainers:
  - AngeloGioac<PERSON><PERSON> <<EMAIL>>

description:
  The MediaTek's MT6893 Pin controller is used to control SoC pins.

properties:
  compatible:
    const: mediatek,mt6893-pinctrl

  reg:
    items:
      - description: pin controller base
      - description: rm group IO
      - description: bm group IO
      - description: lm group IO
      - description: lb group IO
      - description: rt group IO
      - description: lt group IO
      - description: tm group IO
      - description: External Interrupt (EINT) controller base

  reg-names:
    items:
      - const: base
      - const: rm
      - const: bm
      - const: lm
      - const: lb
      - const: rt
      - const: lt
      - const: tm
      - const: eint

  gpio-controller: true

  '#gpio-cells':
    description:
      Number of cells in GPIO specifier. Since the generic GPIO binding is used,
      the amount of cells must be specified as 2. See the below mentioned gpio
      binding representation for description of particular cells.
    const: 2

  gpio-ranges:
    maxItems: 1

  gpio-line-names: true

  interrupts:
    description: The interrupt outputs to sysirq
    maxItems: 1

  interrupt-controller: true

  '#interrupt-cells':
    const: 2

# PIN CONFIGURATION NODES
patternProperties:
  '-pins$':
    type: object
    additionalProperties: false

    patternProperties:
      '^pins':
        type: object
        allOf:
          - $ref: /schemas/pinctrl/pincfg-node.yaml
          - $ref: /schemas/pinctrl/pinmux-node.yaml
        description:
          A pinctrl node should contain at least one subnodes representing the
          pinctrl groups available on the machine. Each subnode will list the
          pins it needs, and how they should be configured, with regard to muxer
          configuration, pullups, drive strength, input enable/disable and input
          schmitt.

        properties:
          pinmux:
            description:
              Integer array, represents gpio pin number and mux setting.
              Supported pin number and mux are defined as macros in
              arch/arm64/boot/dts/mediatek/mt8196-pinfunc.h for this SoC.

          drive-strength:
            enum: [2, 4, 6, 8, 10, 12, 14, 16]

          drive-strength-microamp:
            enum: [125, 250, 500, 1000]

          bias-pull-down:
            oneOf:
              - type: boolean
              - enum: [75000, 5000]
                description: Pull down RSEL type resistance values (in ohms)
            description:
              For normal pull down type there is no need to specify a resistance
              value, hence this can be specified as a boolean property.
              For RSEL pull down type a resistance value (in ohms) can be added.

          bias-pull-up:
            oneOf:
              - type: boolean
              - enum: [10000, 5000, 4000, 3000]
                description: Pull up RSEL type resistance values (in ohms)
            description:
              For normal pull up type there is no need to specify a resistance
              value, hence this can be specified as a boolean property.
              For RSEL pull up type a resistance value (in ohms) can be added.

          bias-disable: true

          output-high: true

          output-low: true

          input-enable: true

          input-disable: true

          input-schmitt-enable: true

          input-schmitt-disable: true

        required:
          - pinmux

        additionalProperties: false

required:
  - compatible
  - reg
  - interrupts
  - interrupt-controller
  - '#interrupt-cells'
  - gpio-controller
  - '#gpio-cells'
  - gpio-ranges

additionalProperties: false

examples:
  - |
    #include <dt-bindings/interrupt-controller/arm-gic.h>
    #include <dt-bindings/pinctrl/mt65xx.h>
    #define PINMUX_GPIO0__FUNC_GPIO0 (MTK_PIN_NO(0) | 0)
    #define PINMUX_GPIO99__FUNC_SCL0 (MTK_PIN_NO(99) | 1)
    #define PINMUX_GPIO100__FUNC_SDA0 (MTK_PIN_NO(100) | 1)

    pio: pinctrl@10005000 {
        compatible = "mediatek,mt6893-pinctrl";
        reg = <0x10005000 0x1000>,
              <0x11c20000 0x0200>,
              <0x11d10000 0x0200>,
              <0x11e20000 0x0200>,
              <0x11e70000 0x0200>,
              <0x11ea0000 0x0200>,
              <0x11f20000 0x0200>,
              <0x11f30000 0x0200>,
              <0x1100b000 0x1000>;
        reg-names = "base", "rm", "bm", "lm", "lb", "rt",
                    "lt", "tm", "eint";
        gpio-controller;
        #gpio-cells = <2>;
        gpio-ranges = <&pio 0 0 220>;
        interrupt-controller;
        interrupts = <GIC_SPI 212 IRQ_TYPE_LEVEL_HIGH 0>;
        #interrupt-cells = <2>;

        gpio-pins {
            pins {
                pinmux = <PINMUX_GPIO0__FUNC_GPIO0>;
                bias-pull-up = <4000>;
                drive-strength = <6>;
            };
        };

        i2c0-pins {
            pins-bus {
                pinmux = <PINMUX_GPIO99__FUNC_SCL0>,
                         <PINMUX_GPIO100__FUNC_SDA0>;
                bias-pull-down = <75000>;
                drive-strength-microamp = <1000>;
            };
        };
    };
