# SPDX-License-Identifier: GPL-2.0-only OR BSD-2-Clause
%YAML 1.2
---
$id: http://devicetree.org/schemas/pinctrl/brcm,bcm21664-pinctrl.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: Broadcom BCM21664 pin controller

maintainers:
  - <PERSON><PERSON><PERSON> <<EMAIL>>
  - <PERSON> <<EMAIL>>
  - <PERSON> <<EMAIL>>

allOf:
  - $ref: pinctrl.yaml#

properties:
  compatible:
    const: brcm,bcm21664-pinctrl

  reg:
    maxItems: 1

patternProperties:
  '-pins$':
    type: object
    additionalProperties: false

    patternProperties:
      '-grp[0-9]$':
        type: object

        properties:
          pins:
            description:
              Specifies the name(s) of one or more pins to be configured by
              this node.
            items:
              enum: [ adcsyn, batrm, bsc1clk, bsc1dat, camcs0, camcs1, clk32k,
                      clk_cx8, dclk1, dclk4, dclkreq1, dclkreq4, dmic0clk,
                      dmic0dq, dsi0te, gpio00, gpio01, gpio02, gpio03, gpio04,
                      gpio05, gpio06, gpio07, gpio08, gpio09, gpio10, gpio11,
                      gpio12, gpio13, gpio14, gpio15, gpio16, gpio17, gpio18,
                      gpio19, gpio20, gpio21, gpio22, gpio23, gpio24, gpio25,
                      gpio26, gpio27, gpio28, gpio32, gpio33, gpio34, gpio93,
                      gpio94, gps_calreq, gps_hostreq, gps_pablank, gps_tmark,
                      icusbdm, icusbdp, lcdcs0, lcdres, lcdscl, lcdsda, lcdte,
                      mdmgpio00, mdmgpio01, mdmgpio02, mdmgpio03, mdmgpio04,
                      mdmgpio05, mdmgpio06, mdmgpio07, mdmgpio08, mmc0ck,
                      mmc0cmd, mmc0dat0, mmc0dat1, mmc0dat2, mmc0dat3, mmc0dat4,
                      mmc0dat5, mmc0dat6, mmc0dat7, mmc0rst, mmc1ck, mmc1cmd,
                      mmc1dat0, mmc1dat1, mmc1dat2, mmc1dat3, mmc1dat4,
                      mmc1dat5, mmc1dat6, mmc1dat7, mmc1rst, pc1, pc2, pmbscclk,
                      pmbscdat, pmuint, resetn, rfst2g_mtsloten3g,
                      rtxdata2g_txdata3g1, rtxen2g_txdata3g2, rxdata3g0,
                      rxdata3g1, rxdata3g2, sdck, sdcmd, sddat0, sddat1, sddat2,
                      sddat3, simclk, simdat, simdet, simrst, spi0clk, spi0fss,
                      spi0rxd, spi0txd, sri_c, sri_d, sri_e, sspck, sspdi,
                      sspdo, sspsyn, stat1, stat2, swclktck, swdiotms, sysclken,
                      tdi, tdo, testmode, traceclk, tracedt00, tracedt01,
                      tracedt02, tracedt03, tracedt04, tracedt05, tracedt06,
                      tracedt07, tracedt08, tracedt09, tracedt10, tracedt11,
                      tracedt12, tracedt13, tracedt14, tracedt15, trstb,
                      txdata3g0, ubctsn, ubrtsn, ubrx, ubtx ]

          function:
            description:
              Specifies the pin mux selection.
            enum: [ alt1, alt2, alt3, alt4, alt5, alt6 ]

          bias-disable: true

          bias-pull-up:
            type: boolean

          bias-pull-down:
            type: boolean

          slew-rate:
            description: |
              Meaning depends on configured pin mux:
                bsc*clk/pmbscclk or bsc*dat/pmbscdat or gpio16/gpio17:
                  0: Standard (100 kbps) & Fast (400 kbps) mode
                  1: Highspeed (3.4 Mbps) mode
                Otherwise:
                  0: fast slew rate
                  1: normal slew rate

          drive-strength:
            enum: [ 2, 4, 6, 8, 10, 12, 14, 16 ]

          input-enable: true
          input-disable: true

          input-schmitt-enable: true
          input-schmitt-disable: true

        required:
          - pins

        additionalProperties: false

        allOf:
          - $ref: pincfg-node.yaml#
          # Limitations for I2C pins
          - if:
              properties:
                pins:
                  contains:
                    enum: [ bsc1clk, bsc1dat, gpio16, gpio17, pmbscclk,
                            pmbscdat ]
            then:
              properties:
                drive-strength: false
                bias-pull-down: false
                input-schmitt-enable: false
                input-schmitt-disable: false


required:
  - compatible
  - reg

unevaluatedProperties: false

examples:
  - |
    pinctrl@35004800 {
      compatible = "brcm,bcm21664-pinctrl";
      reg = <0x35004800 0x7f0>;

      dev-a-active-pins {
        /* group node defining 1 standard pin */
        std-grp0 {
          pins = "gpio00";
          function = "alt1";
          input-schmitt-enable;
          bias-disable;
          slew-rate = <1>;
          drive-strength = <4>;
        };

        /* group node defining 2 I2C pins */
        i2c-grp0 {
          pins = "bsc1clk", "bsc1dat";
          function = "alt2";
          bias-pull-up;
          input-enable;
        };
      };
    };
...
