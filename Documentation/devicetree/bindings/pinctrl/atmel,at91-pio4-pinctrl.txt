* Atmel PIO4 Controller

The Atmel PIO4 controller is used to select the function of a pin and to
configure it.

Required properties:
- compatible:
	"atmel,sama5d2-pinctrl"
	"microchip,sama7d65-pinctrl", "microchip,sama7g5-pinctrl"
	"microchip,sama7g5-pinctrl"
- reg: base address and length of the PIO controller.
- interrupts: interrupt outputs from the controller, one for each bank.
- interrupt-controller: mark the device node as an interrupt controller.
- #interrupt-cells: should be two.
- gpio-controller: mark the device node as a gpio controller.
- #gpio-cells: should be two.

Please refer to ../gpio/gpio.txt and ../interrupt-controller/interrupts.txt for
a general description of GPIO and interrupt bindings.

Please refer to pinctrl-bindings.txt in this directory for details of the
common pinctrl bindings used by client devices.

Subnode format
Each node (or subnode) will list the pins it needs and how to configured these
pins.

	node {
		pinmux = <PIN_NUMBER_PINMUX>;
		GENERIC_PINCONFIG;
	};

Required properties:
- pinmux: integer array. Each integer represents a pin number plus mux and
ioset settings. Use the macros from boot/dts/<soc>-pinfunc.h file to get the
right representation of the pin.

Optional properties:
- GENERIC_PINCONFIG: generic pinconfig options to use:
	- bias-disable, bias-pull-down, bias-pull-up, drive-open-drain,
	 drive-push-pull input-schmitt-enable, input-debounce, output-low,
	 output-high.
	- for microchip,sama7g5-pinctrl only:
		- slew-rate: 0 - disabled, 1 - enabled (default)
- atmel,drive-strength: 0 or 1 for low drive, 2 for medium drive and 3 for
high drive. The default value is low drive.

Example:

#include <sama5d2-pinfunc.h>

...
{
	pioA: pinctrl@fc038000 {
		compatible = "atmel,sama5d2-pinctrl";
		reg = <0xfc038000 0x600>;
		interrupts = <18 IRQ_TYPE_LEVEL_HIGH 7>,
			     <68 IRQ_TYPE_LEVEL_HIGH 7>,
			     <69 IRQ_TYPE_LEVEL_HIGH 7>,
			     <70 IRQ_TYPE_LEVEL_HIGH 7>;
		interrupt-controller;
		#interrupt-cells = <2>;
		gpio-controller;
		#gpio-cells = <2>;
		clocks = <&pioA_clk>;

		pinctrl_i2c0_default: i2c0_default {
			pinmux = <PIN_PD21__TWD0>,
				 <PIN_PD22__TWCK0>;
			bias-disable;
		};

		pinctrl_led_gpio_default: led_gpio_default {
			pinmux = <PIN_PB0>,
				 <PIN_PB5>;
			bias-pull-up;
			atmel,drive-strength = <ATMEL_PIO_DRVSTR_ME>;
		};

		pinctrl_sdmmc1_default: sdmmc1_default {
			cmd_data {
				pinmux = <PIN_PA28__SDMMC1_CMD>,
					 <PIN_PA18__SDMMC1_DAT0>,
					 <PIN_PA19__SDMMC1_DAT1>,
					 <PIN_PA20__SDMMC1_DAT2>,
					 <PIN_PA21__SDMMC1_DAT3>;
				bias-pull-up;
			};

			ck_cd {
				pinmux = <PIN_PA22__SDMMC1_CK>,
					 <PIN_PA30__SDMMC1_CD>;
				bias-disable;
			};
		};
		...
	};
};
...
