# SPDX-License-Identifier: (GPL-2.0-only OR BSD-2-Clause)
%YAML 1.2
---
$id: http://devicetree.org/schemas/platform/huawei,gaokun-ec.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: Huawei Matebook E Go Embedded Controller

maintainers:
  - <PERSON><PERSON><PERSON> <<EMAIL>>

description:
  Different from other Qualcomm Snapdragon sc8180x and sc8280xp-based
  machines, the Huawei Matebook E Go tablets use embedded controllers
  while others use a system called PMIC GLink which handles battery,
  UCSI, USB Type-C DP Alt Mode. In addition, Huawei's implementation
  also handles additional features, such as charging thresholds, FN
  lock, smart charging, tablet lid status, thermal sensors, and more.

properties:
  compatible:
    enum:
      - huawei,gaokun3-ec

  reg:
    const: 0x38

  '#address-cells':
    const: 1

  '#size-cells':
    const: 0

  interrupts:
    maxItems: 1

patternProperties:
  '^connector@[01]$':
    $ref: /schemas/connector/usb-connector.yaml#

    properties:
      reg:
        maxItems: 1

required:
  - compatible
  - reg
  - interrupts

additionalProperties: false

examples:
  - |
    #include <dt-bindings/interrupt-controller/irq.h>
    i2c {
        #address-cells = <1>;
        #size-cells = <0>;

        embedded-controller@38 {
            compatible = "huawei,gaokun3-ec";
            reg = <0x38>;

            interrupts-extended = <&tlmm 107 IRQ_TYPE_LEVEL_LOW>;

            #address-cells = <1>;
            #size-cells = <0>;

            connector@0 {
                compatible = "usb-c-connector";
                reg = <0>;
                power-role = "dual";
                data-role = "dual";

                ports {
                    #address-cells = <1>;
                    #size-cells = <0>;

                    port@0 {
                        reg = <0>;

                        ucsi0_ss_in: endpoint {
                            remote-endpoint = <&usb_0_qmpphy_out>;
                        };
                    };

                    port@1 {
                        reg = <1>;

                        ucsi0_sbu: endpoint {
                            remote-endpoint = <&usb0_sbu_mux>;
                        };
                    };
                };
            };

            connector@1 {
                compatible = "usb-c-connector";
                reg = <1>;
                power-role = "dual";
                data-role = "dual";

                ports {
                    #address-cells = <1>;
                    #size-cells = <0>;

                    port@0 {
                        reg = <0>;

                        ucsi1_ss_in: endpoint {
                            remote-endpoint = <&usb_1_qmpphy_out>;
                        };
                    };

                    port@1 {
                        reg = <1>;

                        ucsi1_sbu: endpoint {
                            remote-endpoint = <&usb1_sbu_mux>;
                        };
                    };
                };
            };
        };
    };
