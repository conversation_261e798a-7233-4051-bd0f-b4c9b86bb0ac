This part of the documentation inside Documentation/ABI directory
attempts to document the ABI between the Linux kernel and
userspace, and the relative stability of these interfaces.  Due to the
everchanging nature of Linux, and the differing maturity levels, these
interfaces should be used by userspace programs in different ways.

We have four different levels of ABI stability, as shown by the four
different subdirectories in this location.  Interfaces may change levels
of stability according to the rules described below.

The different levels of stability are:

  stable/
	This directory documents the interfaces that the developer has
	defined to be stable.  Userspace programs are free to use these
	interfaces with no restrictions, and backward compatibility for
	them will be guaranteed for at least 2 years.  Most interfaces
	(like syscalls) are expected to never change and always be
	available.

  testing/
	This directory documents interfaces that are felt to be stable,
	as the main development of this interface has been completed.
	The interface can be changed to add new features, but the
	current interface will not break by doing this, unless grave
	errors or security problems are found in them.  Userspace
	programs can start to rely on these interfaces, but they must be
	aware of changes that can occur before these interfaces move to
	be marked stable.  Programs that use these interfaces are
	strongly encouraged to add their name to the description of
	these interfaces, so that the kernel developers can easily
	notify them if any changes occur (see the description of the
	layout of the files below for details on how to do this.)

  obsolete/
	This directory documents interfaces that are still remaining in
	the kernel, but are marked to be removed at some later point in
	time.  The description of the interface will document the reason
	why it is obsolete and when it can be expected to be removed.

  removed/
	This directory contains a list of the old interfaces that have
	been removed from the kernel.

Every file in these directories will contain the following information:

What:		Short description of the interface
Date:		Date created
KernelVersion:	Kernel version this feature first showed up in.
Contact:	Primary contact for this interface (may be a mailing list)
Description:	Long description of the interface and how to use it.
Users:		All users of this interface who wish to be notified when
		it changes.  This is very important for interfaces in
		the "testing" stage, so that kernel developers can work
		with userspace developers to ensure that things do not
		break in ways that are unacceptable.  It is also
		important to get feedback for these interfaces to make
		sure they are working in a proper way and do not need to
		be changed further.


Note:
   The fields should be use a simple notation, compatible with ReST markup.
   Also, the file **should not** have a top-level index, like::

	===
	foo
	===

How things move between levels:

Interfaces in stable may move to obsolete, as long as the proper
notification is given.

Interfaces may be removed from obsolete and the kernel as long as the
documented amount of time has gone by.

Interfaces in the testing state can move to the stable state when the
developers feel they are finished.  They cannot be removed from the
kernel tree without going through the obsolete state first.

It's up to the developer to place their interfaces in the category they
wish for it to start out in.


Notable bits of non-ABI, which should not under any circumstances be considered
stable:

- Kconfig.  Userspace should not rely on the presence or absence of any
  particular Kconfig symbol, in /proc/config.gz, in the copy of .config
  commonly installed to /boot, or in any invocation of the kernel build
  process.

- Kernel-internal symbols.  Do not rely on the presence, absence, location, or
  type of any kernel symbol, either in System.map files or the kernel binary
  itself.  See Documentation/process/stable-api-nonsense.rst.
