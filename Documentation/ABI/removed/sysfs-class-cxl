The cxl driver was removed in 6.15.

Please note that attributes that are shared between devices are stored in
the directory pointed to by the symlink device/.
For example, the real path of the attribute /sys/class/cxl/afu0.0s/irqs_max is
/sys/class/cxl/afu0.0s/device/irqs_max, i.e. /sys/class/cxl/afu0.0/irqs_max.


Slave contexts (eg. /sys/class/cxl/afu0.0s):

What:           /sys/class/cxl/<afu>/afu_err_buf
Date:           September 2014, removed February 2025
Contact:        <EMAIL>
Description:    read only
                AFU Error Buffer contents. The contents of this file are
		application specific and depends on the AFU being used.
		Applications interacting with the AFU can use this attribute
		to know about the current error condition and take appropriate
		action like logging the event etc.


What:           /sys/class/cxl/<afu>/irqs_max
Date:           September 2014, removed February 2025
Contact:        <EMAIL>
Description:    read/write
                Decimal value of maximum number of interrupts that can be
                requested by userspace.  The default on probe is the maximum
                that hardware can support (eg. 2037). Write values will limit
                userspace applications to that many userspace interrupts. Must
                be >= irqs_min.
Users:		https://github.com/ibm-capi/libcxl

What:           /sys/class/cxl/<afu>/irqs_min
Date:           September 2014, removed February 2025
Contact:        <EMAIL>
Description:    read only
                Decimal value of the minimum number of interrupts that
                userspace must request on a CXL_START_WORK ioctl. Userspace may
                omit the num_interrupts field in the START_WORK IOCTL to get
                this minimum automatically.
Users:		https://github.com/ibm-capi/libcxl

What:           /sys/class/cxl/<afu>/mmio_size
Date:           September 2014, removed February 2025
Contact:        <EMAIL>
Description:    read only
                Decimal value of the size of the MMIO space that may be mmapped
                by userspace.
Users:		https://github.com/ibm-capi/libcxl

What:           /sys/class/cxl/<afu>/modes_supported
Date:           September 2014, removed February 2025
Contact:        <EMAIL>
Description:    read only
                List of the modes this AFU supports. One per line.
                Valid entries are: "dedicated_process" and "afu_directed"
Users:		https://github.com/ibm-capi/libcxl

What:           /sys/class/cxl/<afu>/mode
Date:           September 2014, removed February 2025
Contact:        <EMAIL>
Description:    read/write
                The current mode the AFU is using. Will be one of the modes
                given in modes_supported. Writing will change the mode
                provided that no user contexts are attached.
Users:		https://github.com/ibm-capi/libcxl


What:           /sys/class/cxl/<afu>/prefault_mode
Date:           September 2014, removed February 2025
Contact:        <EMAIL>
Description:    read/write
                Set the mode for prefaulting in segments into the segment table
                when performing the START_WORK ioctl. Only applicable when
                running under hashed page table mmu.
                Possible values:

                =======================  ======================================
		none			 No prefaulting (default)
		work_element_descriptor  Treat the work element
					 descriptor as an effective address and
					 prefault what it points to.
                all			 all segments process calling
					 START_WORK maps.
                =======================  ======================================

Users:		https://github.com/ibm-capi/libcxl

What:           /sys/class/cxl/<afu>/reset
Date:           September 2014, removed February 2025
Contact:        <EMAIL>
Description:    write only
                Writing 1 here will reset the AFU provided there are not
                contexts active on the AFU.
Users:		https://github.com/ibm-capi/libcxl

What:           /sys/class/cxl/<afu>/api_version
Date:           September 2014, removed February 2025
Contact:        <EMAIL>
Description:    read only
                Decimal value of the current version of the kernel/user API.
Users:		https://github.com/ibm-capi/libcxl

What:           /sys/class/cxl/<afu>/api_version_compatible
Date:           September 2014, removed February 2025
Contact:        <EMAIL>
Description:    read only
                Decimal value of the lowest version of the userspace API
                this kernel supports.
Users:		https://github.com/ibm-capi/libcxl


AFU configuration records (eg. /sys/class/cxl/afu0.0/cr0):

An AFU may optionally export one or more PCIe like configuration records, known
as AFU configuration records, which will show up here (if present).

What:           /sys/class/cxl/<afu>/cr<config num>/vendor
Date:           February 2015, removed February 2025
Contact:        <EMAIL>
Description:    read only
		Hexadecimal value of the vendor ID found in this AFU
		configuration record.
Users:		https://github.com/ibm-capi/libcxl

What:           /sys/class/cxl/<afu>/cr<config num>/device
Date:           February 2015, removed February 2025
Contact:        <EMAIL>
Description:    read only
		Hexadecimal value of the device ID found in this AFU
		configuration record.
Users:		https://github.com/ibm-capi/libcxl

What:           /sys/class/cxl/<afu>/cr<config num>/class
Date:           February 2015, removed February 2025
Contact:        <EMAIL>
Description:    read only
		Hexadecimal value of the class code found in this AFU
		configuration record.
Users:		https://github.com/ibm-capi/libcxl

What:           /sys/class/cxl/<afu>/cr<config num>/config
Date:           February 2015, removed February 2025
Contact:        <EMAIL>
Description:    read only
		This binary file provides raw access to the AFU configuration
		record. The format is expected to match the either the standard
		or extended configuration space defined by the PCIe
		specification.
Users:		https://github.com/ibm-capi/libcxl



Master contexts (eg. /sys/class/cxl/afu0.0m)

What:           /sys/class/cxl/<afu>m/mmio_size
Date:           September 2014, removed February 2025
Contact:        <EMAIL>
Description:    read only
                Decimal value of the size of the MMIO space that may be mmapped
                by userspace. This includes all slave contexts space also.
Users:		https://github.com/ibm-capi/libcxl

What:           /sys/class/cxl/<afu>m/pp_mmio_len
Date:           September 2014, removed February 2025
Contact:        <EMAIL>
Description:    read only
                Decimal value of the Per Process MMIO space length.
Users:		https://github.com/ibm-capi/libcxl

What:           /sys/class/cxl/<afu>m/pp_mmio_off
Date:           September 2014, removed February 2025
Contact:        <EMAIL>
Description:    read only
                (not in a guest)
                Decimal value of the Per Process MMIO space offset.
Users:		https://github.com/ibm-capi/libcxl


Card info (eg. /sys/class/cxl/card0)

What:           /sys/class/cxl/<card>/caia_version
Date:           September 2014, removed February 2025
Contact:        <EMAIL>
Description:    read only
                Identifies the CAIA Version the card implements.
Users:		https://github.com/ibm-capi/libcxl

What:           /sys/class/cxl/<card>/psl_revision
Date:           September 2014, removed February 2025
Contact:        <EMAIL>
Description:    read only
                Identifies the revision level of the PSL.
Users:		https://github.com/ibm-capi/libcxl

What:           /sys/class/cxl/<card>/base_image
Date:           September 2014, removed February 2025
Contact:        <EMAIL>
Description:    read only
                (not in a guest)
                Identifies the revision level of the base image for devices
                that support loadable PSLs. For FPGAs this field identifies
                the image contained in the on-adapter flash which is loaded
                during the initial program load.
Users:		https://github.com/ibm-capi/libcxl

What:           /sys/class/cxl/<card>/image_loaded
Date:           September 2014, removed February 2025
Contact:        <EMAIL>
Description:    read only
                (not in a guest)
                Will return "user" or "factory" depending on the image loaded
                onto the card.
Users:		https://github.com/ibm-capi/libcxl

What:           /sys/class/cxl/<card>/load_image_on_perst
Date:           December 2014, removed February 2025
Contact:        <EMAIL>
Description:    read/write
                (not in a guest)
                Valid entries are "none", "user", and "factory".
                "none" means PERST will not cause image to be loaded to the
                card.  A power cycle is required to load the image.
                "none" could be useful for debugging because the trace arrays
                are preserved.

                "user" and "factory" means PERST will cause either the user or
                user or factory image to be loaded.
                Default is to reload on PERST whichever image the card has
                loaded.
Users:		https://github.com/ibm-capi/libcxl

What:           /sys/class/cxl/<card>/reset
Date:           October 2014, removed February 2025
Contact:        <EMAIL>
Description:    write only
                Writing 1 will issue a PERST to card provided there are no
                contexts active on any one of the card AFUs. This may cause
                the card to reload the FPGA depending on load_image_on_perst.
                Writing -1 will do a force PERST irrespective of any active
                contexts on the card AFUs.
Users:		https://github.com/ibm-capi/libcxl

What:		/sys/class/cxl/<card>/perst_reloads_same_image
Date:		July 2015, removed February 2025
Contact:	<EMAIL>
Description:	read/write
                (not in a guest)
		Trust that when an image is reloaded via PERST, it will not
		have changed.

		==  =================================================
		0   don't trust, the image may be different (default)
		1   trust that the image will not change.
		==  =================================================
Users:		https://github.com/ibm-capi/libcxl

What:           /sys/class/cxl/<card>/psl_timebase_synced
Date:           March 2016, removed February 2025
Contact:        <EMAIL>
Description:    read only
                Returns 1 if the psl timebase register is synchronized
                with the core timebase register, 0 otherwise.
Users:          https://github.com/ibm-capi/libcxl

What:           /sys/class/cxl/<card>/tunneled_ops_supported
Date:           May 2018, removed February 2025
Contact:        <EMAIL>
Description:    read only
                Returns 1 if tunneled operations are supported in capi mode,
                0 otherwise.
Users:          https://github.com/ibm-capi/libcxl
