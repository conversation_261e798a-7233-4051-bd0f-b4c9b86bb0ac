What:		/sys/firmware/turris-mox-rwtm/board_version
Date:		August 2019
KernelVersion:	5.4
Contact:	<PERSON><PERSON> <<EMAIL>>
Description:	(Read) Board version burned into eFuses of this Turris Mox board.
		Format: %i

What:		/sys/firmware/turris-mox-rwtm/mac_address*
Date:		August 2019
KernelVersion:	5.4
Contact:	<PERSON><PERSON> <<EMAIL>>
Description:	(Read) MAC addresses burned into eFuses of this Turris Mox board.
		Format: %pM

What:		/sys/firmware/turris-mox-rwtm/ram_size
Date:		August 2019
KernelVersion:	5.4
Contact:	<PERSON><PERSON> <<EMAIL>>
Description:	(Read) RAM size in MiB of this Turris Mox board as was detected
		during manufacturing and burned into eFuses. Can be 512 or 1024.
		Format: %i

What:		/sys/firmware/turris-mox-rwtm/serial_number
Date:		August 2019
KernelVersion:	5.4
Contact:	<PERSON><PERSON> <<EMAIL>>
Description:	(Read) Serial number burned into eFuses of this Turris Mox device.
		Format: %016X
