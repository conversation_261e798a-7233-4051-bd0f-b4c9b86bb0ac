What:		/sys/kernel/debug/scmi/<n>/raw/message
Date:		March 2023
KernelVersion:	6.3
Contact:	<EMAIL>
Description:	SCMI Raw synchronous message injection/snooping facility; write
		a complete SCMI synchronous command message (header included)
		in little-endian binary format to have it sent to the configured
		backend SCMI server for instance <n>.
		Any subsequently received response can be read from this same
		entry if it arrived within the configured timeout.
		Each write to the entry causes one command request to be built
		and sent while the replies are read back one message at time
		(receiving an EOF at each message boundary).
Users:		Debugging, any userspace test suite

What:		/sys/kernel/debug/scmi/<n>/raw/message_async
Date:		March 2023
KernelVersion:	6.3
Contact:	<EMAIL>
Description:	SCMI Raw asynchronous message injection/snooping facility; write
		a complete SCMI asynchronous command message (header included)
		in little-endian binary format to have it sent to the configured
		backend SCMI server for instance <n>.
		Any subsequently received response can be read from this same
		entry if it arrived within the configured timeout.
		Any additional delayed response received afterwards can be read
		from this same entry too if it arrived within the configured
		timeout.
		Each write to the entry causes one command request to be built
		and sent while the replies are read back one message at time
		(receiving an EOF at each message boundary).
Users:		Debugging, any userspace test suite

What:		/sys/kernel/debug/scmi/<n>/raw/message_poll
Date:		June 2025
KernelVersion:	6.16
Contact:	<EMAIL>
Description:	SCMI Raw message injection/snooping facility using polling mode;
		write a complete SCMI command message (header included) in
		little-endian binary format to have it sent to the configured
		backend SCMI server for instance <n>, using polling mode on
		the reception path. (if transport is polling capable)
		Any subsequently received response can be read from this same
		entry if it arrived within the configured timeout.
		Each write to the entry causes one command request to be built
		and sent while the replies are read back one message at time
		(receiving an EOF at each message boundary).
Users:		Debugging, any userspace test suite

What:		/sys/kernel/debug/scmi/<n>/raw/message_poll_async
Date:		June 2025
KernelVersion:	6.16
Contact:	<EMAIL>
Description:	SCMI Raw asynchronous message injection/snooping facility using
		polling-mode; write a complete SCMI asynchronous command message
		(header included) in little-endian binary format to have it sent
		to the configured backend SCMI server for instance <n>, using
		polling-mode on the reception path of the immediate part of the
		asynchronous command. (if transport is polling capable)
		Any subsequently received response can be read from this same
		entry if it arrived within the configured timeout.
		Any additional delayed response received afterwards can be read
		from this same entry too if it arrived within the configured
		timeout.
		Each write to the entry causes one command request to be built
		and sent while the replies are read back one message at time
		(receiving an EOF at each message boundary).
Users:		Debugging, any userspace test suite

What:		/sys/kernel/debug/scmi/<n>/raw/errors
Date:		March 2023
KernelVersion:	6.3
Contact:	<EMAIL>
Description:	SCMI Raw message errors facility; any kind of timed-out or
		generally unexpectedly received SCMI message, for instance <n>,
		can be read from this entry.
		Each read gives back one message at time (receiving an EOF at
		each message boundary).
Users:		Debugging, any userspace test suite

What:		/sys/kernel/debug/scmi/<n>/raw/notification
Date:		March 2023
KernelVersion:	6.3
Contact:	<EMAIL>
Description:	SCMI Raw notification snooping facility; any notification
		emitted by the backend SCMI server, for instance <n>, can be
		read from this entry.
		Each read gives back one message at time (receiving an EOF at
		each message boundary).
Users:		Debugging, any userspace test suite

What:		/sys/kernel/debug/scmi/<n>/raw/reset
Date:		March 2023
KernelVersion:	6.3
Contact:	<EMAIL>
Description:	SCMI Raw stack reset facility; writing a value to this entry
		causes the internal queues of any kind of received message,
		still pending to be read out for instance <n>, to be immediately
		flushed.
		Can be used to reset and clean the SCMI Raw stack between to
		different test-run.
Users:		Debugging, any userspace test suite

What:		/sys/kernel/debug/scmi/<n>/raw/channels/<m>/message
Date:		March 2023
KernelVersion:	6.3
Contact:	<EMAIL>
Description:	SCMI Raw synchronous message injection/snooping facility; write
		a complete SCMI synchronous command message (header included)
		in little-endian binary format to have it sent to the configured
		backend SCMI server for instance <n> through the <m> transport
		channel.
		Any subsequently received response can be read from this same
		entry if it arrived on channel <m> within the configured
		timeout.
		Each write to the entry causes one command request to be built
		and sent while the replies are read back one message at time
		(receiving an EOF at each message boundary).
		Channel identifier <m> matches the SCMI protocol number which
		has been associated with this transport channel in the DT
		description, with base protocol number 0x10 being the default
		channel for this instance.
		Note that these per-channel entries rooted at <..>/channels
		exist only if the transport is configured to have more than
		one default channel.
Users:		Debugging, any userspace test suite

What:		/sys/kernel/debug/scmi/<n>/raw/channels/<m>/message_async
Date:		March 2023
KernelVersion:	6.3
Contact:	<EMAIL>
Description:	SCMI Raw asynchronous message injection/snooping facility; write
		a complete SCMI asynchronous command message (header included)
		in little-endian binary format to have it sent to the configured
		backend SCMI server for instance <n> through the <m> transport
		channel.
		Any subsequently received response can be read from this same
		entry if it arrived on channel <m> within the configured
		timeout.
		Any additional delayed response received afterwards can be read
		from this same entry too if it arrived within the configured
		timeout.
		Each write to the entry causes one command request to be built
		and sent while the replies are read back one message at time
		(receiving an EOF at each message boundary).
		Channel identifier <m> matches the SCMI protocol number which
		has been associated with this transport channel in the DT
		description, with base protocol number 0x10 being the default
		channel for this instance.
		Note that these per-channel entries rooted at <..>/channels
		exist only if the transport is configured to have more than
		one default channel.
Users:		Debugging, any userspace test suite


What:		/sys/kernel/debug/scmi/<n>/raw/channels/<m>/message_poll
Date:		June 2025
KernelVersion:	6.16
Contact:	<EMAIL>
Description:	SCMI Raw message injection/snooping facility using polling mode;
		write a complete SCMI command message (header included) in
		little-endian binary format to have it sent to the configured
		backend SCMI server for instance <n> through the <m> transport
		channel, using polling mode on the reception path.
		(if transport is polling capable)
		Any subsequently received response can be read from this same
		entry if it arrived on channel <m> within the configured
		timeout.
		Each write to the entry causes one command request to be built
		and sent while the replies are read back one message at time
		(receiving an EOF at each message boundary).
		Channel identifier <m> matches the SCMI protocol number which
		has been associated with this transport channel in the DT
		description, with base protocol number 0x10 being the default
		channel for this instance.
		Note that these per-channel entries rooted at <..>/channels
		exist only if the transport is configured to have more than
		one default channel.
Users:		Debugging, any userspace test suite

What:		/sys/kernel/debug/scmi/<n>/raw/channels/<m>/message_poll_async
Date:		June 2025
KernelVersion:	6.16
Contact:	<EMAIL>
Description:	SCMI Raw asynchronous message injection/snooping facility using
		polling-mode; write a complete SCMI asynchronous command message
		(header included) in little-endian binary format to have it sent
		to the configured backend SCMI server for instance <n> through
		the <m> transport channel, using polling mode on the reception
		path of the immediate part of the asynchronous command.
		(if transport is polling capable)
		Any subsequently received response can be read from this same
		entry if it arrived on channel <m> within the configured
		timeout.
		Any additional delayed response received afterwards can be read
		from this same entry too if it arrived within the configured
		timeout.
		Each write to the entry causes one command request to be built
		and sent while the replies are read back one message at time
		(receiving an EOF at each message boundary).
		Channel identifier <m> matches the SCMI protocol number which
		has been associated with this transport channel in the DT
		description, with base protocol number 0x10 being the default
		channel for this instance.
		Note that these per-channel entries rooted at <..>/channels
		exist only if the transport is configured to have more than
		one default channel.
Users:		Debugging, any userspace test suite
