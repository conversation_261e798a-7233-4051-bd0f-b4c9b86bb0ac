What:		/config/usb-gadget/gadget/functions/midi2.name
Date:		Jul 2023
KernelVersion:	6.6
Description:
		The attributes:

		============	===============================================
		process_ump	Flag to process UMP Stream messages (0 or 1)
		static_block	Flag for static blocks (0 or 1)
		iface_name	MIDI interface name string
		============	===============================================

What:		/config/usb-gadget/gadget/functions/midi2.name/ep.number
Date:		Jul 2023
KernelVersion:	6.6
Description:
		This group contains a UMP Endpoint configuration.
		A new Endpoint starts from 0, and can be up to 3.

		The attributes:

		=============	===============================================
		protocol_caps	MIDI protocol capabilities (1, 2 or 3 for both)
		protocol	Default MIDI protocol (1 or 2)
		ep_name		UMP Endpoint name string
		product_id	Product ID string
		manufacturer	Manufacture ID (24 bit)
		family		Device family ID (16 bit)
		model		Device model ID (16 bit)
		sw_revision	Software Revision (32 bit)
		=============	===============================================

What:		/config/usb-gadget/gadget/functions/midi2.name/ep.number/block.number
Date:		Jul 2023
KernelVersion:	6.6
Description:
		This group contains a UMP Function Block configuration.
		A new block starts from 0, and can be up to 31.

		The attributes:

		=================	==============================================
		name			Function Block name string
		direction		1: input, 2: output, 3: bidirectional
		first_group		The first UMP Group number (0-15)
		num_groups		The number of groups in this FB (1-16)
		midi1_first_group	The first UMP Group number for MIDI 1.0 (0-15)
		midi1_num_groups	The number of groups for MIDI 1.0 (0-16)
		ui_hint			0: unknown, 1: receiver, 2: sender, 3: both
		midi_ci_version		Supported MIDI-CI version number (8 bit)
		is_midi1		Legacy MIDI 1.0 device (0, 1 or 2)
		sysex8_streams		Max number of SysEx8 streams (8 bit)
		active			Active FB flag (0 or 1)
		=================	==============================================
