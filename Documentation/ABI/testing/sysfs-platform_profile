What:		/sys/firmware/acpi/platform_profile_choices
Date:		October 2020
Contact:	<PERSON> <<EMAIL>>
Description:	This file contains a space-separated list of profiles supported for this device.

		Drivers must use the following standard profile-names:

		====================	========================================
		low-power		Low power consumption
		cool			Cooler operation
		quiet			Quieter operation
		balanced		Balance between low power consumption
					and performance
		balanced-performance	Balance between performance and low
					power consumption with a slight bias
					towards performance
		performance		High performance operation
		====================	========================================

		Userspace may expect drivers to offer more than one of these
		standard profile names.

What:		/sys/firmware/acpi/platform_profile
Date:		October 2020
Contact:	<PERSON> <<EMAIL>>
Description:	Reading this file gives the current selected profile for this
		device. Writing this file with one of the strings from
		platform_profile_choices changes the profile to the new value.

		This file can be monitored for changes by polling for POLLPRI,
		POLLPRI will be signalled on any changes, independent of those
		changes coming from a userspace write; or coming from another
		source such as e.g. a hotkey triggered profile change handled
		either directly by the embedded-controller or fully handled
		inside the kernel.

		This file may also emit the string 'custom' to indicate
		that multiple platform profiles drivers are in use but
		have different values.  This string can not be written to
		this interface and is solely for informational purposes.
