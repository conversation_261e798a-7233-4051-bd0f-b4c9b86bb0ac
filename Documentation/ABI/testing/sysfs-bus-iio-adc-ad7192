What:		/sys/bus/iio/devices/iio:deviceX/ac_excitation_en
KernelVersion:
Contact:	<EMAIL>
Description:
		This attribute, if available, is used to enable the AC
		excitation mode found on some converters. In ac excitation mode,
		the polarity of the excitation voltage is reversed on
		alternate cycles, to eliminate DC errors.

What:		/sys/bus/iio/devices/iio:deviceX/bridge_switch_en
KernelVersion:
Contact:	<EMAIL>
Description:
		This attribute, if available, is used to close or open the
		bridge power down switch found on some converters.
		In bridge applications, such as strain gauges and load cells,
		the bridge itself consumes the majority of the current in the
		system. To minimize the current consumption of the system,
		the bridge can be disconnected (when it is not being used
		using the bridge_switch_en attribute.

What:		/sys/bus/iio/devices/iio:deviceX/in_voltage2-voltage2_shorted_raw
KernelVersion:
Contact:	<EMAIL>
Description:
		Measure voltage from AIN2 pin connected to AIN(+)
		and AIN(-) shorted.
