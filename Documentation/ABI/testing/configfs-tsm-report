What:		/sys/kernel/config/tsm/report/$name/inblob
Date:		September, 2023
KernelVersion:	v6.7
Contact:	<EMAIL>
Description:
		(WO) Up to 64 bytes of user specified binary data. For replay
		protection this should include a nonce, but the kernel does not
		place any restrictions on the content.

What:		/sys/kernel/config/tsm/report/$name/outblob
Date:		September, 2023
KernelVersion:	v6.7
Contact:	<EMAIL>
Description:
		(RO) Binary attestation report generated from @inblob and other
		options The format of the report is implementation specific
		where the implementation is conveyed via the @provider
		attribute.

What:		/sys/kernel/config/tsm/report/$name/auxblob
Date:		October, 2023
KernelVersion:	v6.7
Contact:	<EMAIL>
Description:
		(RO) Optional supplemental data that a TSM may emit, visibility
		of this attribute depends on TSM, and may be empty if no
		auxiliary data is available.

		When @provider is "sev_guest" this file contains the
		"cert_table" from SEV-ES Guest-Hypervisor Communication Block
		Standardization v2.03 Section ******* MSG_REPORT_REQ.
		https://www.amd.com/content/dam/amd/en/documents/epyc-technical-docs/specifications/56421.pdf

What:		/sys/kernel/config/tsm/report/$name/manifestblob
Date:		January, 2024
KernelVersion:	v6.10
Contact:	<EMAIL>
Description:
		(RO) Optional supplemental data that a TSM may emit, visibility
		of this attribute depends on TSM, and may be empty if no
		manifest data is available.

		See 'service_provider' for information on the format of the
		manifest blob.

What:		/sys/kernel/config/tsm/report/$name/provider
Date:		September, 2023
KernelVersion:	v6.7
Contact:	<EMAIL>
Description:
		(RO) A name for the format-specification of @outblob like
		"sev_guest" [1] or "tdx_guest" [2] in the near term, or a
		common standard format in the future.

		[1]: SEV Secure Nested Paging Firmware ABI Specification
		Revision 1.55 Table 22
		https://www.amd.com/content/dam/amd/en/documents/epyc-technical-docs/specifications/56860.pdf

		[2]: Intel® Trust Domain Extensions Data Center Attestation
		Primitives : Quote Generation Library and Quote Verification
		Library Revision 0.8 Appendix 4,5
		https://download.01.org/intel-sgx/latest/dcap-latest/linux/docs/Intel_TDX_DCAP_Quoting_Library_API.pdf

What:		/sys/kernel/config/tsm/report/$name/generation
Date:		September, 2023
KernelVersion:	v6.7
Contact:	<EMAIL>
Description:
		(RO) The value in this attribute increments each time @inblob or
		any option is written. Userspace can detect conflicts by
		checking generation before writing to any attribute and making
		sure the number of writes matches expectations after reading
		@outblob, or it can prevent conflicts by creating a report
		instance per requesting context.

What:		/sys/kernel/config/tsm/report/$name/privlevel
Date:		September, 2023
KernelVersion:	v6.7
Contact:	<EMAIL>
Description:
		(WO) Attribute is visible if a TSM implementation provider
		supports the concept of attestation reports for TVMs running at
		different privilege levels, like SEV-SNP "VMPL", specify the
		privilege level via this attribute.  The minimum acceptable
		value is conveyed via @privlevel_floor and the maximum
		acceptable value is TSM_PRIVLEVEL_MAX (3).

What:		/sys/kernel/config/tsm/report/$name/privlevel_floor
Date:		September, 2023
KernelVersion:	v6.7
Contact:	<EMAIL>
Description:
		(RO) Indicates the minimum permissible value that can be written
		to @privlevel.

What:		/sys/kernel/config/tsm/report/$name/service_provider
Date:		January, 2024
KernelVersion:	v6.10
Contact:	<EMAIL>
Description:
		(WO) Attribute is visible if a TSM implementation provider
		supports the concept of attestation reports from a service
		provider for TVMs, like SEV-SNP running under an SVSM.
		Specifying the service provider via this attribute will create
		an attestation report as specified by the service provider.
		The only currently supported service provider is "svsm".

		For the "svsm" service provider, see the Secure VM Service Module
		for SEV-SNP Guests v1.00 Section 7. For the doc, search for
		"site:amd.com "Secure VM Service Module for SEV-SNP
		Guests", docID: 58019"

What:		/sys/kernel/config/tsm/report/$name/service_guid
Date:		January, 2024
KernelVersion:	v6.10
Contact:	<EMAIL>
Description:
		(WO) Attribute is visible if a TSM implementation provider
		supports the concept of attestation reports from a service
		provider for TVMs, like SEV-SNP running under an SVSM.
		Specifying an empty/null GUID (00000000-0000-0000-0000-000000)
		requests all active services within the service provider be
		part of the attestation report. Specifying a GUID request
		an attestation report of just the specified service using the
		manifest form specified by the service_manifest_version
		attribute.

		See 'service_provider' for information on the format of the
		service guid.

What:		/sys/kernel/config/tsm/report/$name/service_manifest_version
Date:		January, 2024
KernelVersion:	v6.10
Contact:	<EMAIL>
Description:
		(WO) Attribute is visible if a TSM implementation provider
		supports the concept of attestation reports from a service
		provider for TVMs, like SEV-SNP running under an SVSM.
		Indicates the service manifest version requested for the
		attestation report (default 0). If this field is not set by
		the user, the default manifest version of the service (the
		service's initial/first manifest version) is returned.

		See 'service_provider' for information on the format of the
		service manifest version.
