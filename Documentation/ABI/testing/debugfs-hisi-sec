What:		/sys/kernel/debug/hisi_sec2/<bdf>/clear_enable
Date:		Oct 2019
Contact:	<EMAIL>
Description:	Enabling/disabling of clear action after reading
		the SEC debug registers.
		0: disable, 1: enable.
		Only available for PF, and take no other effect on SEC.

What:		/sys/kernel/debug/hisi_sec2/<bdf>/current_qm
Date:		Oct 2019
Contact:	<EMAIL>
Description:	One SEC controller has one PF and multiple VFs, each function
		has a QM. This file can be used to select the QM which below
		qm refers to.
		Only available for PF.

What:		/sys/kernel/debug/hisi_sec2/<bdf>/alg_qos
Date:		Jun 2021
Contact:	<EMAIL>
Description:	The <bdf> is related the function for PF and VF.
		SEC driver supports to configure each function's QoS, the driver
		supports to write <bdf> value to alg_qos in the host. Such as
		"echo <bdf> value > alg_qos". The qos value is 1~1000, means
		1/1000~1000/1000 of total QoS. The driver reading alg_qos to
		get related QoS in the host and VM, Such as "cat alg_qos".

What:		/sys/kernel/debug/hisi_sec2/<bdf>/qm/qm_regs
Date:		Oct 2019
Contact:	<EMAIL>
Description:	Dump of QM related debug registers.
		Available for PF and VF in host. VF in guest currently only
		has one debug register.

What:		/sys/kernel/debug/hisi_sec2/<bdf>/qm/current_q
Date:		Oct 2019
Contact:	<EMAIL>
Description:	One QM of SEC may contain multiple queues. Select specific
		queue to show its debug registers in above 'regs'.
		Only available for PF.

What:		/sys/kernel/debug/hisi_sec2/<bdf>/qm/clear_enable
Date:		Oct 2019
Contact:	<EMAIL>
Description:	Enabling/disabling of clear action after reading
		the SEC's QM debug registers.
		0: disable, 1: enable.
		Only available for PF, and take no other effect on SEC.

What:		/sys/kernel/debug/hisi_sec2/<bdf>/qm/err_irq
Date:		Apr 2020
Contact:	<EMAIL>
Description:	Dump the number of invalid interrupts for
		QM task completion.
		Available for both PF and VF, and take no other effect on SEC.

What:		/sys/kernel/debug/hisi_sec2/<bdf>/qm/aeq_irq
Date:		Apr 2020
Contact:	<EMAIL>
Description:	Dump the number of QM async event queue interrupts.
		Available for both PF and VF, and take no other effect on SEC.

What:		/sys/kernel/debug/hisi_sec2/<bdf>/qm/abnormal_irq
Date:		Apr 2020
Contact:	<EMAIL>
Description:	Dump the number of interrupts for QM abnormal event.
		Available for both PF and VF, and take no other effect on SEC.

What:		/sys/kernel/debug/hisi_sec2/<bdf>/qm/create_qp_err
Date:		Apr 2020
Contact:	<EMAIL>
Description:	Dump the number of queue allocation errors.
		Available for both PF and VF, and take no other effect on SEC.

What:		/sys/kernel/debug/hisi_sec2/<bdf>/qm/mb_err
Date:		Apr 2020
Contact:	<EMAIL>
Description:	Dump the number of failed QM mailbox commands.
		Available for both PF and VF, and take no other effect on SEC.

What:		/sys/kernel/debug/hisi_sec2/<bdf>/qm/status
Date:		Apr 2020
Contact:	<EMAIL>
Description:	Dump the status of the QM.
		Two states: work, stop.
		Available for both PF and VF, and take no other effect on SEC.

What:		/sys/kernel/debug/hisi_sec2/<bdf>/qm/diff_regs
Date:		Mar 2022
Contact:	<EMAIL>
Description:	QM debug registers(regs) read hardware register value. This
		node is used to show the change of the qm register values. This
		node can be help users to check the change of register values.

What:		/sys/kernel/debug/hisi_sec2/<bdf>/qm/qm_state
Date:		Jan 2024
Contact:	<EMAIL>
Description:	Dump the state of the device.
		0: busy, 1: idle.
		Only available for PF, and take no other effect on SEC.

What:		/sys/kernel/debug/hisi_sec2/<bdf>/qm/dev_timeout
Date:		Feb 2024
Contact:	<EMAIL>
Description:	Set the wait time when stop queue fails. Available for both PF
		and VF, and take no other effect on SEC.
		0: not wait(default), others value: wait dev_timeout * 20 microsecond.

What:		/sys/kernel/debug/hisi_sec2/<bdf>/qm/dev_state
Date:		Feb 2024
Contact:	<EMAIL>
Description:	Dump the stop queue status of the QM. The default value is 0,
		if dev_timeout is set, when stop queue fails, the dev_state
		will return non-zero value. Available for both PF and VF,
		and take no other effect on SEC.

What:		/sys/kernel/debug/hisi_sec2/<bdf>/sec_dfx/diff_regs
Date:		Mar 2022
Contact:	<EMAIL>
Description:	SEC debug registers(regs) read hardware register value. This
		node is used to show the change of the register values. This
		node can be help users to check the change of register values.

What:		/sys/kernel/debug/hisi_sec2/<bdf>/sec_dfx/send_cnt
Date:		Apr 2020
Contact:	<EMAIL>
Description:	Dump the total number of sent requests.
		Available for both PF and VF, and take no other effect on SEC.

What:		/sys/kernel/debug/hisi_sec2/<bdf>/sec_dfx/recv_cnt
Date:		Apr 2020
Contact:	<EMAIL>
Description:	Dump the total number of received requests.
		Available for both PF and VF, and take no other effect on SEC.

What:		/sys/kernel/debug/hisi_sec2/<bdf>/sec_dfx/send_busy_cnt
Date:		Apr 2020
Contact:	<EMAIL>
Description:	Dump the total number of requests sent with returning busy.
		Available for both PF and VF, and take no other effect on SEC.

What:		/sys/kernel/debug/hisi_sec2/<bdf>/sec_dfx/err_bd_cnt
Date:		Apr 2020
Contact:	<EMAIL>
Description:	Dump the total number of BD type error requests
		to be received.
		Available for both PF and VF, and take no other effect on SEC.

What:		/sys/kernel/debug/hisi_sec2/<bdf>/sec_dfx/invalid_req_cnt
Date:		Apr 2020
Contact:	<EMAIL>
Description:	Dump the total number of invalid requests being received.
		Available for both PF and VF, and take no other effect on SEC.

What:		/sys/kernel/debug/hisi_sec2/<bdf>/sec_dfx/done_flag_cnt
Date:		Apr 2020
Contact:	<EMAIL>
Description:	Dump the total number of completed but marked error requests
		to be received.
		Available for both PF and VF, and take no other effect on SEC.

What:           /sys/kernel/debug/hisi_sec2/<bdf>/cap_regs
Date:           Oct 2024
Contact:        <EMAIL>
Description:    Dump the values of the qm and sec capability bit registers and
                support the query of device specifications to facilitate fault locating.
                Available for both PF and VF, and take no other effect on SEC.
