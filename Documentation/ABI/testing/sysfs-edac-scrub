What:		/sys/bus/edac/devices/<dev-name>/scrubX
Date:		March 2025
KernelVersion:	6.15
Contact:	<EMAIL>
Description:
		The sysfs EDAC bus devices /<dev-name>/scrubX subdirectory
		belongs to an instance of memory scrub control feature,
		where <dev-name> directory corresponds to a device/memory
		region registered with the EDAC device driver for the
		scrub control feature.

		The sysfs scrub attr nodes are only present if the parent
		driver has implemented the corresponding attr callback
		function and provided the necessary operations to the EDAC
		device driver during registration.

What:		/sys/bus/edac/devices/<dev-name>/scrubX/addr
Date:		March 2025
KernelVersion:	6.15
Contact:	<EMAIL>
Description:
		(RW) The base address of the memory region to be scrubbed
		for on-demand scrubbing. Setting address starts scrubbing.
		The size must be set before that.

		The readback addr value is non-zero if the requested
		on-demand scrubbing is in progress, zero otherwise.

What:		/sys/bus/edac/devices/<dev-name>/scrubX/size
Date:		March 2025
KernelVersion:	6.15
Contact:	<EMAIL>
Description:
		(RW) The size of the memory region to be scrubbed
		(on-demand scrubbing).

What:		/sys/bus/edac/devices/<dev-name>/scrubX/enable_background
Date:		March 2025
KernelVersion:	6.15
Contact:	<EMAIL>
Description:
		(RW) Start/Stop background (patrol) scrubbing if supported.

What:		/sys/bus/edac/devices/<dev-name>/scrubX/min_cycle_duration
Date:		March 2025
KernelVersion:	6.15
Contact:	<EMAIL>
Description:
		(RO) Supported minimum scrub cycle duration in seconds
		by the memory scrubber.

		Device-based scrub: returns the minimum scrub cycle
		supported by the memory device.

		Region-based scrub: returns the max of minimum scrub cycles
		supported by individual memory devices that back the region.

What:		/sys/bus/edac/devices/<dev-name>/scrubX/max_cycle_duration
Date:		March 2025
KernelVersion:	6.15
Contact:	<EMAIL>
Description:
		(RO) Supported maximum scrub cycle duration in seconds
		by the memory scrubber.

		Device-based scrub: returns the maximum scrub cycle supported
		by the memory device.

		Region-based scrub: returns the min of maximum scrub cycles
		supported by individual memory devices that back the region.

		If the memory device does not provide maximum scrub cycle
		information, return the maximum supported value of the scrub
		cycle field.

What:		/sys/bus/edac/devices/<dev-name>/scrubX/current_cycle_duration
Date:		March 2025
KernelVersion:	6.15
Contact:	<EMAIL>
Description:
		(RW) The current scrub cycle duration in seconds and must be
		within the supported range by the memory scrubber.

		Scrub has an overhead when running and that may want to be
		reduced by taking longer to do it.
