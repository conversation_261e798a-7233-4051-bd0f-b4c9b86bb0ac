What:		/sys/kernel/mm/cma/
Date:		Feb 2021
Contact:	<PERSON><PERSON> <<EMAIL>>
Description:
		/sys/kernel/mm/cma/ contains a subdirectory for each CMA
		heap name (also sometimes called CMA areas).

		Each CMA heap subdirectory (that is, each
		/sys/kernel/mm/cma/<cma-heap-name> directory) contains the
		following items:

			alloc_pages_success
			alloc_pages_fail

What:		/sys/kernel/mm/cma/<cma-heap-name>/alloc_pages_success
Date:		Feb 2021
Contact:	<PERSON><PERSON> <<EMAIL>>
Description:
		the number of pages CMA API succeeded to allocate

What:		/sys/kernel/mm/cma/<cma-heap-name>/alloc_pages_fail
Date:		Feb 2021
Contact:	<PERSON><PERSON> <<EMAIL>>
Description:
		the number of pages CMA API failed to allocate

What:		/sys/kernel/mm/cma/<cma-heap-name>/release_pages_success
Date:		Feb 2024
Contact:	Anshu<PERSON> Khandual <<EMAIL>>
Description:
		the number of pages CMA API succeeded to release

What:		/sys/kernel/mm/cma/<cma-heap-name>/total_pages
Date:		Jun 2024
Contact:	<PERSON> <<EMAIL>>
Description:
		The size of the CMA area in pages.

What:		/sys/kernel/mm/cma/<cma-heap-name>/available_pages
Date:		Jun 2024
Contact:	<PERSON> van der <PERSON> <<EMAIL>>
Description:
		The number of pages in the CMA area that are still
		available for CMA allocation.
