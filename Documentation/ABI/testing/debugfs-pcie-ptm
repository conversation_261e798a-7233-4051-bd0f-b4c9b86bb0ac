What:		/sys/kernel/debug/pcie_ptm_*/local_clock
Date:		May 2025
Contact:	Manivannan Sadhasivam <<EMAIL>>
Description:
		(RO) PTM local clock in nanoseconds. Applicable for both Root
		Complex and Endpoint controllers.

What:		/sys/kernel/debug/pcie_ptm_*/master_clock
Date:		May 2025
Contact:	Manivannan Sadhasivam <<EMAIL>>
Description:
		(RO) PTM master clock in nanoseconds. Applicable only for
		Endpoint controllers.

What:		/sys/kernel/debug/pcie_ptm_*/t1
Date:		May 2025
Contact:	Manivannan Sadhasivam <<EMAIL>>
Description:
		(RO) PTM T1 timestamp in nanoseconds. Applicable only for
		Endpoint controllers.

What:		/sys/kernel/debug/pcie_ptm_*/t2
Date:		May 2025
Contact:	Manivannan Sadhasivam <<EMAIL>>
Description:
		(RO) PTM T2 timestamp in nanoseconds. Applicable only for
		Root Complex controllers.

What:		/sys/kernel/debug/pcie_ptm_*/t3
Date:		May 2025
Contact:	Manivannan Sadhasivam <<EMAIL>>
Description:
		(RO) PTM T3 timestamp in nanoseconds. Applicable only for
		Root Complex controllers.

What:		/sys/kernel/debug/pcie_ptm_*/t4
Date:		May 2025
Contact:	Manivannan Sadhasivam <<EMAIL>>
Description:
		(RO) PTM T4 timestamp in nanoseconds. Applicable only for
		Endpoint controllers.

What:		/sys/kernel/debug/pcie_ptm_*/context_update
Date:		May 2025
Contact:	Manivannan Sadhasivam <<EMAIL>>
Description:
		(RW) Control the PTM context update mode. Applicable only for
		Endpoint controllers.

		Following values are supported:

		* auto = PTM context auto update trigger for every 10ms

		* manual = PTM context manual update. Writing 'manual' to this
			   file triggers PTM context update (default)

What:		/sys/kernel/debug/pcie_ptm_*/context_valid
Date:		May 2025
Contact:	Manivannan Sadhasivam <<EMAIL>>
Description:
		(RW) Control the PTM context validity (local clock timing).
		Applicable only for Root Complex controllers. PTM context is
		invalidated by hardware if the Root Complex enters low power
		mode or changes link frequency.

		Following values are supported:

		* 0 = PTM context invalid (default)

		* 1 = PTM context valid
