What:		/sys/kernel/mm/numa/
Date:		June 2021
Contact:	Linux memory management mailing list <<EMAIL>>
Description:	Interface for NUMA

What:		/sys/kernel/mm/numa/demotion_enabled
Date:		June 2021
Contact:	Linux memory management mailing list <<EMAIL>>
Description:	Enable/disable demoting pages during reclaim

		Page migration during reclaim is intended for systems
		with tiered memory configurations.  These systems have
		multiple types of memory with varied performance
		characteristics instead of plain NUMA systems where
		the same kind of memory is found at varied distances.
		Allowing page migration during reclaim enables these
		systems to migrate pages from fast tiers to slow tiers
		when the fast tier is under pressure.  This migration
		is performed before swap if an eligible numa node is
		present in cpuset.mems for the cgroup (or if cpuset v1
		is being used). If cpusets.mems changes at runtime, it
		may move data to a NUMA node that does not fall into the
		cpuset of the new cpusets.mems, which might be construed
		to violate the guarantees of cpusets.  Shared memory,
		such as libraries, owned by another cgroup may still be
		demoted and result in memory use on a node not present
		in cpusets.mem. This should not be enabled on systems
		which need strict cpuset location guarantees.
