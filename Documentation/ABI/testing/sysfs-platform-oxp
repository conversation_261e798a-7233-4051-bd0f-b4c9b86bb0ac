What:		/sys/devices/platform/<platform>/tt_toggle
Date:		Jun 2023
KernelVersion:	6.5
Contact:	"<PERSON><PERSON><PERSON>" <<EMAIL>>
Description:
		Takeover TDP controls from the device. OneXPlayer devices have a
		turbo button that can be used to switch between two TDP modes
		(usually 15W and 25W). By setting this attribute to 1, this
		functionality is disabled, handing TDP control over to (Windows)
		userspace software and the Turbo button turns into a keyboard
		shortcut over the AT keyboard of the device. In addition,
		using this setting is a prerequisite for PWM control for most
		newer models (otherwise it NOOPs).

What:		/sys/devices/platform/<platform>/tt_led
Date:		April 2025
KernelVersion:	6.16
Contact:	"<PERSON><PERSON><PERSON>" <<EMAIL>>
Description:
		Some OneXPlayer devices (e.g., X1 series) feature a little LED
		nested in the Turbo button. This LED is illuminated when the
		device is in the higher TDP mode (e.g., 25W). Once tt_toggle
		is engaged, this LED is left dangling to its last state. This
		attribute allows userspace to control the LED state manually
		(either with 1 or 0). Only a subset of devices contain this LED.
