What:		/sys/devices/virtual/misc/tdx_guest/measurements/MRNAME[:HASH]
Date:		April, 2025
KernelVersion:	v6.16
Contact:	<EMAIL>
Description:
		Value of a TDX measurement register (MR). MRNAME and HASH above
		are placeholders. The optional suffix :HASH is used for MRs
		that have associated hash algorithms. See below for a complete
		list of TDX MRs exposed via sysfs. Refer to Intel TDX Module
		ABI Specification for the definition of TDREPORT and the full
		list of TDX measurements.

		Intel TDX Module ABI Specification can be found at:
		https://www.intel.com/content/www/us/en/developer/tools/trust-domain-extensions/documentation.html#architecture

		See also:
		https://docs.kernel.org/driver-api/coco/measurement-registers.html

What:		/sys/devices/virtual/misc/tdx_guest/measurements/mrconfigid
Date:		April, 2025
KernelVersion:	v6.16
Contact:	<EMAIL>
Description:
		(RO) MRCONFIGID - 48-byte immutable storage typically used for
		software-defined ID for non-owner-defined configuration of the
		guest TD – e.g., run-time or OS configuration.

What:		/sys/devices/virtual/misc/tdx_guest/measurements/mrowner
Date:		April, 2025
KernelVersion:	v6.16
Contact:	<EMAIL>
Description:
		(RO) MROWNER - 48-byte immutable storage typically used for
		software-defined ID for the guest TD’s owner.

What:		/sys/devices/virtual/misc/tdx_guest/measurements/mrownerconfig
Date:		April, 2025
KernelVersion:	v6.16
Contact:	<EMAIL>
Description:
		(RO) MROWNERCONFIG - 48-byte immutable storage typically used
		for software-defined ID for owner-defined configuration of the
		guest TD – e.g., specific to the workload rather than the
		run-time or OS.

What:		/sys/devices/virtual/misc/tdx_guest/measurements/mrtd:sha384
Date:		April, 2025
KernelVersion:	v6.16
Contact:	<EMAIL>
Description:
		(RO) MRTD - Measurement of the initial contents of the TD.

What:		/sys/devices/virtual/misc/tdx_guest/measurements/rtmr[0123]:sha384
Date:		April, 2025
KernelVersion:	v6.16
Contact:	<EMAIL>
Description:
		(RW) RTMR[0123] - 4 Run-Time extendable Measurement Registers.
		Read from any of these returns the current value of the
		corresponding RTMR. Write extends the written buffer to the
		RTMR. All writes must start at offset 0 and be 48 bytes in
		size. Partial writes will result in EINVAL returned by the
		write() syscall.
