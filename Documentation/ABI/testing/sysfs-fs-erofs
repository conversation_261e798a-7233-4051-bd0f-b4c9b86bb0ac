What:		/sys/fs/erofs/features/
Date:		November 2021
Contact:	"<PERSON>" <h<PERSON><PERSON><PERSON><PERSON>@oppo.com>
Description:	Shows all enabled kernel features.
		Supported features:
		zero_padding, compr_cfgs, big_pcluster, chunked_file,
		device_table, compr_head2, sb_chksum, ztailpacking,
		dedupe, fragments.

What:		/sys/fs/erofs/<disk>/sync_decompress
Date:		November 2021
Contact:	"<PERSON>" <<EMAIL>>
Description:	Control strategy of sync decompression:

		- 0 (default, auto): enable for readpage, and enable for
		  readahead on atomic contexts only.
		- 1 (force on): enable for readpage and readahead.
		- 2 (force off): disable for all situations.

What:		/sys/fs/erofs/<disk>/drop_caches
Date:		November 2024
Contact:	"Guo <PERSON>" <<EMAIL>>
Description:	Writing to this will drop compression-related caches,
		currently used to drop in-memory pclusters and cached
		compressed folios:

		- 1 : invalidate cached compressed folios
		- 2 : drop in-memory pclusters
		- 3 : drop in-memory pclusters and cached compressed folios

What:		/sys/fs/erofs/accel
Date:		May 2025
Contact:	"<PERSON>" <<EMAIL>>
Description:	Used to set or show hardware accelerators in effect
		and multiple accelerators are separated by '\n'.
		Supported accelerator(s): qat_deflate.
		Disable all accelerators with an empty string (echo > accel).
