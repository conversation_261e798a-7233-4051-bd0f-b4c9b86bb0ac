What:		/sys/bus/pci/devices/<BDF>/qat_ras/errors_correctable
Date:		January 2024
KernelVersion:	6.7
Contact:	<EMAIL>
Description:	(RO) Reports the number of correctable errors detected by the device.

		This attribute is only available for qat_4xxx and qat_6xxx devices.

What:		/sys/bus/pci/devices/<BDF>/qat_ras/errors_nonfatal
Date:		January 2024
KernelVersion:	6.7
Contact:	<EMAIL>
Description:	(RO) Reports the number of non fatal errors detected by the device.

		This attribute is only available for qat_4xxx and qat_6xxx devices.

What:		/sys/bus/pci/devices/<BDF>/qat_ras/errors_fatal
Date:		January 2024
KernelVersion:	6.7
Contact:	<EMAIL>
Description:	(RO) Reports the number of fatal errors detected by the device.

		This attribute is only available for qat_4xxx and qat_6xxx devices.

What:		/sys/bus/pci/devices/<BDF>/qat_ras/reset_error_counters
Date:		January 2024
KernelVersion:	6.7
Contact:	<EMAIL>
Description:	(WO) Write to resets all error counters of a device.

		The following example reports how to reset the counters::

			# echo 1 > /sys/bus/pci/devices/<BDF>/qat_ras/reset_error_counters
			# cat /sys/bus/pci/devices/<BDF>/qat_ras/errors_correctable
			0
			# cat /sys/bus/pci/devices/<BDF>/qat_ras/errors_nonfatal
			0
			# cat /sys/bus/pci/devices/<BDF>/qat_ras/errors_fatal
			0

		This attribute is only available for qat_4xxx and qat_6xxx devices.
