What:		/sys/bus/coresight/devices/<cti-name>/enable
Date:		March 2020
KernelVersion:	5.7
Contact:	<PERSON> or <PERSON><PERSON>
Description:	(RW) Enable/Disable the CTI hardware.

What:		/sys/bus/coresight/devices/<cti-name>/powered
Date:		March 2020
KernelVersion:	5.7
Contact:	<PERSON> or <PERSON><PERSON>
Description:	(Read) Indicate if the CTI hardware is powered.

What:		/sys/bus/coresight/devices/<cti-name>/ctmid
Date:		March 2020
KernelVersion:	5.7
Contact:	<PERSON> or <PERSON><PERSON>er
Description:	(Read) Display the associated CTM ID

What:		/sys/bus/coresight/devices/<cti-name>/nr_trigger_cons
Date:		March 2020
KernelVersion:	5.7
Contact:	<PERSON> or <PERSON><PERSON>
Description:	(Read) Number of devices connected to triggers on this CTI

What:		/sys/bus/coresight/devices/<cti-name>/triggers<N>/name
Date:		March 2020
KernelVersion:	5.7
Contact:	<PERSON> or <PERSON><PERSON><PERSON>
Description:	(Read) Name of connected device <N>

What:		/sys/bus/coresight/devices/<cti-name>/triggers<N>/in_signals
Date:		March 2020
KernelVersion:	5.7
Contact:	<PERSON> or <PERSON><PERSON>
Description:	(Read) Input trigger signals from connected device <N>

What:		/sys/bus/coresight/devices/<cti-name>/triggers<N>/in_types
Date:		March 2020
KernelVersion:	5.7
Contact:	Mike Leach or Mathieu Poirier
Description:	(Read) Functional types for the input trigger signals
		from connected device <N>

What:		/sys/bus/coresight/devices/<cti-name>/triggers<N>/out_signals
Date:		March 2020
KernelVersion:	5.7
Contact:	Mike Leach or Mathieu Poirier
Description:	(Read) Output trigger signals to connected device <N>

What:		/sys/bus/coresight/devices/<cti-name>/triggers<N>/out_types
Date:		March 2020
KernelVersion:	5.7
Contact:	Mike Leach or Mathieu Poirier
Description:	(Read) Functional types for the output trigger signals
		to connected device <N>

What:		/sys/bus/coresight/devices/<cti-name>/regs/inout_sel
Date:		March 2020
KernelVersion:	5.7
Contact:	Mike Leach or Mathieu Poirier
Description:	(RW) Select the index for inen and outen registers.

What:		/sys/bus/coresight/devices/<cti-name>/regs/inen
Date:		March 2020
KernelVersion:	5.7
Contact:	Mike Leach or Mathieu Poirier
Description:	(RW) Read or write the CTIINEN register selected by inout_sel.

What:		/sys/bus/coresight/devices/<cti-name>/regs/outen
Date:		March 2020
KernelVersion:	5.7
Contact:	Mike Leach or Mathieu Poirier
Description:	(RW) Read or write the CTIOUTEN register selected by inout_sel.

What:		/sys/bus/coresight/devices/<cti-name>/regs/gate
Date:		March 2020
KernelVersion:	5.7
Contact:	Mike Leach or Mathieu Poirier
Description:	(RW) Read or write CTIGATE register.

What:		/sys/bus/coresight/devices/<cti-name>/regs/asicctl
Date:		March 2020
KernelVersion:	5.7
Contact:	Mike Leach or Mathieu Poirier
Description:	(RW) Read or write ASICCTL register.

What:		/sys/bus/coresight/devices/<cti-name>/regs/intack
Date:		March 2020
KernelVersion:	5.7
Contact:	Mike Leach or Mathieu Poirier
Description:	(Write) Write the INTACK register.

What:		/sys/bus/coresight/devices/<cti-name>/regs/appset
Date:		March 2020
KernelVersion:	5.7
Contact:	Mike Leach or Mathieu Poirier
Description:	(RW) Set CTIAPPSET register to activate channel. Read back to
		determine current value of register.

What:		/sys/bus/coresight/devices/<cti-name>/regs/appclear
Date:		March 2020
KernelVersion:	5.7
Contact:	Mike Leach or Mathieu Poirier
Description:	(Write) Write APPCLEAR register to deactivate channel.

What:		/sys/bus/coresight/devices/<cti-name>/regs/apppulse
Date:		March 2020
KernelVersion:	5.7
Contact:	Mike Leach or Mathieu Poirier
Description:	(Write) Write APPPULSE to pulse a channel active for one clock
		cycle.

What:		/sys/bus/coresight/devices/<cti-name>/regs/chinstatus
Date:		March 2020
KernelVersion:	5.7
Contact:	Mike Leach or Mathieu Poirier
Description:	(Read) Read current status of channel inputs.

What:		/sys/bus/coresight/devices/<cti-name>/regs/choutstatus
Date:		March 2020
KernelVersion:	5.7
Contact:	Mike Leach or Mathieu Poirier
Description:	(Read) read current status of channel outputs.

What:		/sys/bus/coresight/devices/<cti-name>/regs/triginstatus
Date:		March 2020
KernelVersion:	5.7
Contact:	Mike Leach or Mathieu Poirier
Description:	(Read) read current status of input trigger signals

What:		/sys/bus/coresight/devices/<cti-name>/regs/trigoutstatus
Date:		March 2020
KernelVersion:	5.7
Contact:	Mike Leach or Mathieu Poirier
Description:	(Read) read current status of output trigger signals.

What:		/sys/bus/coresight/devices/<cti-name>/channels/trigin_attach
Date:		March 2020
KernelVersion:	5.7
Contact:	Mike Leach or Mathieu Poirier
Description:	(Write) Attach a CTI input trigger to a CTM channel.

What:		/sys/bus/coresight/devices/<cti-name>/channels/trigin_detach
Date:		March 2020
KernelVersion:	5.7
Contact:	Mike Leach or Mathieu Poirier
Description:	(Write) Detach a CTI input trigger from a CTM channel.

What:		/sys/bus/coresight/devices/<cti-name>/channels/trigout_attach
Date:		March 2020
KernelVersion:	5.7
Contact:	Mike Leach or Mathieu Poirier
Description:	(Write) Attach a CTI output trigger to a CTM channel.

What:		/sys/bus/coresight/devices/<cti-name>/channels/trigout_detach
Date:		March 2020
KernelVersion:	5.7
Contact:	Mike Leach or Mathieu Poirier
Description:	(Write) Detach a CTI output trigger from a CTM channel.

What:		/sys/bus/coresight/devices/<cti-name>/channels/chan_gate_enable
Date:		March 2020
KernelVersion:	5.7
Contact:	Mike Leach or Mathieu Poirier
Description:	(RW) Enable CTIGATE for single channel (Write) or list enabled
		channels through the gate (R).

What:		/sys/bus/coresight/devices/<cti-name>/channels/chan_gate_disable
Date:		March 2020
KernelVersion:	5.7
Contact:	Mike Leach or Mathieu Poirier
Description:	(Write) Disable CTIGATE for single channel.

What:		/sys/bus/coresight/devices/<cti-name>/channels/chan_set
Date:		March 2020
KernelVersion:	5.7
Contact:	Mike Leach or Mathieu Poirier
Description:	(Write) Activate a single channel.

What:		/sys/bus/coresight/devices/<cti-name>/channels/chan_clear
Date:		March 2020
KernelVersion:	5.7
Contact:	Mike Leach or Mathieu Poirier
Description:	(Write) Deactivate a single channel.

What:		/sys/bus/coresight/devices/<cti-name>/channels/chan_pulse
Date:		March 2020
KernelVersion:	5.7
Contact:	Mike Leach or Mathieu Poirier
Description:	(Write) Pulse a single channel - activate for a single clock cycle.

What:		/sys/bus/coresight/devices/<cti-name>/channels/trigout_filtered
Date:		March 2020
KernelVersion:	5.7
Contact:	Mike Leach or Mathieu Poirier
Description:	(Read) List of output triggers filtered across all connections.

What:		/sys/bus/coresight/devices/<cti-name>/channels/trig_filter_enable
Date:		March 2020
KernelVersion:	5.7
Contact:	Mike Leach or Mathieu Poirier
Description:	(RW) Enable or disable trigger output signal filtering.

What:		/sys/bus/coresight/devices/<cti-name>/channels/chan_inuse
Date:		March 2020
KernelVersion:	5.7
Contact:	Mike Leach or Mathieu Poirier
Description:	(Read) show channels with at least one attached trigger signal.

What:		/sys/bus/coresight/devices/<cti-name>/channels/chan_free
Date:		March 2020
KernelVersion:	5.7
Contact:	Mike Leach or Mathieu Poirier
Description:	(Read) show channels with no attached trigger signals.

What:		/sys/bus/coresight/devices/<cti-name>/channels/chan_xtrigs_sel
Date:		March 2020
KernelVersion:	5.7
Contact:	Mike Leach or Mathieu Poirier
Description:	(RW) Write channel number to select a channel to view, read to
		see selected channel number.

What:		/sys/bus/coresight/devices/<cti-name>/channels/chan_xtrigs_in
Date:		March 2020
KernelVersion:	5.7
Contact:	Mike Leach or Mathieu Poirier
Description:	(Read) Read to see input triggers connected to selected view
		channel.

What:		/sys/bus/coresight/devices/<cti-name>/channels/chan_xtrigs_out
Date:		March 2020
KernelVersion:	5.7
Contact:	Mike Leach or Mathieu Poirier
Description:	(Read) Read to see output triggers connected to selected view
		channel.

What:		/sys/bus/coresight/devices/<cti-name>/channels/chan_xtrigs_reset
Date:		March 2020
KernelVersion:	5.7
Contact:	Mike Leach or Mathieu Poirier
Description:	(Write) Clear all channel / trigger programming.
