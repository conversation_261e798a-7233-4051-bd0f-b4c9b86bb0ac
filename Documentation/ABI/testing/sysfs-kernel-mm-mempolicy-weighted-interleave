What:		/sys/kernel/mm/mempolicy/weighted_interleave/
Date:		January 2024
Contact:	Linux memory management mailing list <<EMAIL>>
Description:	Configuration Interface for the Weighted Interleave policy

What:		/sys/kernel/mm/mempolicy/weighted_interleave/nodeN
Date:		January 2024
Contact:	Linux memory management mailing list <<EMAIL>>
Description:	Weight configuration interface for nodeN

		The interleave weight for a memory node (N). These weights are
		utilized by tasks which have set their mempolicy to
		MPOL_WEIGHTED_INTERLEAVE.

		These weights only affect new allocations, and changes at runtime
		will not cause migrations on already allocated pages.

		The minimum weight for a node is always 1.

		Minimum weight: 1
		Maximum weight: 255

		Writing invalid values (i.e. any values not in [1,255],
		empty string, ...) will return -EINVAL.

		Changing the weight to a valid value will automatically
		switch the system to manual mode as well.

What:		/sys/kernel/mm/mempolicy/weighted_interleave/auto
Date:		May 2025
Contact:	Linux memory management mailing list <<EMAIL>>
Description:	Auto-weighting configuration interface

		Configuration mode for weighted interleave. 'true' indicates
		that the system is in auto mode, and a 'false' indicates that
		the system is in manual mode.

		In auto mode, all node weights are re-calculated and overwritten
		(visible via the nodeN interfaces) whenever new bandwidth data
		is made available during either boot or hotplug events.

		In manual mode, node weights can only be updated by the user.
		Note that nodes that are onlined with previously set weights
		will reuse those weights. If they were not previously set or
		are onlined with missing bandwidth data, the weights will use
		a default weight of 1.

		Writing any true value string (e.g. Y or 1) will enable auto
		mode, while writing any false value string (e.g. N or 0) will
		enable manual mode. All other strings are ignored and will
		return -EINVAL.

		Writing a new weight to a node directly via the nodeN interface
		will also automatically switch the system to manual mode.
