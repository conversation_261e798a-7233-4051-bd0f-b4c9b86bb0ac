What:		/sys/power/
Date:		August 2006
Contact:	<PERSON> <<EMAIL>>
Description:
		The /sys/power directory will contain files that will
		provide a unified interface to the power management
		subsystem.

What:		/sys/power/state
Date:		November 2016
Contact:	<PERSON> <<EMAIL>>
Description:
		The /sys/power/state file controls system sleep states.
		Reading from this file returns the available sleep state
		labels, which may be "mem" (suspend), "standby" (power-on
		suspend), "freeze" (suspend-to-idle) and "disk" (hibernation).

		Writing one of the above strings to this file causes the system
		to transition into the corresponding state, if available.

		See Documentation/admin-guide/pm/sleep-states.rst for more
		information.

What:		/sys/power/mem_sleep
Date:		November 2016
Contact:	<PERSON> <<EMAIL>>
Description:
		The /sys/power/mem_sleep file controls the operating mode of
		system suspend.  Reading from it returns the available modes
		as "s2idle" (always present), "shallow" and "deep" (present if
		supported).  The mode that will be used on subsequent attempts
		to suspend the system (by writing "mem" to the /sys/power/state
		file described above) is enclosed in square brackets.

		Writing one of the above strings to this file causes the mode
		represented by it to be used on subsequent attempts to suspend
		the system.

		See Documentation/admin-guide/pm/sleep-states.rst for more
		information.

What:		/sys/power/disk
Date:		September 2006
Contact:	Rafael J. Wysocki <<EMAIL>>
Description:
		The /sys/power/disk file controls the operating mode of the
		suspend-to-disk mechanism.  Reading from this file returns
		the name of the method by which the system will be put to
		sleep on the next suspend.  There are four methods supported:

		'firmware' - means that the memory image will be saved to disk
		by some firmware, in which case we also assume that the
		firmware will handle the system suspend.

		'platform' - the memory image will be saved by the kernel and
		the system will be put to sleep by the platform driver (e.g.
		ACPI or other PM registers).

		'shutdown' - the memory image will be saved by the kernel and
		the system will be powered off.

		'reboot' - the memory image will be saved by the kernel and
		the system will be rebooted.

		Additionally, /sys/power/disk can be used to turn on one of the
		two testing modes of the suspend-to-disk mechanism: 'testproc'
		or 'test'.  If the suspend-to-disk mechanism is in the
		'testproc' mode, writing 'disk' to /sys/power/state will cause
		the kernel to disable nonboot CPUs and freeze tasks, wait for 5
		seconds, unfreeze tasks and enable nonboot CPUs.  If it is in
		the 'test' mode, writing 'disk' to /sys/power/state will cause
		the kernel to disable nonboot CPUs and freeze tasks, shrink
		memory, suspend devices, wait for 5 seconds, resume devices,
		unfreeze tasks and enable nonboot CPUs.  Then, we are able to
		look in the log messages and work out, for example, which code
		is being slow and which device drivers are misbehaving.

		The suspend-to-disk method may be chosen by writing to this
		file one of the accepted strings:

		- 'firmware'
		- 'platform'
		- 'shutdown'
		- 'reboot'
		- 'testproc'
		- 'test'

		It will only change to 'firmware' or 'platform' if the system
		supports that.

What:		/sys/power/image_size
Date:		August 2006
Contact:	Rafael J. Wysocki <<EMAIL>>
Description:
		The /sys/power/image_size file controls the size of the image
		created by the suspend-to-disk mechanism.  It can be written a
		string representing a non-negative integer that will be used
		as an upper limit of the image size, in bytes.  The kernel's
		suspend-to-disk code will do its best to ensure the image size
		will not exceed this number.  However, if it turns out to be
		impossible, the kernel will try to suspend anyway using the
		smallest image possible.  In particular, if "0" is written to
		this file, the suspend image will be as small as possible.

		Reading from this file will display the current image size
		limit, which is set to around 2/5 of available RAM by default.

What:		/sys/power/pm_trace
Date:		August 2006
Contact:	Rafael J. Wysocki <<EMAIL>>
Description:
		The /sys/power/pm_trace file controls the code which saves the
		last PM event point in the RTC across reboots, so that you can
		debug a machine that just hangs during suspend (or more
		commonly, during resume).  Namely, the RTC is only used to save
		the last PM event point if this file contains '1'.  Initially
		it contains '0' which may be changed to '1' by writing a
		string representing a nonzero integer into it.

		To use this debugging feature you should attempt to suspend
		the machine, then reboot it and run::

		  dmesg -s 1000000 | grep 'hash matches'

		If you do not get any matches (or they appear to be false
		positives), it is possible that the last PM event point
		referred to a device created by a loadable kernel module.  In
		this case cat /sys/power/pm_trace_dev_match (see below) after
		your system is started up and the kernel modules are loaded.

		CAUTION: Using it will cause your machine's real-time (CMOS)
		clock to be set to a random invalid time after a resume.

What:		/sys/power/pm_trace_dev_match
Date:		October 2010
Contact:	James Hogan <<EMAIL>>
Description:
		The /sys/power/pm_trace_dev_match file contains the name of the
		device associated with the last PM event point saved in the RTC
		across reboots when pm_trace has been used.  More precisely it
		contains the list of current devices (including those
		registered by loadable kernel modules since boot) which match
		the device hash in the RTC at boot, with a newline after each
		one.

		The advantage of this file over the hash matches printed to the
		kernel log (see /sys/power/pm_trace), is that it includes
		devices created after boot by loadable kernel modules.

		Due to the small hash size necessary to fit in the RTC, it is
		possible that more than one device matches the hash, in which
		case further investigation is required to determine which
		device is causing the problem.  Note that genuine RTC clock
		values (such as when pm_trace has not been used), can still
		match a device and output its name here.

What:		/sys/power/pm_async
Date:		January 2009
Contact:	Rafael J. Wysocki <<EMAIL>>
Description:
		The /sys/power/pm_async file controls the switch allowing the
		user space to enable or disable asynchronous suspend and resume
		of devices.  If enabled, this feature will cause some device
		drivers' suspend and resume callbacks to be executed in parallel
		with each other and with the main suspend thread.  It is enabled
		if this file contains "1", which is the default.  It may be
		disabled by writing "0" to this file, in which case all devices
		will be suspended and resumed synchronously.

What:		/sys/power/wakeup_count
Date:		July 2010
Contact:	Rafael J. Wysocki <<EMAIL>>
Description:
		The /sys/power/wakeup_count file allows user space to put the
		system into a sleep state while taking into account the
		concurrent arrival of wakeup events.  Reading from it returns
		the current number of registered wakeup events and it blocks if
		some wakeup events are being processed at the time the file is
		read from.  Writing to it will only succeed if the current
		number of wakeup events is equal to the written value and, if
		successful, will make the kernel abort a subsequent transition
		to a sleep state if any wakeup events are reported after the
		write has returned.

What:		/sys/power/reserved_size
Date:		May 2011
Contact:	Rafael J. Wysocki <<EMAIL>>
Description:
		The /sys/power/reserved_size file allows user space to control
		the amount of memory reserved for allocations made by device
		drivers during the "device freeze" stage of hibernation.  It can
		be written a string representing a non-negative integer that
		will be used as the amount of memory to reserve for allocations
		made by device drivers' "freeze" callbacks, in bytes.

		Reading from this file will display the current value, which is
		set to 1 MB by default.

What:		/sys/power/autosleep
Date:		April 2012
Contact:	Rafael J. Wysocki <<EMAIL>>
Description:
		The /sys/power/autosleep file can be written one of the strings
		returned by reads from /sys/power/state.  If that happens, a
		work item attempting to trigger a transition of the system to
		the sleep state represented by that string is queued up.  This
		attempt will only succeed if there are no active wakeup sources
		in the system at that time.  After every execution, regardless
		of whether or not the attempt to put the system to sleep has
		succeeded, the work item requeues itself until user space
		writes "off" to /sys/power/autosleep.

		Reading from this file causes the last string successfully
		written to it to be returned.

What:		/sys/power/wake_lock
Date:		February 2012
Contact:	Rafael J. Wysocki <<EMAIL>>
Description:
		The /sys/power/wake_lock file allows user space to create
		wakeup source objects and activate them on demand (if one of
		those wakeup sources is active, reads from the
		/sys/power/wakeup_count file block or return false).  When a
		string without white space is written to /sys/power/wake_lock,
		it will be assumed to represent a wakeup source name.  If there
		is a wakeup source object with that name, it will be activated
		(unless active already).  Otherwise, a new wakeup source object
		will be registered, assigned the given name and activated.
		If a string written to /sys/power/wake_lock contains white
		space, the part of the string preceding the white space will be
		regarded as a wakeup source name and handled as descrived above.
		The other part of the string will be regarded as a timeout (in
		nanoseconds) such that the wakeup source will be automatically
		deactivated after it has expired.  The timeout, if present, is
		set regardless of the current state of the wakeup source object
		in question.

		Reads from this file return a string consisting of the names of
		wakeup sources created with the help of it that are active at
		the moment, separated with spaces.


What:		/sys/power/wake_unlock
Date:		February 2012
Contact:	Rafael J. Wysocki <<EMAIL>>
Description:
		The /sys/power/wake_unlock file allows user space to deactivate
		wakeup sources created with the help of /sys/power/wake_lock.
		When a string is written to /sys/power/wake_unlock, it will be
		assumed to represent the name of a wakeup source to deactivate.

		If a wakeup source object of that name exists and is active at
		the moment, it will be deactivated.

		Reads from this file return a string consisting of the names of
		wakeup sources created with the help of /sys/power/wake_lock
		that are inactive at the moment, separated with spaces.

What:		/sys/power/pm_print_times
Date:		May 2012
Contact:	Sameer Nanda <<EMAIL>>
Description:
		The /sys/power/pm_print_times file allows user space to
		control whether the time taken by devices to suspend and
		resume is printed.  These prints are useful for hunting down
		devices that take too long to suspend or resume.

		Writing a "1" enables this printing while writing a "0"
		disables it.  The default value is "0".  Reading from this file
		will display the current value.

What:		/sys/power/pm_wakeup_irq
Date:		April 2015
Contact:	Alexandra Yates <<EMAIL>>
Description:
		The /sys/power/pm_wakeup_irq file reports to user space the IRQ
		number of the first wakeup interrupt (that is, the first
		interrupt from an IRQ line armed for system wakeup) seen by the
		kernel during the most recent system suspend/resume cycle.

		This output is useful for system wakeup diagnostics of spurious
		wakeup interrupts.

What:		/sys/power/pm_debug_messages
Date:		July 2017
Contact:	Rafael J. Wysocki <<EMAIL>>
Description:
		The /sys/power/pm_debug_messages file controls the printing
		of debug messages from the system suspend/hiberbation
		infrastructure to the kernel log.

		Writing a "1" to this file enables the debug messages and
		writing a "0" (default) to it disables them.  Reads from
		this file return the current value.

What:		/sys/power/resume_offset
Date:		April 2018
Contact:	Mario Limonciello <<EMAIL>>
Description:
		This file is used for telling the kernel an offset into a disk
		to use when hibernating the system such as with a swap file.

		Reads from this file will display the current offset
		the kernel will be using on the next hibernation
		attempt.

		Using this sysfs file will override any values that were
		set using the kernel command line for disk offset.

What:		/sys/power/suspend_stats
Date:		July 2019
Contact:	Kalesh Singh <<EMAIL>>
Description:
		The /sys/power/suspend_stats directory contains suspend related
		statistics.

What:		/sys/power/suspend_stats/success
Date:		July 2019
Contact:	Kalesh Singh <<EMAIL>>
Description:
		The /sys/power/suspend_stats/success file contains the number
		of times entering system sleep state succeeded.

What:		/sys/power/suspend_stats/fail
Date:		July 2019
Contact:	Kalesh Singh <<EMAIL>>
Description:
		The /sys/power/suspend_stats/fail file contains the number
		of times entering system sleep state failed.

What:		/sys/power/suspend_stats/failed_freeze
Date:		July 2019
Contact:	Kalesh Singh <<EMAIL>>
Description:
		The /sys/power/suspend_stats/failed_freeze file contains the
		number of times freezing processes failed.

What:		/sys/power/suspend_stats/failed_prepare
Date:		July 2019
Contact:	Kalesh Singh <<EMAIL>>
Description:
		The /sys/power/suspend_stats/failed_prepare file contains the
		number of times preparing all non-sysdev devices for
		a system PM transition failed.

What:		/sys/power/suspend_stats/failed_resume
Date:		July 2019
Contact:	Kalesh Singh <<EMAIL>>
Description:
		The /sys/power/suspend_stats/failed_resume file contains the
		number of times executing "resume" callbacks of
		non-sysdev devices failed.

What:		/sys/power/suspend_stats/failed_resume_early
Date:		July 2019
Contact:	Kalesh Singh <<EMAIL>>
Description:
		The /sys/power/suspend_stats/failed_resume_early file contains
		the number of times executing "early resume" callbacks
		of devices failed.

What:		/sys/power/suspend_stats/failed_resume_noirq
Date:		July 2019
Contact:	Kalesh Singh <<EMAIL>>
Description:
		The /sys/power/suspend_stats/failed_resume_noirq file contains
		the number of times executing "noirq resume" callbacks
		of devices failed.

What:		/sys/power/suspend_stats/failed_suspend
Date:		July 2019
Contact:	Kalesh Singh <<EMAIL>>
Description:
		The /sys/power/suspend_stats/failed_suspend file contains
		the number of times executing "suspend" callbacks
		of all non-sysdev devices failed.

What:		/sys/power/suspend_stats/failed_suspend_late
Date:		July 2019
Contact:	Kalesh Singh <<EMAIL>>
Description:
		The /sys/power/suspend_stats/failed_suspend_late file contains
		the number of times executing "late suspend" callbacks
		of all devices failed.

What:		/sys/power/suspend_stats/failed_suspend_noirq
Date:		July 2019
Contact:	Kalesh Singh <<EMAIL>>
Description:
		The /sys/power/suspend_stats/failed_suspend_noirq file contains
		the number of times executing "noirq suspend" callbacks
		of all devices failed.

What:		/sys/power/suspend_stats/last_failed_dev
Date:		July 2019
Contact:	Kalesh Singh <<EMAIL>>
Description:
		The /sys/power/suspend_stats/last_failed_dev file contains
		the last device for which a suspend/resume callback failed.

What:		/sys/power/suspend_stats/last_failed_errno
Date:		July 2019
Contact:	Kalesh Singh <<EMAIL>>
Description:
		The /sys/power/suspend_stats/last_failed_errno file contains
		the errno of the last failed attempt at entering
		system sleep state.

What:		/sys/power/suspend_stats/last_failed_step
Date:		July 2019
Contact:	Kalesh Singh <<EMAIL>>
Description:
		The /sys/power/suspend_stats/last_failed_step file contains
		the last failed step in the suspend/resume path.

What:		/sys/power/suspend_stats/last_hw_sleep
Date:		June 2023
Contact:	Mario Limonciello <<EMAIL>>
Description:
		The /sys/power/suspend_stats/last_hw_sleep file
		contains the duration of time spent in a hardware sleep
		state in the most recent system suspend-resume cycle.
		This number is measured in microseconds.

What:		/sys/power/suspend_stats/total_hw_sleep
Date:		June 2023
Contact:	Mario Limonciello <<EMAIL>>
Description:
		The /sys/power/suspend_stats/total_hw_sleep file
		contains the aggregate of time spent in a hardware sleep
		state since the kernel was booted. This number
		is measured in microseconds.

What:		/sys/power/suspend_stats/max_hw_sleep
Date:		June 2023
Contact:	Mario Limonciello <<EMAIL>>
Description:
		The /sys/power/suspend_stats/max_hw_sleep file
		contains the maximum amount of time that the hardware can
		report for time spent in a hardware sleep state. When sleep
		cycles are longer than this time, the values for
		'total_hw_sleep' and 'last_hw_sleep' may not be accurate.
		This number is measured in microseconds.

What:		/sys/power/sync_on_suspend
Date:		October 2019
Contact:	Jonas Meurer <<EMAIL>>
Description:
		This file controls whether or not the kernel will sync()
		filesystems during system suspend (after freezing user space
		and before suspending devices).

		Writing a "1" to this file enables the sync() and writing a "0"
		disables it.  Reads from the file return the current value.
		The default is "1" if the build-time "SUSPEND_SKIP_SYNC" config
		flag is unset, or "0" otherwise.
