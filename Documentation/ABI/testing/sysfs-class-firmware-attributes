What:		/sys/class/firmware-attributes/*/attributes/*/
Date:		February 2021
KernelVersion:	5.11
Contact:	<PERSON><PERSON><PERSON> <Divya.<PERSON><EMAIL>>,
		<PERSON>rasanth K<PERSON> <<EMAIL>>
		<EMAIL>
Description:
		A sysfs interface for systems management software to enable
		configuration capability on supported systems.  This directory
		exposes interfaces for interacting with configuration options.

		Unless otherwise specified in an attribute description all attributes are optional
		and will accept UTF-8 input.

		type:
		    A file that can be read to obtain the type of attribute.
		    This attribute is mandatory.

		The following are known types:

			- enumeration: a set of pre-defined valid values
			- integer: a range of numerical values
			- string

		HP specific types
		-----------------
			- ordered-list - a set of ordered list valid values


		All attribute types support the following values:

		current_value:
				A file that can be read to obtain the current
				value of the <attr>.

				This file can also be written to in order to update the value of a
				<attr>

				This attribute is mandatory.

		default_value:
				A file that can be read to obtain the default
				value of the <attr>

		display_name:
				A file that can be read to obtain a user friendly
				description of the at <attr>

		display_name_language_code:
						A file that can be read to obtain
						the IETF language tag corresponding to the
						"display_name" of the <attr>

		"enumeration"-type specific properties:

		possible_values:
					A file that can be read to obtain the possible
					values of the <attr>. Values are separated using
					semi-colon (``;``).

		"integer"-type specific properties:

		min_value:
				A file that can be read to obtain the lower
				bound value of the <attr>

		max_value:
				A file that can be read to obtain the upper
				bound value of the <attr>

		scalar_increment:
					A file that can be read to obtain the scalar value used for
					increments of current_value this attribute accepts.

		"string"-type specific properties:

		max_length:
				A file that can be read to obtain the maximum
				length value of the <attr>

		min_length:
				A file that can be read to obtain the minimum
				length value of the <attr>

		Dell specific class extensions
		------------------------------

		On Dell systems the following additional attributes are available:

		dell_modifier:
				A file that can be read to obtain attribute-level
				dependency rule. It says an attribute X will become read-only or
				suppressed, if/if-not attribute Y is configured.

				modifier rules can be in following format::

				    [ReadOnlyIf:<attribute>=<value>]
				    [ReadOnlyIfNot:<attribute>=<value>]
				    [SuppressIf:<attribute>=<value>]
				    [SuppressIfNot:<attribute>=<value>]

				For example::

				    AutoOnFri/dell_modifier has value,
					    [SuppressIfNot:AutoOn=SelectDays]

				This means AutoOnFri will be suppressed in BIOS setup if AutoOn
				attribute is not "SelectDays" and its value will not be effective
				through sysfs until this rule is met.

		Enumeration attributes also support the following:

		dell_value_modifier:
					A file that can be read to obtain value-level dependency.
					This file is similar to dell_modifier but here,	an
					attribute's current value will be forcefully changed based
					dependent attributes value.

					dell_value_modifier rules can be in following format::

					    <value>[ForceIf:<attribute>=<value>]
					    <value>[ForceIfNot:<attribute>=<value>]

					For example::

					    LegacyOrom/dell_value_modifier has value:
						    Disabled[ForceIf:SecureBoot=Enabled]

					This means LegacyOrom's current value will be forced to
					"Disabled" in BIOS setup if SecureBoot is Enabled and its
					value will not be effective through sysfs until this rule is
					met.

		HP specific class extensions
		------------------------------

		On HP systems the following additional attributes are available:

		"ordered-list"-type specific properties:

		elements:
					A file that can be read to obtain the possible
					list of values of the <attr>. Values are separated using
					semi-colon (``;``) and listed according to their priority.
					An element listed first has the highest priority. Writing
					the list in a different order to current_value alters
					the priority order for the particular attribute.

What:		/sys/class/firmware-attributes/*/authentication/
Date:		February 2021
KernelVersion:	5.11
Contact:	Divya Bharathi <<EMAIL>>,
		Prasanth KSR <<EMAIL>>
		<EMAIL>
Description:
		Devices support various authentication mechanisms which can be exposed
		as a separate configuration object.

		For example a "BIOS Admin" password and "System" Password can be set,
		reset or cleared using these attributes.

		- An "Admin" password is used for preventing modification to the BIOS
		  settings.
		- A "System" password is required to boot a machine.

		Change in any of these two authentication methods will also generate an
		uevent KOBJ_CHANGE.

		is_enabled:
					A file that can be read to obtain a 0/1 flag to see if
					<attr> authentication is enabled.
					This attribute is mandatory.

		role:
					The type of authentication used.
					This attribute is mandatory.

					Known types:
						bios-admin:
							Representing BIOS administrator password
						power-on:
							Representing a password required to use
							the system
						system-mgmt:
							Representing System Management password.
							See Lenovo extensions section for details
						HDD:
							Representing HDD password
							See Lenovo extensions section for details
						NVMe:
							Representing NVMe password
							See Lenovo extensions section for details

		mechanism:
					The means of authentication.  This attribute is mandatory.
					Supported types are "password" or "certificate".

		max_password_length:
					A file that can be read to obtain the
					maximum length of the Password

		min_password_length:
					A file that can be read to obtain the
					minimum length of the Password

		current_password:
					A write only value used for privileged access such as
					setting	attributes when a system or admin password is set
					or resetting to a new password

					This attribute is mandatory when mechanism == "password".

		new_password:
					A write only value that when used in tandem with
					current_password will reset a system or admin password.

		Note, password management is session specific. If Admin password is set,
		same password must be written into current_password file (required for
		password-validation) and must be cleared once the session is over.
		For example::

			echo "password" > current_password
			echo "disabled" > TouchScreen/current_value
			echo "" > current_password

		Drivers may emit a CHANGE uevent when a password is set or unset
		userspace may check it again.

		On Dell, Lenovo and HP systems, if Admin password is set, then all BIOS attributes
		require password validation.
		On Lenovo systems if you change the Admin password the new password is not active until
		the next boot.

		Lenovo specific class extensions
		--------------------------------

		On Lenovo systems the following additional settings are available:

		role: system-mgmt	This gives the same authority as the bios-admin password to control
					security related features. The authorities allocated can be set via
					the BIOS menu SMP Access Control Policy

		role: HDD & NVMe	This password is used to unlock access to the drive at boot. Note see
					'level' and 'index' extensions below.

		lenovo_encoding:
					The encoding method that is used. This can be either "ascii"
					or "scancode". Default is set to "ascii"

		lenovo_kbdlang:
					The keyboard language method that is used. This is generally a
					two char code (e.g. "us", "fr", "gr") and may vary per platform.
					Default is set to "us"

		level:
					Available for HDD and NVMe authentication to set 'user' or 'master'
					privilege level.
					If only the user password is configured then this should be used to
					unlock the drive at boot. If both master and user passwords are set
					then either can be used. If a master password is set a user password
					is required.
					This attribute defaults to 'user' level

		index:
					Used with HDD and NVME authentication to set the drive index
					that is being referenced (e.g hdd1, hdd2 etc)
					This attribute defaults to device 1.

		certificate, signature, save_signature:
					These attributes are used for certificate based authentication. This is
					used in conjunction with a signing server as an alternative to password
					based authentication.
					The user writes to the attribute(s) with a BASE64 encoded string obtained
					from the signing server.
					The attributes can be displayed to check the stored value.

					Some usage examples:

						Installing a certificate to enable feature::

							echo "supervisor password" > authentication/Admin/current_password
							echo "signed certificate" > authentication/Admin/certificate

						Updating the installed certificate::

							echo "signature" > authentication/Admin/signature
							echo "signed certificate" > authentication/Admin/certificate

						Removing the installed certificate::

							echo "signature" > authentication/Admin/signature
							echo "" > authentication/Admin/certificate

						Changing a BIOS setting::

							echo "signature" > authentication/Admin/signature
							echo "save signature" > authentication/Admin/save_signature
							echo Enable > attribute/PasswordBeep/current_value

					You cannot enable certificate authentication if a supervisor password
					has not been set.
					Clearing the certificate results in no bios-admin authentication method
					being configured allowing anyone to make changes.
					After any of these operations the system must reboot for the changes to
					take effect.
					Admin and System certificates are supported from 2025 systems onward.

		certificate_thumbprint:
					Read only attribute used to display the MD5, SHA1 and SHA256 thumbprints
					for the certificate installed in the BIOS.

		certificate_to_password:
					Write only attribute used to switch from certificate based authentication
					back to password based.
					Usage::

						echo "signature" > authentication/Admin/signature
						echo "password" > authentication/Admin/certificate_to_password

		HP specific class extensions
		--------------------------------

		On HP systems the following additional settings are available:

		role: enhanced-bios-auth:
					This role is specific to Secure Platform Management (SPM) attribute.
					It requires configuring an endorsement (kek) and signing certificate (sk).


What:		/sys/class/firmware-attributes/*/attributes/pending_reboot
Date:		February 2021
KernelVersion:	5.11
Contact:	Divya Bharathi <<EMAIL>>,
		Prasanth KSR <<EMAIL>>
		<EMAIL>
Description:
		A read-only attribute reads 1 if a reboot is necessary to apply
		pending BIOS attribute changes. Also, an uevent_KOBJ_CHANGE is
		generated when it changes to 1.

			==	=========================================
			0	All BIOS attributes setting are current
			1	A reboot is necessary to get pending BIOS
				attribute changes applied
			==	=========================================

		Note, userspace applications need to follow below steps for efficient
		BIOS management,

		1.	Check if admin password is set. If yes, follow session method for
			password management as briefed under authentication section above.
		2.	Before setting any attribute, check if it has any modifiers
			or value_modifiers. If yes, incorporate them and then modify
			attribute.

		Drivers may emit a CHANGE uevent when this value changes and userspace
		may check it again.

What:		/sys/class/firmware-attributes/*/attributes/reset_bios
Date:		February 2021
KernelVersion:	5.11
Contact:	Divya Bharathi <<EMAIL>>,
		Prasanth KSR <<EMAIL>>
		<EMAIL>
Description:
		This attribute can be used to reset the BIOS Configuration.
		Specifically, it tells which type of reset BIOS configuration is being
		requested on the host.

		Reading from it returns a list of supported options encoded as:

			- 'builtinsafe' (Built in safe configuration profile)
			- 'lastknowngood' (Last known good saved configuration profile)
			- 'factory' (Default factory settings configuration profile)
			- 'custom' (Custom saved configuration profile)

		The currently selected option is printed in square brackets as
		shown below::

		    # echo "factory" > /sys/class/firmware-attributes/*/device/attributes/reset_bios
		    # cat /sys/class/firmware-attributes/*/device/attributes/reset_bios
		    builtinsafe lastknowngood [factory] custom

		Note that any changes to this attribute requires a reboot
		for changes to take effect.

What:		/sys/class/firmware-attributes/*/attributes/save_settings
Date:		August 2023
KernelVersion:	6.6
Contact:	Mark Pearson <<EMAIL>>
Description:
		On Lenovo platforms there is a limitation in the number of times an attribute can be
		saved. This is an architectural limitation and it limits the number of attributes
		that can be modified to 48.
		A solution for this is instead of the attribute being saved after every modification,
		to allow a user to bulk set the attributes, and then trigger a final save. This allows
		unlimited attributes.

		Read the attribute to check what save mode is enabled (single or bulk).
		E.g:
		# cat /sys/class/firmware-attributes/thinklmi/attributes/save_settings
		single

		Write the attribute with 'bulk' to enable bulk save mode.
		Write the attribute with 'single' to enable saving, after every attribute set.
		The default setting is single mode.
		E.g:
		# echo bulk > /sys/class/firmware-attributes/thinklmi/attributes/save_settings

		When in bulk mode write 'save' to trigger a save of all currently modified attributes.
		Note, once a save has been triggered, in bulk mode, attributes can no longer be set and
		will return a permissions error. This is to prevent users hitting the 48+ save limitation
		(which requires entering the BIOS to clear the error condition)
		E.g:
		# echo save > /sys/class/firmware-attributes/thinklmi/attributes/save_settings

What:		/sys/class/firmware-attributes/*/attributes/debug_cmd
Date:		July 2021
KernelVersion:	5.14
Contact:	Mark Pearson <<EMAIL>>
Description:
		This write only attribute can be used to send debug commands to the BIOS.
		This should only be used when recommended by the BIOS vendor. Vendors may
		use it to enable extra debug attributes or BIOS features for testing purposes.

		Note that any changes to this attribute requires a reboot for changes to take effect.


		HP specific class extensions - Secure Platform Manager (SPM)
		--------------------------------

What:		/sys/class/firmware-attributes/*/authentication/SPM/kek
Date:		March 2023
KernelVersion:	5.18
Contact:	"Jorge Lopez" <<EMAIL>>
Description:
		'kek' Key-Encryption-Key is a write-only file that can be used to configure the
		RSA public key that will be used by the BIOS to verify
		signatures when setting the signing key.  When written,
		the bytes should correspond to the KEK certificate
		(x509 .DER format containing an OU).  The size of the
		certificate must be less than or equal to 4095 bytes.

What:		/sys/class/firmware-attributes/*/authentication/SPM/sk
Date:		March 2023
KernelVersion:	5.18
Contact:	"Jorge Lopez" <<EMAIL>>
Description:
		'sk' Signature Key is a write-only file that can be used to configure the RSA
		public key that will be used by the BIOS to verify signatures
		when configuring BIOS settings and security features.  When
		written, the bytes should correspond to the modulus of the
		public key.  The exponent is assumed to be 0x10001.

What:		/sys/class/firmware-attributes/*/authentication/SPM/status
Date:		March 2023
KernelVersion:	5.18
Contact:	"Jorge Lopez" <<EMAIL>>
Description:
		'status' is a read-only file that returns ASCII text in JSON format reporting
		the status information.

		  "State": "not provisioned | provisioned | provisioning in progress",
		  "Version": "Major.Minor",
		  "Nonce": <16-bit unsigned number display in base 10>,
		  "FeaturesInUse": <16-bit unsigned number display in base 10>,
		  "EndorsementKeyMod": "<256 bytes in base64>",
		  "SigningKeyMod": "<256 bytes in base64>"

What:		/sys/class/firmware-attributes/*/attributes/Sure_Start/audit_log_entries
Date:		March 2023
KernelVersion:	5.18
Contact:	"Jorge Lopez" <<EMAIL>>
Description:
		'audit_log_entries' is a read-only file that returns the events in the log.

			Audit log entry format

			Byte 0-15:   Requested Audit Log entry  (Each Audit log is 16 bytes)
			Byte 16-127: Unused

What:		/sys/class/firmware-attributes/*/attributes/Sure_Start/audit_log_entry_count
Date:		March 2023
KernelVersion:	5.18
Contact:	"Jorge Lopez" <<EMAIL>>
Description:
		'audit_log_entry_count' is a read-only file that returns the number of existing
		audit log events available to be read. Values are separated using comma. (``,``)

			[No of entries],[log entry size],[Max number of entries supported]

		log entry size identifies audit log size for the current BIOS version.
		The current size is 16 bytes but it can be up to 128 bytes long in future BIOS
		versions.
