What:		/sys/fs/f2fs/<disk>/gc_max_sleep_time
Date:		July 2013
Contact:	"Namja<PERSON> Je<PERSON>" <<EMAIL>>
Description:	Controls the maximum sleep time for gc_thread. Time
		is in milliseconds.

What:		/sys/fs/f2fs/<disk>/gc_min_sleep_time
Date:		July 2013
Contact:	"Namja<PERSON> Je<PERSON>" <<EMAIL>>
Description:	Controls the minimum sleep time for gc_thread. Time
		is in milliseconds.

What:		/sys/fs/f2fs/<disk>/gc_no_gc_sleep_time
Date:		July 2013
Contact:	"<PERSON>ja<PERSON>" <<EMAIL>>
Description:	Controls the default sleep time for gc_thread. Time
		is in milliseconds.

What:		/sys/fs/f2fs/<disk>/gc_idle
Date:		July 2013
Contact:	"Namjae Jeon" <<EMAIL>>
Description:	Controls the victim selection policy for garbage collection.
		Setting gc_idle = 0(default) will disable this option. Setting:

		===========  ===============================================
		gc_idle = 1  will select the Cost Benefit approach & setting
		gc_idle = 2  will select the greedy approach & setting
		gc_idle = 3  will select the age-threshold based approach.
		===========  ===============================================

What:		/sys/fs/f2fs/<disk>/reclaim_segments
Date:		October 2013
Contact:	"Jaegeuk Kim" <<EMAIL>>
Description:	This parameter controls the number of prefree segments to be
		reclaimed. If the number of prefree segments is larger than
		the number of segments in the proportion to the percentage
		over total volume size, f2fs tries to conduct checkpoint to
		reclaim the prefree segments to free segments.
		By default, 5% over total # of segments.

What:		/sys/fs/f2fs/<disk>/main_blkaddr
Date:		November 2019
Contact:	"Ramon Pantin" <<EMAIL>>
Description:	Shows first block address of MAIN area.

What:		/sys/fs/f2fs/<disk>/ipu_policy
Date:		November 2013
Contact:	"Jaegeuk Kim" <<EMAIL>>
Description:	Controls the in-place-update policy.
		updates in f2fs. User can set:

		===== =============== ===================================================
		value policy          description
		0x00  DISABLE         disable IPU(=default option in LFS mode)
		0x01  FORCE           all the time
		0x02  SSR             if SSR mode is activated
		0x04  UTIL            if FS utilization is over threshold
		0x08  SSR_UTIL        if SSR mode is activated and FS utilization is over
		                      threshold
		0x10  FSYNC           activated in fsync path only for high performance
		                      flash storages. IPU will be triggered only if the
		                      # of dirty pages over min_fsync_blocks.
		                      (=default option)
		0x20  ASYNC           do IPU given by asynchronous write requests
		0x40  NOCACHE         disable IPU bio cache
		0x80  HONOR_OPU_WRITE use OPU write prior to IPU write if inode has
		                      FI_OPU_WRITE flag
		===== =============== ===================================================

		Refer segment.h for details.

What:		/sys/fs/f2fs/<disk>/min_ipu_util
Date:		November 2013
Contact:	"Jaegeuk Kim" <<EMAIL>>
Description:	Controls the FS utilization condition for the in-place-update
		policies. It is used by F2FS_IPU_UTIL and F2FS_IPU_SSR_UTIL policies.

What:		/sys/fs/f2fs/<disk>/min_fsync_blocks
Date:		September 2014
Contact:	"Jaegeuk Kim" <<EMAIL>>
Description:	Controls the dirty page count condition for the in-place-update
		policies.

What:		/sys/fs/f2fs/<disk>/min_seq_blocks
Date:		August 2018
Contact:	"Jaegeuk Kim" <<EMAIL>>
Description:	Controls the dirty page count condition for batched sequential
		writes in writepages.

What:		/sys/fs/f2fs/<disk>/min_hot_blocks
Date:		March 2017
Contact:	"Jaegeuk Kim" <<EMAIL>>
Description:	Controls the dirty page count condition for redefining hot data.

What:		/sys/fs/f2fs/<disk>/min_ssr_sections
Date:		October 2017
Contact:	"Chao Yu" <<EMAIL>>
Description:	Controls the free section threshold to trigger SSR allocation.
		If this is large, SSR mode will be enabled early.

What:		/sys/fs/f2fs/<disk>/max_small_discards
Date:		November 2013
Contact:	"Jaegeuk Kim" <<EMAIL>>
Description:	Controls the issue rate of discard commands that consist of small
		blocks less than 2MB. The candidates to be discarded are cached during
		checkpoint, and issued by issue_discard thread after checkpoint.
		It is enabled by default.

What:		/sys/fs/f2fs/<disk>/max_ordered_discard
Date:		October 2022
Contact:	"Yangtao Li" <<EMAIL>>
Description:	Controls the maximum ordered discard, the unit size is one block(4KB).
		Set it to 16 by default.

What:		/sys/fs/f2fs/<disk>/max_discard_request
Date:		December 2021
Contact:	"Konstantin Vyshetsky" <<EMAIL>>
Description:	Controls the number of discards a thread will issue at a time.
		Higher number will allow the discard thread to finish its work
		faster, at the cost of higher latency for incoming I/O.

What:		/sys/fs/f2fs/<disk>/min_discard_issue_time
Date:		December 2021
Contact:	"Konstantin Vyshetsky" <<EMAIL>>
Description:	Controls the interval the discard thread will wait between
		issuing discard requests when there are discards to be issued and
		no I/O aware interruptions occur.

What:		/sys/fs/f2fs/<disk>/mid_discard_issue_time
Date:		December 2021
Contact:	"Konstantin Vyshetsky" <<EMAIL>>
Description:	Controls the interval the discard thread will wait between
		issuing discard requests when there are discards to be issued and
		an I/O aware interruption occurs.

What:		/sys/fs/f2fs/<disk>/max_discard_issue_time
Date:		December 2021
Contact:	"Konstantin Vyshetsky" <<EMAIL>>
Description:	Controls the interval the discard thread will wait when there are
		no discard operations to be issued.

What:		/sys/fs/f2fs/<disk>/discard_granularity
Date:		July 2017
Contact:	"Chao Yu" <<EMAIL>>
Description:	Controls discard granularity of inner discard thread. Inner thread
		will not issue discards with size that is smaller than granularity.
		The unit size is one block(4KB), now only support configuring
		in range of [1, 512]. Default value is 16.
		For small devices, default value is 1.

What:		/sys/fs/f2fs/<disk>/umount_discard_timeout
Date:		January 2019
Contact:	"Jaegeuk Kim" <<EMAIL>>
Description:	Set timeout to issue discard commands during umount.
	        Default: 5 secs

What:		/sys/fs/f2fs/<disk>/pending_discard
Date:		November 2021
Contact:	"Jaegeuk Kim" <<EMAIL>>
Description:	Shows the number of pending discard commands in the queue.

What:		/sys/fs/f2fs/<disk>/max_victim_search
Date:		January 2014
Contact:	"Jaegeuk Kim" <<EMAIL>>
Description:	Controls the number of trials to find a victim segment
		when conducting SSR and cleaning operations. The default value
		is 4096 which covers 8GB block address range.

What:		/sys/fs/f2fs/<disk>/migration_granularity
Date:		October 2018
Contact:	"Chao Yu" <<EMAIL>>
Description:	Controls migration granularity of garbage collection on large
		section, it can let GC move partial segment{s} of one section
		in one GC cycle, so that dispersing heavy overhead GC to
		multiple lightweight one.

What:		/sys/fs/f2fs/<disk>/dir_level
Date:		March 2014
Contact:	"Jaegeuk Kim" <<EMAIL>>
Description:	Controls the directory level for large directory. If a
		directory has a number of files, it can reduce the file lookup
		latency by increasing this dir_level value. Otherwise, it
		needs to decrease this value to reduce the space overhead.
		The default value is 0.

What:		/sys/fs/f2fs/<disk>/ram_thresh
Date:		March 2014
Contact:	"Jaegeuk Kim" <<EMAIL>>
Description:	Controls the memory footprint used by free nids and cached
		nat entries. By default, 1 is set, which indicates
		10 MB / 1 GB RAM.

What:		/sys/fs/f2fs/<disk>/cp_interval
Date:		October 2015
Contact:	"Jaegeuk Kim" <<EMAIL>>
Description:	Controls the checkpoint timing, set to 60 seconds by default.

What:		/sys/fs/f2fs/<disk>/idle_interval
Date:		January 2016
Contact:	"Jaegeuk Kim" <<EMAIL>>
Description:	Controls the idle timing of system, if there is no FS operation
		during given interval.
		Set to 5 seconds by default.

What:		/sys/fs/f2fs/<disk>/discard_idle_interval
Date:		September 2018
Contact:	"Chao Yu" <<EMAIL>>
Contact:	"Sahitya Tummala" <<EMAIL>>
Description:	Controls the idle timing of discard thread given
		this time interval.
		Default is 5 secs.

What:		/sys/fs/f2fs/<disk>/gc_idle_interval
Date:		September 2018
Contact:	"Chao Yu" <<EMAIL>>
Contact:	"Sahitya Tummala" <<EMAIL>>
Description:    Controls the idle timing for gc path. Set to 5 seconds by default.

What:		/sys/fs/f2fs/<disk>/iostat_enable
Date:		August 2017
Contact:	"Chao Yu" <<EMAIL>>
Description:	Controls to enable/disable IO stat.

What:		/sys/fs/f2fs/<disk>/ra_nid_pages
Date:		October 2015
Contact:	"Chao Yu" <<EMAIL>>
Description:	Controls the count of nid pages to be readaheaded.
		When building free nids, F2FS reads NAT blocks ahead for
		speed up. Default is 0.

What:		/sys/fs/f2fs/<disk>/dirty_nats_ratio
Date:		January 2016
Contact:	"Chao Yu" <<EMAIL>>
Description:	Controls dirty nat entries ratio threshold, if current
		ratio exceeds configured threshold, checkpoint will
		be triggered for flushing dirty nat entries.

What:		/sys/fs/f2fs/<disk>/lifetime_write_kbytes
Date:		January 2016
Contact:	"Shuoran Liu" <<EMAIL>>
Description:	Shows total written kbytes issued to disk.

What:		/sys/fs/f2fs/<disk>/features
Date:		July 2017
Contact:	"Jaegeuk Kim" <<EMAIL>>
Description:	<deprecated: should use /sys/fs/f2fs/<disk>/feature_list/>
		Shows all enabled features in current device.
		Supported features:
		encryption, blkzoned, extra_attr, projquota, inode_checksum,
		flexible_inline_xattr, quota_ino, inode_crtime, lost_found,
		verity, sb_checksum, casefold, readonly, compression, pin_file.

What:		/sys/fs/f2fs/<disk>/feature_list/
Date:		June 2021
Contact:	"Jaegeuk Kim" <<EMAIL>>
Description:	Expand /sys/fs/f2fs/<disk>/features to meet sysfs rule.
		Supported on-disk features:
		encryption, block_zoned (aka blkzoned), extra_attr,
		project_quota (aka projquota), inode_checksum,
		flexible_inline_xattr, quota_ino, inode_crtime, lost_found,
		verity, sb_checksum, casefold, readonly, compression.
		Note that, pin_file is moved into /sys/fs/f2fs/features/.

What:		/sys/fs/f2fs/features/
Date:		July 2017
Contact:	"Jaegeuk Kim" <<EMAIL>>
Description:	Shows all enabled kernel features.
		Supported features:
		encryption, block_zoned, extra_attr, project_quota,
		inode_checksum, flexible_inline_xattr, quota_ino,
		inode_crtime, lost_found, verity, sb_checksum,
		casefold, readonly, compression, test_dummy_encryption_v2,
		atomic_write, pin_file, encrypted_casefold, linear_lookup.

What:		/sys/fs/f2fs/<disk>/inject_rate
Date:		May 2016
Contact:	"Sheng Yong" <<EMAIL>>
Description:	Controls the injection rate of arbitrary faults.

What:		/sys/fs/f2fs/<disk>/inject_type
Date:		May 2016
Contact:	"Sheng Yong" <<EMAIL>>
Description:	Controls the injection type of arbitrary faults.

What:		/sys/fs/f2fs/<disk>/dirty_segments
Date:		October 2017
Contact:	"Jaegeuk Kim" <<EMAIL>>
Description:	Shows the number of dirty segments.

What:		/sys/fs/f2fs/<disk>/reserved_blocks
Date:		June 2017
Contact:	"Chao Yu" <<EMAIL>>
Description:	Controls target reserved blocks in system, the threshold
		is soft, it could exceed current available user space.

What:		/sys/fs/f2fs/<disk>/current_reserved_blocks
Date:		October 2017
Contact:	"Yunlong Song" <<EMAIL>>
Contact:	"Chao Yu" <<EMAIL>>
Description:	Shows current reserved blocks in system, it may be temporarily
		smaller than target_reserved_blocks, but will gradually
		increase to target_reserved_blocks when more free blocks are
		freed by user later.

What:		/sys/fs/f2fs/<disk>/gc_urgent
Date:		August 2017
Contact:	"Jaegeuk Kim" <<EMAIL>>
Description:	Do background GC aggressively when set. Set to 0 by default.
		gc urgent high(1): does GC forcibly in a period of given
		gc_urgent_sleep_time and ignores I/O idling check. uses greedy
		GC approach and turns SSR mode on.
		gc urgent low(2): lowers the bar of checking I/O idling in
		order to process outstanding discard commands and GC a
		little bit aggressively. always uses cost benefit GC approach,
		and will override age-threshold GC approach if ATGC is enabled
		at the same time.
		gc urgent mid(3): does GC forcibly in a period of given
		gc_urgent_sleep_time and executes a mid level of I/O idling check.
		always uses cost benefit GC approach, and will override
		age-threshold GC approach if ATGC is enabled at the same time.

What:		/sys/fs/f2fs/<disk>/gc_urgent_sleep_time
Date:		August 2017
Contact:	"Jaegeuk Kim" <<EMAIL>>
Description:	Controls sleep time of GC urgent mode. Set to 500ms by default.

What:		/sys/fs/f2fs/<disk>/readdir_ra
Date:		November 2017
Contact:	"Sheng Yong" <<EMAIL>>
Description:	Controls readahead inode block in readdir. Enabled by default.

What:		/sys/fs/f2fs/<disk>/gc_pin_file_thresh
Date:		January 2018
Contact:	Jaegeuk Kim <<EMAIL>>
Description:	This indicates how many GC can be failed for the pinned
		file. If it exceeds this, F2FS doesn't guarantee its pinning
		state. 2048 trials is set by default, and 65535 as maximum.

What:		/sys/fs/f2fs/<disk>/extension_list
Date:		February 2018
Contact:	"Chao Yu" <<EMAIL>>
Description:	Used to control configure extension list:
		- Query: cat /sys/fs/f2fs/<disk>/extension_list
		- Add: echo '[h/c]extension' > /sys/fs/f2fs/<disk>/extension_list
		- Del: echo '[h/c]!extension' > /sys/fs/f2fs/<disk>/extension_list
		- [h] means add/del hot file extension
		- [c] means add/del cold file extension

What:		/sys/fs/f2fs/<disk>/unusable
Date:		April 2019
Contact:	"Daniel Rosenberg" <<EMAIL>>
Description:	If checkpoint=disable, it displays the number of blocks that
		are unusable.
		If checkpoint=enable it displays the number of blocks that
		would be unusable if checkpoint=disable were to be set.

What:		/sys/fs/f2fs/<disk>/encoding
Date:		July 2019
Contact:	"Daniel Rosenberg" <<EMAIL>>
Description:	Displays name and version of the encoding set for the filesystem.
		If no encoding is set, displays (none)

What:		/sys/fs/f2fs/<disk>/free_segments
Date:		September 2019
Contact:	"Hridya Valsaraju" <<EMAIL>>
Description:	Number of free segments in disk.

What:		/sys/fs/f2fs/<disk>/cp_foreground_calls
Date:		September 2019
Contact:	"Hridya Valsaraju" <<EMAIL>>
Description:	Number of checkpoint operations performed on demand. Available when
		CONFIG_F2FS_STAT_FS=y.

What:		/sys/fs/f2fs/<disk>/cp_background_calls
Date:		September 2019
Contact:	"Hridya Valsaraju" <<EMAIL>>
Description:	Number of checkpoint operations performed in the background to
		free segments. Available when CONFIG_F2FS_STAT_FS=y.

What:		/sys/fs/f2fs/<disk>/gc_foreground_calls
Date:		September 2019
Contact:	"Hridya Valsaraju" <<EMAIL>>
Description:	Number of garbage collection operations performed on demand.
		Available when CONFIG_F2FS_STAT_FS=y.

What:		/sys/fs/f2fs/<disk>/gc_background_calls
Date:		September 2019
Contact:	"Hridya Valsaraju" <<EMAIL>>
Description:	Number of garbage collection operations triggered in background.
		Available when CONFIG_F2FS_STAT_FS=y.

What:		/sys/fs/f2fs/<disk>/moved_blocks_foreground
Date:		September 2019
Contact:	"Hridya Valsaraju" <<EMAIL>>
Description:	Number of blocks moved by garbage collection in foreground.
		Available when CONFIG_F2FS_STAT_FS=y.

What:		/sys/fs/f2fs/<disk>/moved_blocks_background
Date:		September 2019
Contact:	"Hridya Valsaraju" <<EMAIL>>
Description:	Number of blocks moved by garbage collection in background.
		Available when CONFIG_F2FS_STAT_FS=y.

What:		/sys/fs/f2fs/<disk>/avg_vblocks
Date:		September 2019
Contact:	"Hridya Valsaraju" <<EMAIL>>
Description:	Average number of valid blocks.
		Available when CONFIG_F2FS_STAT_FS=y.

What:		/sys/fs/f2fs/<disk>/mounted_time_sec
Date:		February 2020
Contact:	"Jaegeuk Kim" <<EMAIL>>
Description:	Show the mounted time in secs of this partition.

What:		/sys/fs/f2fs/<disk>/data_io_flag
Date:		April 2020
Contact:	"Jaegeuk Kim" <<EMAIL>>
Description:	Give a way to attach REQ_META|FUA to data writes
		given temperature-based bits. Now the bits indicate:

		+-------------------+-------------------+
		|      REQ_META     |      REQ_FUA      |
		+------+------+-----+------+------+-----+
		|    5 |    4 |   3 |    2 |    1 |   0 |
		+------+------+-----+------+------+-----+
		| Cold | Warm | Hot | Cold | Warm | Hot |
		+------+------+-----+------+------+-----+

What:		/sys/fs/f2fs/<disk>/node_io_flag
Date:		June 2020
Contact:	"Jaegeuk Kim" <<EMAIL>>
Description:	Give a way to attach REQ_META|FUA to node writes
		given temperature-based bits. Now the bits indicate:

		+-------------------+-------------------+
		|      REQ_META     |      REQ_FUA      |
		+------+------+-----+------+------+-----+
		|    5 |    4 |   3 |    2 |    1 |   0 |
		+------+------+-----+------+------+-----+
		| Cold | Warm | Hot | Cold | Warm | Hot |
		+------+------+-----+------+------+-----+

What:		/sys/fs/f2fs/<disk>/iostat_period_ms
Date:		April 2020
Contact:	"Daeho Jeong" <<EMAIL>>
Description:	Give a way to change iostat_period time. 3secs by default.
		The new iostat trace gives stats gap given the period.
What:		/sys/fs/f2fs/<disk>/max_io_bytes
Date:		December 2020
Contact:	"Jaegeuk Kim" <<EMAIL>>
Description:	This gives a control to limit the bio size in f2fs.
		Default is zero, which will follow underlying block layer limit,
		whereas, if it has a certain bytes value, f2fs won't submit a
		bio larger than that size.

What:		/sys/fs/f2fs/<disk>/stat/sb_status
Date:		December 2020
Contact:	"Chao Yu" <<EMAIL>>
Description:	Show status of f2fs superblock in real time.

		====== ===================== =================================
		value  sb status macro       description
		0x1    SBI_IS_DIRTY          dirty flag for checkpoint
		0x2    SBI_IS_CLOSE          specify unmounting
		0x4    SBI_NEED_FSCK         need fsck.f2fs to fix
		0x8    SBI_POR_DOING         recovery is doing or not
		0x10   SBI_NEED_SB_WRITE     need to recover superblock
		0x20   SBI_NEED_CP           need to checkpoint
		0x40   SBI_IS_SHUTDOWN       shutdown by ioctl
		0x80   SBI_IS_RECOVERED      recovered orphan/data
		0x100  SBI_CP_DISABLED       CP was disabled last mount
		0x200  SBI_CP_DISABLED_QUICK CP was disabled quickly
		0x400  SBI_QUOTA_NEED_FLUSH  need to flush quota info in CP
		0x800  SBI_QUOTA_SKIP_FLUSH  skip flushing quota in current CP
		0x1000 SBI_QUOTA_NEED_REPAIR quota file may be corrupted
		0x2000 SBI_IS_RESIZEFS       resizefs is in process
		0x4000 SBI_IS_FREEZING       freefs is in process
		====== ===================== =================================

What:		/sys/fs/f2fs/<disk>/stat/cp_status
Date:		September 2022
Contact:	"Chao Yu" <<EMAIL>>
Description:	Show status of f2fs checkpoint in real time.

		=============================== ==============================
		cp flag				value
		CP_UMOUNT_FLAG			0x00000001
		CP_ORPHAN_PRESENT_FLAG		0x00000002
		CP_COMPACT_SUM_FLAG		0x00000004
		CP_ERROR_FLAG			0x00000008
		CP_FSCK_FLAG			0x00000010
		CP_FASTBOOT_FLAG		0x00000020
		CP_CRC_RECOVERY_FLAG		0x00000040
		CP_NAT_BITS_FLAG		0x00000080
		CP_TRIMMED_FLAG			0x00000100
		CP_NOCRC_RECOVERY_FLAG		0x00000200
		CP_LARGE_NAT_BITMAP_FLAG	0x00000400
		CP_QUOTA_NEED_FSCK_FLAG		0x00000800
		CP_DISABLED_FLAG		0x00001000
		CP_DISABLED_QUICK_FLAG		0x00002000
		CP_RESIZEFS_FLAG		0x00004000
		=============================== ==============================

What:		/sys/fs/f2fs/<disk>/stat/issued_discard
Date:		December 2023
Contact:	"Zhiguo Niu" <<EMAIL>>
Description:	Shows the number of issued discard.

What:		/sys/fs/f2fs/<disk>/stat/queued_discard
Date:		December 2023
Contact:	"Zhiguo Niu" <<EMAIL>>
Description:	Shows the number of queued discard.

What:		/sys/fs/f2fs/<disk>/stat/undiscard_blks
Date:		December 2023
Contact:	"Zhiguo Niu" <<EMAIL>>
Description:	Shows the total number of undiscard blocks.

What:		/sys/fs/f2fs/<disk>/ckpt_thread_ioprio
Date:		January 2021
Contact:	"Daeho Jeong" <<EMAIL>>
Description:	Give a way to change checkpoint merge daemon's io priority.
		Its default value is "be,3", which means "BE" I/O class and
		I/O priority "3". We can select the class between "rt" and "be",
		and set the I/O priority within valid range of it. "," delimiter
		is necessary in between I/O class and priority number.

What:		/sys/fs/f2fs/<disk>/ovp_segments
Date:		March 2021
Contact:	"Jaegeuk Kim" <<EMAIL>>
Description:	Shows the number of overprovision segments.

What:		/sys/fs/f2fs/<disk>/compr_written_block
Date:		March 2021
Contact:	"Daeho Jeong" <<EMAIL>>
Description:	Show the block count written after compression since mount. Note
		that when the compressed blocks are deleted, this count doesn't
		decrease. If you write "0" here, you can initialize
		compr_written_block and compr_saved_block to "0".

What:		/sys/fs/f2fs/<disk>/compr_saved_block
Date:		March 2021
Contact:	"Daeho Jeong" <<EMAIL>>
Description:	Show the saved block count with compression since mount. Note
		that when the compressed blocks are deleted, this count doesn't
		decrease. If you write "0" here, you can initialize
		compr_written_block and compr_saved_block to "0".

What:		/sys/fs/f2fs/<disk>/compr_new_inode
Date:		March 2021
Contact:	"Daeho Jeong" <<EMAIL>>
Description:	Show the count of inode newly enabled for compression since mount.
		Note that when the compression is disabled for the files, this count
		doesn't decrease. If you write "0" here, you can initialize
		compr_new_inode to "0".

What:		/sys/fs/f2fs/<disk>/atgc_candidate_ratio
Date:		May 2021
Contact:	"Chao Yu" <<EMAIL>>
Description:	When ATGC is on, it controls candidate ratio in order to limit total
		number of potential victim in all candidates, the value should be in
		range of [0, 100], by default it was initialized as 20(%).

What:		/sys/fs/f2fs/<disk>/atgc_candidate_count
Date:		May 2021
Contact:	"Chao Yu" <<EMAIL>>
Description:	When ATGC is on, it controls candidate count in order to limit total
		number of potential victim in all candidates, by default it was
		initialized as 10 (sections).

What:		/sys/fs/f2fs/<disk>/atgc_age_weight
Date:		May 2021
Contact:	"Chao Yu" <<EMAIL>>
Description:	When ATGC is on, it controls age weight to balance weight proportion
		in between aging and valid blocks, the value should be in range of
		[0, 100], by default it was initialized as 60(%).

What:		/sys/fs/f2fs/<disk>/atgc_age_threshold
Date:		May 2021
Contact:	"Chao Yu" <<EMAIL>>
Description:	When ATGC is on, it controls age threshold to bypass GCing young
		candidates whose age is not beyond the threshold, by default it was
		initialized as 604800 seconds (equals to 7 days).

What:		/sys/fs/f2fs/<disk>/atgc_enabled
Date:		Feb 2024
Contact:	"Jinbao Liu" <<EMAIL>>
Description:	It represents whether ATGC is on or off. The value is 1 which
               indicates that ATGC is on, and 0 indicates that it is off.

What:		/sys/fs/f2fs/<disk>/gc_reclaimed_segments
Date:		July 2021
Contact:	"Daeho Jeong" <<EMAIL>>
Description:	Show how many segments have been reclaimed by GC during a specific
		GC mode (0: GC normal, 1: GC idle CB, 2: GC idle greedy,
		3: GC idle AT, 4: GC urgent high, 5: GC urgent low 6: GC urgent mid)
		You can re-initialize this value to "0".

What:		/sys/fs/f2fs/<disk>/gc_segment_mode
Date:		July 2021
Contact:	"Daeho Jeong" <<EMAIL>>
Description:	You can control for which gc mode the "gc_reclaimed_segments" node shows.
		Refer to the description of the modes in "gc_reclaimed_segments".

What:		/sys/fs/f2fs/<disk>/seq_file_ra_mul
Date:		July 2021
Contact:	"Daeho Jeong" <<EMAIL>>
Description:	You can	control the multiplier value of	bdi device readahead window size
		between 2 (default) and 256 for POSIX_FADV_SEQUENTIAL advise option.

What:		/sys/fs/f2fs/<disk>/max_fragment_chunk
Date:		August 2021
Contact:	"Daeho Jeong" <<EMAIL>>
Description:	With "mode=fragment:block" mount options, we can scatter block allocation.
		f2fs will allocate 1..<max_fragment_chunk> blocks in a chunk and make a hole
		in the length of 1..<max_fragment_hole> by turns. This value can be set
		between 1..512 and the default value is 4.

What:		/sys/fs/f2fs/<disk>/max_fragment_hole
Date:		August 2021
Contact:	"Daeho Jeong" <<EMAIL>>
Description:	With "mode=fragment:block" mount options, we can scatter block allocation.
		f2fs will allocate 1..<max_fragment_chunk> blocks in a chunk and make a hole
		in the length of 1..<max_fragment_hole> by turns. This value can be set
		between 1..512 and the default value is 4.

What:		/sys/fs/f2fs/<disk>/gc_remaining_trials
Date:		October 2022
Contact:	"Yangtao Li" <<EMAIL>>
Description:	You can set the trial count limit for GC urgent and idle mode with this value.
		If GC thread gets to the limit, the mode will turn back to GC normal mode.
		By default, the value is zero, which means there is no limit like before.

What:		/sys/fs/f2fs/<disk>/max_roll_forward_node_blocks
Date:		January 2022
Contact:	"Jaegeuk Kim" <<EMAIL>>
Description:	Controls max # of node block writes to be used for roll forward
		recovery. This can limit the roll forward recovery time.

What:		/sys/fs/f2fs/<disk>/unusable_blocks_per_sec
Date:		June 2022
Contact:	"Jaegeuk Kim" <<EMAIL>>
Description:	Shows the number of unusable blocks in a section which was defined by
		the zone capacity reported by underlying zoned device.

What:		/sys/fs/f2fs/<disk>/current_atomic_write
Date:		July 2022
Contact:	"Daeho Jeong" <<EMAIL>>
Description:	Show the total current atomic write block count, which is not committed yet.
		This is a read-only entry.

What:		/sys/fs/f2fs/<disk>/peak_atomic_write
Date:		July 2022
Contact:	"Daeho Jeong" <<EMAIL>>
Description:	Show the peak value of total current atomic write block count after boot.
		If you write "0" here, you can initialize to "0".

What:		/sys/fs/f2fs/<disk>/committed_atomic_block
Date:		July 2022
Contact:	"Daeho Jeong" <<EMAIL>>
Description:	Show the accumulated total committed atomic write block count after boot.
		If you write "0" here, you can initialize to "0".

What:		/sys/fs/f2fs/<disk>/revoked_atomic_block
Date:		July 2022
Contact:	"Daeho Jeong" <<EMAIL>>
Description:	Show the accumulated total revoked atomic write block count after boot.
		If you write "0" here, you can initialize to "0".

What:		/sys/fs/f2fs/<disk>/gc_mode
Date:		October 2022
Contact:	"Yangtao Li" <<EMAIL>>
Description:	Show the current gc_mode as a string.
		This is a read-only entry.

What:		/sys/fs/f2fs/<disk>/discard_urgent_util
Date:		November 2022
Contact:	"Yangtao Li" <<EMAIL>>
Description:	When space utilization exceeds this, do background DISCARD aggressively.
		Does DISCARD forcibly in a period of given min_discard_issue_time when the number
		of discards is not 0 and set discard granularity to 1.
		Default: 80

What:		/sys/fs/f2fs/<disk>/hot_data_age_threshold
Date:		November 2022
Contact:	"Ping Xiong" <<EMAIL>>
Description:	When DATA SEPARATION is on, it controls the age threshold to indicate
		the data blocks as hot. By default it was initialized as 262144 blocks
		(equals to 1GB).

What:		/sys/fs/f2fs/<disk>/warm_data_age_threshold
Date:		November 2022
Contact:	"Ping Xiong" <<EMAIL>>
Description:	When DATA SEPARATION is on, it controls the age threshold to indicate
		the data blocks as warm. By default it was initialized as 2621440 blocks
		(equals to 10GB).

What:		/sys/fs/f2fs/<disk>/fault_rate
Date:		May 2016
Contact:	"Sheng Yong" <<EMAIL>>
Contact:	"Chao Yu" <<EMAIL>>
Description:	Enable fault injection in all supported types with
		specified injection rate.

What:		/sys/fs/f2fs/<disk>/fault_type
Date:		May 2016
Contact:	"Sheng Yong" <<EMAIL>>
Contact:	"Chao Yu" <<EMAIL>>
Description:	Support configuring fault injection type, should be
		enabled with fault_injection option, fault type value
		is shown below, it supports single or combined type.

		===========================      ==========
		Type_Name                        Type_Value
		===========================      ==========
		FAULT_KMALLOC                    0x00000001
		FAULT_KVMALLOC                   0x00000002
		FAULT_PAGE_ALLOC                 0x00000004
		FAULT_PAGE_GET                   0x00000008
		FAULT_ALLOC_BIO                  0x00000010 (obsolete)
		FAULT_ALLOC_NID                  0x00000020
		FAULT_ORPHAN                     0x00000040
		FAULT_BLOCK                      0x00000080
		FAULT_DIR_DEPTH                  0x00000100
		FAULT_EVICT_INODE                0x00000200
		FAULT_TRUNCATE                   0x00000400
		FAULT_READ_IO                    0x00000800
		FAULT_CHECKPOINT                 0x00001000
		FAULT_DISCARD                    0x00002000
		FAULT_WRITE_IO                   0x00004000
		FAULT_SLAB_ALLOC                 0x00008000
		FAULT_DQUOT_INIT                 0x00010000
		FAULT_LOCK_OP                    0x00020000
		FAULT_BLKADDR_VALIDITY           0x00040000
		FAULT_BLKADDR_CONSISTENCE        0x00080000
		FAULT_NO_SEGMENT                 0x00100000
		FAULT_INCONSISTENT_FOOTER        0x00200000
		FAULT_TIMEOUT                    0x00400000 (1000ms)
		FAULT_VMALLOC                    0x00800000
		===========================      ==========

What:		/sys/fs/f2fs/<disk>/discard_io_aware_gran
Date:		January 2023
Contact:	"Yangtao Li" <<EMAIL>>
Description:	Controls background discard granularity of inner discard thread
		when is not in idle. Inner thread will not issue discards with size that
		is smaller than granularity. The unit size is one block(4KB), now only
		support configuring in range of [0, 512].
		Default: 512

What:		/sys/fs/f2fs/<disk>/last_age_weight
Date:		January 2023
Contact:	"Ping Xiong" <<EMAIL>>
Description:	When DATA SEPARATION is on, it controls the weight of last data block age.

What:		/sys/fs/f2fs/<disk>/compress_watermark
Date:		February 2023
Contact:	"Yangtao Li" <<EMAIL>>
Description:	When compress cache is on, it controls free memory watermark
		in order to limit caching compress page. If free memory is lower
		than watermark, then deny caching compress page. The value should be in
		range of (0, 100], by default it was initialized as 20(%).

What:		/sys/fs/f2fs/<disk>/compress_percent
Date:		February 2023
Contact:	"Yangtao Li" <<EMAIL>>
Description:	When compress cache is on, it controls cached page
		percent(compress pages / free_ram) in order to limit caching compress page.
		If cached page percent exceed threshold, then deny caching compress page.
		The value should be in range of (0, 100], by default it was initialized
		as 20(%).

What:		/sys/fs/f2fs/<disk>/discard_io_aware
Date:		November 2023
Contact:	"Chao Yu" <<EMAIL>>
Description:	It controls to enable/disable IO aware feature for background discard.
		By default, the value is 1 which indicates IO aware is on.

What:		/sys/fs/f2fs/<disk>/blkzone_alloc_policy
Date:		July 2024
Contact:	"Yuanhong Liao" <<EMAIL>>
Description:	The zone UFS we are currently using consists of two parts:
		conventional zones and sequential zones. It can be used to control which part
		to prioritize for writes, with a default value of 0.

		========================  =========================================
		value					  description
		blkzone_alloc_policy = 0  Prioritize writing to sequential zones
		blkzone_alloc_policy = 1  Only allow writing to sequential zones
		blkzone_alloc_policy = 2  Prioritize writing to conventional zones
		========================  =========================================

What:		/sys/fs/f2fs/<disk>/migration_window_granularity
Date:		September 2024
Contact:	"Daeho Jeong" <<EMAIL>>
Description:	Controls migration window granularity of garbage collection on large
		section. it can control the scanning window granularity for GC migration
		in a unit of segment, while migration_granularity controls the number
		of segments which can be migrated at the same turn.

What:		/sys/fs/f2fs/<disk>/reserved_segments
Date:		September 2024
Contact:	"Daeho Jeong" <<EMAIL>>
Description:	In order to fine tune GC behavior, we can control the number of
		reserved segments.

What:		/sys/fs/f2fs/<disk>/gc_no_zoned_gc_percent
Date:		September 2024
Contact:	"Daeho Jeong" <<EMAIL>>
Description:	If the percentage of free sections over total sections is above this
		number, F2FS do not garbage collection for zoned devices through the
		background GC thread. the default number is "60".

What:		/sys/fs/f2fs/<disk>/gc_boost_zoned_gc_percent
Date:		September 2024
Contact:	"Daeho Jeong" <<EMAIL>>
Description:	If the percentage of free sections over total sections is under this
		number, F2FS boosts garbage collection for zoned devices through the
		background GC thread. the default number is "25".

What:		/sys/fs/f2fs/<disk>/gc_valid_thresh_ratio
Date:		September 2024
Contact:	"Daeho Jeong" <<EMAIL>>
Description:	It controls the valid block ratio threshold not to trigger excessive GC
		for zoned deivces. The initial value of it is 95(%). F2FS will stop the
		background GC thread from intiating GC for sections having valid blocks
		exceeding the ratio.

What:		/sys/fs/f2fs/<disk>/max_read_extent_count
Date:		November 2024
Contact:	"Chao Yu" <<EMAIL>>
Description:	It controls max read extent count for per-inode, the value of threshold
		is 10240 by default.

What:		/sys/fs/f2fs/tuning/reclaim_caches_kb
Date:		February 2025
Contact:	"Jaegeuk Kim" <<EMAIL>>
Description:	It reclaims the given KBs of file-backed pages registered by
		ioctl(F2FS_IOC_DONATE_RANGE).
		For example, writing N tries to drop N KBs spaces in LRU.

What:		/sys/fs/f2fs/<disk>/carve_out
Date:		March 2025
Contact:	"Daeho Jeong" <<EMAIL>>
Description:	For several zoned storage devices, vendors will provide extra space which
		was used for device level GC than specs and F2FS can use this space for
		filesystem level GC. To do that, we can reserve the space using
		reserved_blocks. However, it is not enough, since this extra space should
		not be shown to users. So, with this new sysfs node, we can hide the space
		by substracting reserved_blocks from total bytes.

What:		/sys/fs/f2fs/<disk>/encoding_flags
Date:		April 2025
Contact:	"Chao Yu" <<EMAIL>>
Description:	This is a read-only entry to show the value of sb.s_encoding_flags, the
		value is hexadecimal.

		============================     ==========
		Flag_Name                        Flag_Value
		============================     ==========
		SB_ENC_STRICT_MODE_FL            0x00000001
		SB_ENC_NO_COMPAT_FALLBACK_FL     0x00000002
		============================     ==========
