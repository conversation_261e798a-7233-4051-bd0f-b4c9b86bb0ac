What:		/sys/kernel/debug/hisi_hpre/<bdf>/cluster[0-3]/regs
Date:		Sep 2019
Contact:	<EMAIL>
Description:	Dump debug registers from the HPRE cluster.
		Only available for PF.

What:		/sys/kernel/debug/hisi_hpre/<bdf>/cluster[0-3]/cluster_ctrl
Date:		Sep 2019
Contact:	<EMAIL>
Description:	Write the HPRE core selection in the cluster into this file,
		and then we can read the debug information of the core.
		Only available for PF.

What:		/sys/kernel/debug/hisi_hpre/<bdf>/rdclr_en
Date:		Sep 2019
Contact:	<EMAIL>
Description:	HPRE cores debug registers read clear control. 1 means enable
		register read clear, otherwise 0. Writing to this file has no
		functional effect, only enable or disable counters clear after
		reading of these registers.
		Only available for PF.

What:		/sys/kernel/debug/hisi_hpre/<bdf>/current_qm
Date:		Sep 2019
Contact:	<EMAIL>
Description:	One HPRE controller has one PF and multiple VFs, each function
		has a QM. Select the QM which below qm refers to.
		Only available for PF.

What:		/sys/kernel/debug/hisi_hpre/<bdf>/alg_qos
Date:		Jun 2021
Contact:	<EMAIL>
Description:	The <bdf> is related the function for PF and VF.
		HPRE driver supports to configure each function's QoS, the driver
		supports to write <bdf> value to alg_qos in the host. Such as
		"echo <bdf> value > alg_qos". The qos value is 1~1000, means
		1/1000~1000/1000 of total QoS. The driver reading alg_qos to
		get related QoS in the host and VM, Such as "cat alg_qos".

What:		/sys/kernel/debug/hisi_hpre/<bdf>/regs
Date:		Sep 2019
Contact:	<EMAIL>
Description:	Dump debug registers from the HPRE.
		Only available for PF.

What:		/sys/kernel/debug/hisi_hpre/<bdf>/qm/regs
Date:		Sep 2019
Contact:	<EMAIL>
Description:	Dump debug registers from the QM.
		Available for PF and VF in host. VF in guest currently only
		has one debug register.

What:		/sys/kernel/debug/hisi_hpre/<bdf>/qm/current_q
Date:		Sep 2019
Contact:	<EMAIL>
Description:	One QM may contain multiple queues. Select specific queue to
		show its debug registers in above regs.
		Only available for PF.

What:		/sys/kernel/debug/hisi_hpre/<bdf>/qm/clear_enable
Date:		Sep 2019
Contact:	<EMAIL>
Description:	QM debug registers(regs) read clear control. 1 means enable
		register read clear, otherwise 0.
		Writing to this file has no functional effect, only enable or
		disable counters clear after reading of these registers.
		Only available for PF.

What:		/sys/kernel/debug/hisi_hpre/<bdf>/qm/err_irq
Date:		Apr 2020
Contact:	<EMAIL>
Description:	Dump the number of invalid interrupts for
		QM task completion.
		Available for both PF and VF, and take no other effect on HPRE.

What:		/sys/kernel/debug/hisi_hpre/<bdf>/qm/aeq_irq
Date:		Apr 2020
Contact:	<EMAIL>
Description:	Dump the number of QM async event queue interrupts.
		Available for both PF and VF, and take no other effect on HPRE.

What:		/sys/kernel/debug/hisi_hpre/<bdf>/qm/abnormal_irq
Date:		Apr 2020
Contact:	<EMAIL>
Description:	Dump the number of interrupts for QM abnormal event.
		Available for both PF and VF, and take no other effect on HPRE.

What:		/sys/kernel/debug/hisi_hpre/<bdf>/qm/create_qp_err
Date:		Apr 2020
Contact:	<EMAIL>
Description:	Dump the number of queue allocation errors.
		Available for both PF and VF, and take no other effect on HPRE.

What:		/sys/kernel/debug/hisi_hpre/<bdf>/qm/mb_err
Date:		Apr 2020
Contact:	<EMAIL>
Description:	Dump the number of failed QM mailbox commands.
		Available for both PF and VF, and take no other effect on HPRE.

What:		/sys/kernel/debug/hisi_hpre/<bdf>/qm/status
Date:		Apr 2020
Contact:	<EMAIL>
Description:	Dump the status of the QM.
		Two states: work, stop.
		Available for both PF and VF, and take no other effect on HPRE.

What:		/sys/kernel/debug/hisi_hpre/<bdf>/qm/diff_regs
Date:		Mar 2022
Contact:	<EMAIL>
Description:	QM debug registers(regs) read hardware register value. This
		node is used to show the change of the qm register values. This
		node can be help users to check the change of register values.

What:		/sys/kernel/debug/hisi_hpre/<bdf>/qm/qm_state
Date:		Jan 2024
Contact:	<EMAIL>
Description:	Dump the state of the device.
		0: busy, 1: idle.
		Only available for PF, and take no other effect on HPRE.

What:		/sys/kernel/debug/hisi_hpre/<bdf>/qm/dev_timeout
Date:		Feb 2024
Contact:	<EMAIL>
Description:	Set the wait time when stop queue fails. Available for both PF
		and VF, and take no other effect on HPRE.
		0: not wait(default), others value: wait dev_timeout * 20 microsecond.

What:		/sys/kernel/debug/hisi_hpre/<bdf>/qm/dev_state
Date:		Feb 2024
Contact:	<EMAIL>
Description:	Dump the stop queue status of the QM. The default value is 0,
		if dev_timeout is set, when stop queue fails, the dev_state
		will return non-zero value. Available for both PF and VF,
		and take no other effect on HPRE.

What:		/sys/kernel/debug/hisi_hpre/<bdf>/hpre_dfx/diff_regs
Date:		Mar 2022
Contact:	<EMAIL>
Description:	HPRE debug registers(regs) read hardware register value. This
		node is used to show the change of the register values. This
		node can be help users to check the change of register values.

What:		/sys/kernel/debug/hisi_hpre/<bdf>/hpre_dfx/send_cnt
Date:		Apr 2020
Contact:	<EMAIL>
Description:	Dump the total number of sent requests.
		Available for both PF and VF, and take no other effect on HPRE.

What:		/sys/kernel/debug/hisi_hpre/<bdf>/hpre_dfx/recv_cnt
Date:		Apr 2020
Contact:	<EMAIL>
Description:	Dump the total number of received requests.
		Available for both PF and VF, and take no other effect on HPRE.

What:		/sys/kernel/debug/hisi_hpre/<bdf>/hpre_dfx/send_busy_cnt
Date:		Apr 2020
Contact:	<EMAIL>
Description:	Dump the total number of requests sent
		with returning busy.
		Available for both PF and VF, and take no other effect on HPRE.

What:		/sys/kernel/debug/hisi_hpre/<bdf>/hpre_dfx/send_fail_cnt
Date:		Apr 2020
Contact:	<EMAIL>
Description:	Dump the total number of completed but error requests.
		Available for both PF and VF, and take no other effect on HPRE.

What:		/sys/kernel/debug/hisi_hpre/<bdf>/hpre_dfx/invalid_req_cnt
Date:		Apr 2020
Contact:	<EMAIL>
Description:	Dump the total number of invalid requests being received.
		Available for both PF and VF, and take no other effect on HPRE.

What:		/sys/kernel/debug/hisi_hpre/<bdf>/hpre_dfx/overtime_thrhld
Date:		Apr 2020
Contact:	<EMAIL>
Description:	Set the threshold time for counting the request which is
		processed longer than the threshold.
		0: disable(default), 1: 1 microsecond.
		Available for both PF and VF, and take no other effect on HPRE.

What:		/sys/kernel/debug/hisi_hpre/<bdf>/hpre_dfx/over_thrhld_cnt
Date:		Apr 2020
Contact:	<EMAIL>
Description:	Dump the total number of time out requests.
		Available for both PF and VF, and take no other effect on HPRE.

What:           /sys/kernel/debug/hisi_hpre/<bdf>/cap_regs
Date:           Oct 2024
Contact:        <EMAIL>
Description:    Dump the values of the qm and hpre capability bit registers and
                support the query of device specifications to facilitate fault locating.
                Available for both PF and VF, and take no other effect on HPRE.
