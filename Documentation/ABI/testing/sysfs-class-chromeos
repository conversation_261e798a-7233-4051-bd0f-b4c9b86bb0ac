What:		/sys/class/chromeos/<ec-device-name>/flashinfo
Date:		August 2015
KernelVersion:	4.2
Description:
		Show the EC flash information.

What:		/sys/class/chromeos/<ec-device-name>/kb_wake_angle
Date:		March 2018
KernelVersion:	4.17
Description:
		Control the keyboard wake lid angle. Values are between
		0 and 360. This file will also show the keyboard wake lid
		angle by querying the hardware.

What:		/sys/class/chromeos/<ec-device-name>/reboot
Date:		August 2015
KernelVersion:	4.2
Description:
		Tell the EC to reboot in various ways. Options are:

		- "cancel": Cancel a pending reboot.
		- "ro": Jump to RO without rebooting.
		- "rw": Jump to RW without rebooting.
		- "cold": Cold reboot.
		- "disable-jump": Disable jump until next reboot.
		- "hibernate": Hibernate the EC.
		- "at-shutdown": Reboot after an AP shutdown.

What:		/sys/class/chromeos/<ec-device-name>/version
Date:		August 2015
KernelVersion:	4.2
Description:
		Show the information about the EC software and hardware.

What:		/sys/class/chromeos/cros_ec/usbpdmuxinfo
Date:		February 2025
Description:
		Show PD mux status for each typec port with following flags:
		- "USB": USB connected
		- "DP": DP connected
		- "POLARITY": CC line Polarity inverted
		- "HPD_IRQ": Hot Plug Detect interrupt is asserted
		- "HPD_LVL": Hot Plug Detect level is asserted
		- "SAFE": DP is in safe mode
		- "TBT": TBT enabled
		- "USB4": USB4 enabled

What:		/sys/class/chromeos/cros_ec/ap_mode_entry
Date:		February 2025
Description:
		Show if the AP mode entry EC feature is supported.
		It indicates whether the EC waits for direction from the AP
		to enter Type-C altmodes or USB4 mode.
