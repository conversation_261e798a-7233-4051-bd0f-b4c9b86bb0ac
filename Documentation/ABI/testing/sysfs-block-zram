What:		/sys/block/zram<id>/disksize
Date:		August 2010
Contact:	<PERSON><PERSON> <<EMAIL>>
Description:
		The disksize file is read-write and specifies the disk size
		which represents the limit on the *uncompressed* worth of data
		that can be stored in this disk.
		Unit: bytes

What:		/sys/block/zram<id>/initstate
Date:		August 2010
Contact:	<PERSON><PERSON> <<EMAIL>>
Description:
		The initstate file is read-only and shows the initialization
		state of the device.

What:		/sys/block/zram<id>/reset
Date:		August 2010
Contact:	<PERSON><PERSON> <<EMAIL>>
Description:
		The reset file is write-only and allows resetting the
		device. The reset operation frees all the memory associated
		with this device.

What:		/sys/block/zram<id>/comp_algorithm
Date:		February 2014
Contact:	<PERSON> <sergey.senoz<PERSON><PERSON>@gmail.com>
Description:
		The comp_algorithm file is read-write and lets to show
		available and selected compression algorithms, change
		compression algorithm selection.

What:		/sys/block/zram<id>/mem_used_max
Date:		August 2014
Contact:	<PERSON><PERSON> <<EMAIL>>
Description:
		The mem_used_max file is write-only and is used to reset
		the counter of maximum memory zram have consumed to store
		compressed data. For resetting the value, you should write
		"0". Otherwise, you could see -EINVAL.
		Unit: bytes

What:		/sys/block/zram<id>/mem_limit
Date:		August 2014
Contact:	Minchan Kim <<EMAIL>>
Description:
		The mem_limit file is write-only and specifies the maximum
		amount of memory ZRAM can use to store the compressed data.
		The limit could be changed in run time and "0" means disable
		the limit. No limit is the initial state.  Unit: bytes

What:		/sys/block/zram<id>/compact
Date:		August 2015
Contact:	Minchan Kim <<EMAIL>>
Description:
		The compact file is write-only and trigger compaction for
		allocator zrm uses. The allocator moves some objects so that
		it could free fragment space.

What:		/sys/block/zram<id>/io_stat
Date:		August 2015
Contact:	Sergey Senozhatsky <<EMAIL>>
Description:
		The io_stat file is read-only and accumulates device's I/O
		statistics not accounted by block layer. For example,
		failed_reads, failed_writes, etc. File format is similar to
		block layer statistics file format.

What:		/sys/block/zram<id>/mm_stat
Date:		August 2015
Contact:	Sergey Senozhatsky <<EMAIL>>
Description:
		The mm_stat file is read-only and represents device's mm
		statistics (orig_data_size, compr_data_size, etc.) in a format
		similar to block layer statistics file format.

What:		/sys/block/zram<id>/debug_stat
Date:		July 2016
Contact:	Sergey Senozhatsky <<EMAIL>>
Description:
		The debug_stat file is read-only and represents various
		device's debugging info useful for kernel developers. Its
		format is not documented intentionally and may change
		anytime without any notice.

What:		/sys/block/zram<id>/backing_dev
Date:		June 2017
Contact:	Minchan Kim <<EMAIL>>
Description:
		The backing_dev file is read-write and set up backing
		device for zram to write incompressible pages.
		For using, user should enable CONFIG_ZRAM_WRITEBACK.

What:		/sys/block/zram<id>/idle
Date:		November 2018
Contact:	Minchan Kim <<EMAIL>>
Description:
		idle file is write-only and mark zram slot as idle.
		If system has mounted debugfs, user can see which slots
		are idle via /sys/kernel/debug/zram/zram<id>/block_state

What:		/sys/block/zram<id>/writeback
Date:		November 2018
Contact:	Minchan Kim <<EMAIL>>
Description:
		The writeback file is write-only and trigger idle and/or
		huge page writeback to backing device.

What:		/sys/block/zram<id>/bd_stat
Date:		November 2018
Contact:	Minchan Kim <<EMAIL>>
Description:
		The bd_stat file is read-only and represents backing device's
		statistics (bd_count, bd_reads, bd_writes) in a format
		similar to block layer statistics file format.

What:		/sys/block/zram<id>/writeback_limit_enable
Date:		November 2018
Contact:	Minchan Kim <<EMAIL>>
Description:
		The writeback_limit_enable file is read-write and specifies
		eanbe of writeback_limit feature. "1" means eable the feature.
		No limit "0" is the initial state.

What:		/sys/block/zram<id>/writeback_limit
Date:		November 2018
Contact:	Minchan Kim <<EMAIL>>
Description:
		The writeback_limit file is read-write and specifies the maximum
		amount of writeback ZRAM can do. The limit could be changed
		in run time.

What:		/sys/block/zram<id>/recomp_algorithm
Date:		November 2022
Contact:	Sergey Senozhatsky <<EMAIL>>
Description:
		The recomp_algorithm file is read-write and allows to set
		or show secondary compression algorithms.

What:		/sys/block/zram<id>/recompress
Date:		November 2022
Contact:	Sergey Senozhatsky <<EMAIL>>
Description:
		The recompress file is write-only and triggers re-compression
		with secondary compression algorithms.

What:		/sys/block/zram<id>/algorithm_params
Date:		August 2024
Contact:	Sergey Senozhatsky <<EMAIL>>
Description:
		The algorithm_params file is write-only and is used to setup
		compression algorithm parameters.
