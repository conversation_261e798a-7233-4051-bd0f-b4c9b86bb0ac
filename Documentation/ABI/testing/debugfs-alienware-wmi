What:		/sys/kernel/debug/alienware-wmi-<wmi_device_name>/system_description
Date:		March 2025
KernelVersion:	6.15
Contact:	<PERSON> <<EMAIL>>
Description:
		This file exposes the raw ``system_description`` number reported
		by the WMAX device.

		Only present on devices with the AWCC interface.

		See Documentation/admin-guide/laptops/alienware-wmi.rst for
		details.

		RO

What:		/sys/kernel/debug/alienware-wmi-<wmi_device_name>/hwmon_data
Date:		March 2025
KernelVersion:	6.15
Contact:	<PERSON> <<EMAIL>>
Description:
		This file exposes HWMON private data.

		Includes fan sensor count, temperature sensor count, internal
		fan IDs and internal temp IDs.

		See Documentation/admin-guide/laptops/alienware-wmi.rst for
		details.

		RO

What:		/sys/kernel/debug/alienware-wmi-<wmi_device_name>/pprof_data
Date:		March 2025
KernelVersion:	6.15
Contact:	<PERSON> <<EMAIL>>
Description:
		This file exposes Platform Profile private data.

		Includes internal mapping to platform profiles and thermal
		profile IDs.

		See Documentation/admin-guide/laptops/alienware-wmi.rst for
		details.

		RO

What:		/sys/kernel/debug/alienware-wmi-<wmi_device_name>/gpio_ctl/total_gpios
Date:		May 2025
KernelVersion:	6.16
Contact:	Kurt Borja <<EMAIL>>
Description:
		Total number of GPIO pins reported by the device.

		RO

What:		/sys/kernel/debug/alienware-wmi-<wmi_device_name>/gpio_ctl/pinX
Date:		May 2025
KernelVersion:	6.16
Contact:	Kurt Borja <<EMAIL>>
Description:
		This file controls GPIO pin X status.

		See Documentation/wmi/devices/alienware-wmi.rst for details.

		RW
