What:		/sys/kernel/debug/vfio/<device>/migration/hisi_acc/dev_data
Date:		Jan 2025
KernelVersion:  6.13
Contact:	<PERSON><PERSON><PERSON> <<EMAIL>>
Description:	Read the configuration data and some status data
		required for device live migration. These data include device
		status data, queue configuration data, some task configuration
		data and device attribute data. The output format of the data
		is defined by the live migration driver.

What:		/sys/kernel/debug/vfio/<device>/migration/hisi_acc/migf_data
Date:		Jan 2025
KernelVersion:  6.13
Contact:	<PERSON><PERSON><PERSON> <<EMAIL>>
Description:	Read the data from the last completed live migration.
		This data includes the same device status data as in "dev_data".
		The migf_data is the dev_data that is migrated.

What:		/sys/kernel/debug/vfio/<device>/migration/hisi_acc/cmd_state
Date:		Jan 2025
KernelVersion:  6.13
Contact:	<PERSON><PERSON><PERSON> <<EMAIL>>
Description:	Used to obtain the device command sending and receiving
		channel status. Returns failure or success logs based on the
		results.
