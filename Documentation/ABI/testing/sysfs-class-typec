USB Type-C port devices (eg. /sys/class/typec/port0/)

What:		/sys/class/typec/<port>/data_role
Date:		April 2017
Contact:	<PERSON><PERSON><PERSON> <<EMAIL>>
Description:
		The supported USB data roles. This attribute can be used for
		requesting data role swapping on the port. Swapping is supported
		as synchronous operation, so write(2) to the attribute will not
		return until the operation has finished. The attribute is
		notified about role changes so that poll(2) on the attribute
		wakes up. Change on the role will also generate uevent
		KOBJ_CHANGE on the port. The current role is show in brackets,
		for example "[host] device" when DRP port is in host mode.

		Valid values: host, device

What:		/sys/class/typec/<port>/power_role
Date:		April 2017
Contact:	<PERSON><PERSON><PERSON> <<EMAIL>>
Description:
		The supported power roles. This attribute can be used to request
		power role swap on the port. Swapping is supported as
		synchronous operation, so write(2) to the attribute will not
		return until the operation has finished. The attribute is
		notified about role changes so that poll(2) on the attribute
		wakes up. Change on the role will also generate uevent
		KOBJ_CHANGE. The current role is show in brackets, for example
		"[source] sink" when in source mode.

		Valid values: source, sink

What:           /sys/class/typec/<port>/port_type
Date:           May 2017
Contact:	Badhri Jagan Sridharan <<EMAIL>>
Description:
		Indicates the type of the port. This attribute can be used for
		requesting a change in the port type. Port type change is
		supported as a synchronous operation, so write(2) to the
		attribute will not return until the operation has finished.

		Valid values:

		======  ==============================================
		source  (The port will behave as source only DFP port)
		sink    (The port will behave as sink only UFP port)
		dual    (The port will behave as dual-role-data and
			dual-role-power port)
		======  ==============================================

What:		/sys/class/typec/<port>/vconn_source
Date:		April 2017
Contact:	Heikki Krogerus <<EMAIL>>
Description:
		Shows is the port VCONN Source. This attribute can be used to
		request VCONN swap to change the VCONN Source during connection
		when both the port and the partner support USB Power Delivery.
		Swapping is supported as synchronous operation, so write(2) to
		the attribute will not return until the operation has finished.
		The attribute is notified about VCONN source changes so that
		poll(2) on the attribute wakes up. Change on VCONN source also
		generates uevent KOBJ_CHANGE.

		Valid values:

		- "no" when the port is not the VCONN Source
		- "yes" when the port is the VCONN Source

What:		/sys/class/typec/<port>/power_operation_mode
Date:		April 2017
Contact:	Heikki Krogerus <<EMAIL>>
Description:
		Shows the current power operational mode the port is in. The
		power operation mode means current level for VBUS. In case USB
		Power Delivery communication is used for negotiating the levels,
		power operation mode should show "usb_power_delivery".

		Valid values:

		- default
		- 1.5A
		- 3.0A
		- usb_power_delivery

What:		/sys/class/typec/<port>/preferred_role
Date:		April 2017
Contact:	Heikki Krogerus <<EMAIL>>
Description:
		The user space can notify the driver about the preferred role.
		It should be handled as enabling of Try.SRC or Try.SNK, as
		defined in USB Type-C specification, in the port drivers. By
		default the preferred role should come from the platform.

		Valid values: source, sink, none (to remove preference)

What:		/sys/class/typec/<port>/supported_accessory_modes
Date:		April 2017
Contact:	Heikki Krogerus <<EMAIL>>
Description:
		Space separated list of accessory modes, defined in the USB
		Type-C specification, the port supports.

What:		/sys/class/typec/<port>/usb_power_delivery_revision
Date:		April 2017
Contact:	Heikki Krogerus <<EMAIL>>
Description:
		Revision number of the supported USB Power Delivery
		specification, or 0.0 when USB Power Delivery is not supported.

		Example values:
		- "2.0": USB Power Delivery Release 2.0
		- "3.0": USB Power Delivery Release 3.0
		- "3.1": USB Power Delivery Release 3.1

What:		/sys/class/typec/<port>-{partner|cable}/usb_power_delivery_revision
Date:		January 2021
Contact:	Benson Leung <<EMAIL>>
Description:
		Revision number of the supported USB Power Delivery
		specification of the port partner or cable, or 0.0 when USB
		Power Delivery is not supported.

		Example values:
		- "2.0": USB Power Delivery Release 2.0
		- "3.0": USB Power Delivery Release 3.0
		- "3.1": USB Power Delivery Release 3.1

What:		/sys/class/typec/<port>/usb_typec_revision
Date:		April 2017
Contact:	Heikki Krogerus <<EMAIL>>
Description:
		Revision number of the supported USB Type-C specification.

What:		/sys/class/typec/<port>/orientation
Date:		February 2020
Contact:	Badhri Jagan Sridharan <<EMAIL>>
Description:
		Indicates the active orientation of the Type-C connector.
		Valid values:
		- "normal": CC1 orientation
		- "reverse": CC2 orientation
		- "unknown": Orientation cannot be determined.

What:		/sys/class/typec/<port>/select_usb_power_delivery
Date:		May 2022
Contact:	Heikki Krogerus <<EMAIL>>
Description:
		Lists the USB Power Delivery Capabilities that the port can
		advertise to the partner. The currently used capabilities are in
		brackets. Selection happens by writing to the file.

What:		/sys/class/typec/<port>/usb_capability
Date:		November 2024
Contact:	Heikki Krogerus <<EMAIL>>
Description:	Lists the supported USB Modes. The default USB mode that is used
		next time with the Enter_USB Message is in brackets. The default
		mode can be changed by writing to the file when supported by the
		driver.

		Valid values:
		- usb2 (USB 2.0)
		- usb3 (USB 3.2)
		- usb4 (USB4)

USB Type-C partner devices (eg. /sys/class/typec/port0-partner/)

What:		/sys/class/typec/<port>-partner/accessory_mode
Date:		April 2017
Contact:	Heikki Krogerus <<EMAIL>>
Description:
		Shows the Accessory Mode name when the partner is an Accessory.
		The Accessory Modes are defined in USB Type-C Specification.

What:		/sys/class/typec/<port>-partner/supports_usb_power_delivery
Date:		April 2017
Contact:	Heikki Krogerus <<EMAIL>>
Description:
		Shows if the partner supports USB Power Delivery communication:
		Valid values: yes, no

What:		/sys/class/typec/<port>-partner/number_of_alternate_modes
Date:		November 2020
Contact:	Prashant Malani <<EMAIL>>
Description:
		Shows the number of alternate modes which are advertised by the partner
		during Power Delivery discovery. This file remains hidden until a value
		greater than or equal to 0 is set by Type C port driver.

What:		/sys/class/typec/<port>-partner/type
Date:		December 2020
Contact:	Heikki Krogerus <<EMAIL>>
Description:	USB Power Delivery Specification defines a set of product types
		for the partner devices. This file will show the product type of
		the partner if it is known. Dual-role capable partners will have
		both UFP and DFP product types defined, but only one that
		matches the current role will be active at the time. If the
		product type of the partner is not visible to the device driver,
		this file will not exist.

		When the partner product type is detected, or changed with role
		swap, uvevent is also raised that contains PRODUCT_TYPE=<product
		type> (for example PRODUCT_TYPE=hub).

		Valid values:

		UFP / device role
		======================  ==========================
		undefined		-
		hub			PDUSB Hub
		peripheral		PDUSB Peripheral
		psd			Power Bank
		ama			Alternate Mode Adapter
		======================  ==========================

		DFP / host role
		======================  ==========================
		undefined		-
		hub			PDUSB Hub
		host			PDUSB Host
		power_brick		Power Brick
		amc			Alternate Mode Controller
		======================  ==========================

What:		/sys/class/typec/<port>-partner/identity/
Date:		April 2017
Contact:	Heikki Krogerus <<EMAIL>>
Description:
		This directory appears only if the port device driver is capable
		of showing the result of Discover Identity USB power delivery
		command. That will not always be possible even when USB power
		delivery is supported, for example when USB power delivery
		communication for the port is mostly handled in firmware. If the
		directory exists, it will have an attribute file for every VDO
		in Discover Identity command result.

What:		/sys/class/typec/<port>-partner/usb_mode
Date:		November 2024
Contact:	Heikki Krogerus <<EMAIL>>
Description:	The USB Modes that the partner device supports. The active mode
		is displayed in brackets. The active USB mode can be changed by
		writing to this file when the port driver is able to send Data
		Reset Message to the partner. That requires USB Power Delivery
		contract between the partner and the port.

		Valid values:
		- usb2 (USB 2.0)
		- usb3 (USB 3.2)
		- usb4 (USB4)

USB Type-C cable devices (eg. /sys/class/typec/port0-cable/)

Note: Electronically Marked Cables will have a device also for one cable plug
(eg. /sys/class/typec/port0-plug0). If the cable is active and has also SOP
Double Prime controller (USB Power Deliver specification ch. 2.4) it will have
second device also for the other plug. Both plugs may have alternate modes as
described in USB Type-C and USB Power Delivery specifications.

What:		/sys/class/typec/<port>-cable/type
Date:		April 2017
Contact:	Heikki Krogerus <<EMAIL>>
Description:	USB Power Delivery Specification defines a set of product types
		for the cables. This file will show the product type of the
		cable if it is known. If the product type of the cable is not
		visible to the device driver, this file will not exist.

		When the cable product type is detected, uvevent is also raised
		with PRODUCT_TYPE showing the product type of the cable.

		Valid values:

		======================  ==========================
		undefined		-
		active			Active Cable
		passive			Passive Cable
		======================  ==========================

What:		/sys/class/typec/<port>-cable/plug_type
Date:		April 2017
Contact:	Heikki Krogerus <<EMAIL>>
Description:
		Shows type of the plug on the cable:

		- type-a - Standard A
		- type-b - Standard B
		- type-c
		- captive

What:		/sys/class/typec/<port>-<plug>/number_of_alternate_modes
Date:		November 2020
Contact:	Prashant Malani <<EMAIL>>
Description:
		Shows the number of alternate modes which are advertised by the plug
		associated with a particular cable during Power Delivery discovery.
		This file remains hidden until a value greater than or equal to 0
		is set by Type C port driver.


USB Type-C partner/cable Power Delivery Identity objects

NOTE: The following attributes will be applicable to both
partner (e.g /sys/class/typec/port0-partner/) and
cable (e.g /sys/class/typec/port0-cable/) devices. Consequently, the example file
paths below are prefixed with "/sys/class/typec/<port>-{partner|cable}/" to
reflect this.

What:		/sys/class/typec/<port>-{partner|cable}/identity/
Date:		April 2017
Contact:	Heikki Krogerus <<EMAIL>>
Description:
		This directory appears only if the port device driver is capable
		of showing the result of Discover Identity USB power delivery
		command. That will not always be possible even when USB power
		delivery is supported, for example when USB power delivery
		communication for the port is mostly handled in firmware. If the
		directory exists, it will have an attribute file for every VDO
		in Discover Identity command result.

What:		/sys/class/typec/<port>-{partner|cable}/identity/id_header
Date:		April 2017
Contact:	Heikki Krogerus <<EMAIL>>
Description:
		ID Header VDO part of Discover Identity command result. The
		value will show 0 until Discover Identity command result becomes
		available. The value can be polled.

What:		/sys/class/typec/<port>-{partner|cable}/identity/cert_stat
Date:		April 2017
Contact:	Heikki Krogerus <<EMAIL>>
Description:
		Cert Stat VDO part of Discover Identity command result. The
		value will show 0 until Discover Identity command result becomes
		available. The value can be polled.

What:		/sys/class/typec/<port>-{partner|cable}/identity/product
Date:		April 2017
Contact:	Heikki Krogerus <<EMAIL>>
Description:
		Product VDO part of Discover Identity command result. The value
		will show 0 until Discover Identity command result becomes
		available. The value can be polled.

What:		/sys/class/typec/<port>-{partner|cable}/identity/product_type_vdo1
Date:		October 2020
Contact:	Prashant Malani <<EMAIL>>
Description:
		1st Product Type VDO of Discover Identity command result.
		The value will show 0 until Discover Identity command result becomes
		available and a valid Product Type VDO is returned.

What:		/sys/class/typec/<port>-{partner|cable}/identity/product_type_vdo2
Date:		October 2020
Contact:	Prashant Malani <<EMAIL>>
Description:
		2nd Product Type VDO of Discover Identity command result.
		The value will show 0 until Discover Identity command result becomes
		available and a valid Product Type VDO is returned.

What:		/sys/class/typec/<port>-{partner|cable}/identity/product_type_vdo3
Date:		October 2020
Contact:	Prashant Malani <<EMAIL>>
Description:
		3rd Product Type VDO of Discover Identity command result.
		The value will show 0 until Discover Identity command result becomes
		available and a valid Product Type VDO is returned.


USB Type-C port alternate mode devices.

What:		/sys/class/typec/<port>/<alt mode>/supported_roles
Date:		April 2017
Contact:	Heikki Krogerus <<EMAIL>>
Description:
		Space separated list of the supported roles.

		Valid values: source, sink
