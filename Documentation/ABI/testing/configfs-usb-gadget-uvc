What:		/config/usb-gadget/gadget/functions/uvc.name
Date:		Dec 2014
KernelVersion:	4.0
Description:	UVC function directory

		===================	=============================
		streaming_maxburst	0..15 (ss only)
		streaming_maxpacket	1..1023 (fs), 1..3072 (hs/ss)
		streaming_interval	1..16
		function_name		string [32]
		===================	=============================

What:		/config/usb-gadget/gadget/functions/uvc.name/control
Date:		Dec 2014
KernelVersion:	4.0
Description:	Control descriptors

		All attributes read only except enable_interrupt_ep:

		===================	=============================
		bInterfaceNumber	USB interface number for this
					streaming interface
		enable_interrupt_ep	flag to enable the interrupt
					endpoint for the VC interface
		===================	=============================

What:		/config/usb-gadget/gadget/functions/uvc.name/control/class
Date:		Dec 2014
KernelVersion:	4.0
Description:	Class descriptors

What:		/config/usb-gadget/gadget/functions/uvc.name/control/class/ss
Date:		Dec 2014
KernelVersion:	4.0
Description:	Super speed control class descriptors

What:		/config/usb-gadget/gadget/functions/uvc.name/control/class/fs
Date:		Dec 2014
KernelVersion:	4.0
Description:	Full speed control class descriptors

What:		/config/usb-gadget/gadget/functions/uvc.name/control/terminal
Date:		Dec 2014
KernelVersion:	4.0
Description:	Terminal descriptors

What:		/config/usb-gadget/gadget/functions/uvc.name/control/terminal/output
Date:		Dec 2014
KernelVersion:	4.0
Description:	Output terminal descriptors

What:		/config/usb-gadget/gadget/functions/uvc.name/control/terminal/output/default
Date:		Dec 2014
KernelVersion:	4.0
Description:	Default output terminal descriptors

		All attributes read only except bSourceID:

		==============	=============================================
		iTerminal	index of string descriptor
		bSourceID	id of the terminal to which this terminal
				is connected
		bAssocTerminal	id of the input terminal to which this output
				terminal is associated
		wTerminalType	terminal type
		bTerminalID	a non-zero id of this terminal
		==============	=============================================

What:		/config/usb-gadget/gadget/functions/uvc.name/control/terminal/camera
Date:		Dec 2014
KernelVersion:	4.0
Description:	Camera terminal descriptors

What:		/config/usb-gadget/gadget/functions/uvc.name/control/terminal/camera/default
Date:		Dec 2014
KernelVersion:	4.0
Description:	Default camera terminal descriptors

		All attributes read only except bmControls, which is read/write:

		========================  ====================================
		bmControls		  bitmap specifying which controls are
					  supported for the video stream
		wOcularFocalLength	  the value of Locular
		wObjectiveFocalLengthMax  the value of Lmin
		wObjectiveFocalLengthMin  the value of Lmax
		iTerminal		  index of string descriptor
		bAssocTerminal		  id of the output terminal to which
					  this terminal is connected
		wTerminalType		  terminal type
		bTerminalID		  a non-zero id of this terminal
		========================  ====================================

What:		/config/usb-gadget/gadget/functions/uvc.name/control/processing
Date:		Dec 2014
KernelVersion:	4.0
Description:	Processing unit descriptors

What:		/config/usb-gadget/gadget/functions/uvc.name/control/processing/default
Date:		Dec 2014
KernelVersion:	4.0
Description:	Default processing unit descriptors

		All attributes read only except bmControls, which is read/write:

		===============	========================================
		iProcessing	index of string descriptor
		bmControls	bitmap specifying which controls are
				supported for the video stream
		wMaxMultiplier	maximum digital magnification x100
		bSourceID	id of the terminal to which this unit is
				connected
		bUnitID		a non-zero id of this unit
		===============	========================================

What:		/config/usb-gadget/gadget/functions/uvc.name/control/extensions
Date:		Nov 2022
KernelVersion:	6.1
Description:	Extension unit descriptors

What:		/config/usb-gadget/gadget/functions/uvc.name/control/extensions/name
Date:		Nov 2022
KernelVersion:	6.1
Description:	Extension Unit (XU) Descriptor

		bLength, bUnitID and iExtension are read-only. All others are
		read-write.

		=================	========================================
		bLength			size of the descriptor in bytes
		bUnitID			non-zero ID of this unit
		guidExtensionCode	Vendor-specific code identifying the XU
		bNumControls		number of controls in this XU
		bNrInPins		number of input pins for this unit
		baSourceID		list of the IDs of the units or terminals
					to which this XU is connected
		bControlSize		size of the bmControls field in bytes
		bmControls		list of bitmaps detailing which vendor
					specific controls are supported
		iExtension		index of a string descriptor that describes
					this extension unit
		=================	========================================

What:		/config/usb-gadget/gadget/functions/uvc.name/control/header
Date:		Dec 2014
KernelVersion:	4.0
Description:	Control header descriptors

What:		/config/usb-gadget/gadget/functions/uvc.name/control/header/name
Date:		Dec 2014
KernelVersion:	4.0
Description:	Specific control header descriptors

dwClockFrequency
bcdUVC
What:		/config/usb-gadget/gadget/functions/uvc.name/streaming
Date:		Dec 2014
KernelVersion:	4.0
Description:	Streaming descriptors

		All attributes read only:

		================	=============================
		bInterfaceNumber	USB interface number for this
					streaming interface
		================	=============================

What:		/config/usb-gadget/gadget/functions/uvc.name/streaming/class
Date:		Dec 2014
KernelVersion:	4.0
Description:	Streaming class descriptors

What:		/config/usb-gadget/gadget/functions/uvc.name/streaming/class/ss
Date:		Dec 2014
KernelVersion:	4.0
Description:	Super speed streaming class descriptors

What:		/config/usb-gadget/gadget/functions/uvc.name/streaming/class/hs
Date:		Dec 2014
KernelVersion:	4.0
Description:	High speed streaming class descriptors

What:		/config/usb-gadget/gadget/functions/uvc.name/streaming/class/fs
Date:		Dec 2014
KernelVersion:	4.0
Description:	Full speed streaming class descriptors

What:		/config/usb-gadget/gadget/functions/uvc.name/streaming/color_matching
Date:		Dec 2014
KernelVersion:	4.0
Description:	Color matching descriptors

What:		/config/usb-gadget/gadget/functions/uvc.name/streaming/color_matching/default
Date:		Dec 2014
KernelVersion:	4.0
Description:	Default color matching descriptors

		All attributes read/write:

		========================  ======================================
		bMatrixCoefficients	  matrix used to compute luma and
					  chroma values from the color primaries
		bTransferCharacteristics  optoelectronic transfer
					  characteristic of the source picture,
					  also called the gamma function
		bColorPrimaries		  color primaries and the reference
					  white
		========================  ======================================

What:		/config/usb-gadget/gadget/functions/uvc.name/streaming/color_matching/name
Date:		Dec 2022
KernelVersion:	6.3
Description:	Additional color matching descriptors

		All attributes read/write:

		========================  ======================================
		bMatrixCoefficients	  matrix used to compute luma and
					  chroma values from the color primaries
		bTransferCharacteristics  optoelectronic transfer
					  characteristic of the source picture,
					  also called the gamma function
		bColorPrimaries		  color primaries and the reference
					  white
		========================  ======================================

What:		/config/usb-gadget/gadget/functions/uvc.name/streaming/mjpeg
Date:		Dec 2014
KernelVersion:	4.0
Description:	MJPEG format descriptors

What:		/config/usb-gadget/gadget/functions/uvc.name/streaming/mjpeg/name
Date:		Dec 2014
KernelVersion:	4.0
Description:	Specific MJPEG format descriptors

		All attributes read only,
		except bmaControls and bDefaultFrameIndex:

		===================	=====================================
		bFormatIndex		unique id for this format descriptor;
					only defined after parent header is
					linked into the streaming class;
					read-only
		bmaControls		this format's data for bmaControls in
					the streaming header
		bmInterlaceFlags	specifies interlace information,
					read-only
		bAspectRatioY		the X dimension of the picture aspect
					ratio, read-only
		bAspectRatioX		the Y dimension of the picture aspect
					ratio, read-only
		bmFlags			characteristics of this format,
					read-only
		bDefaultFrameIndex	optimum frame index for this stream
		===================	=====================================

What:		/config/usb-gadget/gadget/functions/uvc.name/streaming/mjpeg/name/name
Date:		Dec 2014
KernelVersion:	4.0
Description:	Specific MJPEG frame descriptors

		=========================  =====================================
		bFrameIndex		   unique id for this framedescriptor;
					   only defined after parent format is
					   linked into the streaming header;
					   read-only
		dwFrameInterval		   indicates how frame interval can be
					   programmed; a number of values
					   separated by newline can be specified
		dwDefaultFrameInterval	   the frame interval the device would
					   like to use as default
		dwMaxVideoFrameBufferSize  the maximum number of bytes the
					   compressor will produce for a video
					   frame or still image
		dwMaxBitRate		   the maximum bit rate at the shortest
					   frame interval in bps
		dwMinBitRate		   the minimum bit rate at the longest
					   frame interval in bps
		wHeight			   height of decoded bitmap frame in px
		wWidth			   width of decoded bitmam frame in px
		bmCapabilities		   still image support, fixed frame-rate
					   support
		=========================  =====================================

What:		/config/usb-gadget/gadget/functions/uvc.name/streaming/uncompressed
Date:		Dec 2014
KernelVersion:	4.0
Description:	Uncompressed format descriptors

What:		/config/usb-gadget/gadget/functions/uvc.name/streaming/uncompressed/name
Date:		Dec 2014
KernelVersion:	4.0
Description:	Specific uncompressed format descriptors

		==================	=======================================
		bFormatIndex		unique id for this format descriptor;
					only defined after parent header is
					linked into the streaming class;
					read-only
		bmaControls		this format's data for bmaControls in
					the streaming header
		bmInterlaceFlags	specifies interlace information,
					read-only
		bAspectRatioY		the X dimension of the picture aspect
					ratio, read-only
		bAspectRatioX		the Y dimension of the picture aspect
					ratio, read-only
		bDefaultFrameIndex	optimum frame index for this stream
		bBitsPerPixel		number of bits per pixel used to
					specify color in the decoded video
					frame
		guidFormat		globally unique id used to identify
					stream-encoding format
		==================	=======================================

What:		/config/usb-gadget/gadget/functions/uvc.name/streaming/uncompressed/name/name
Date:		Dec 2014
KernelVersion:	4.0
Description:	Specific uncompressed frame descriptors

		=========================  =====================================
		bFrameIndex		   unique id for this framedescriptor;
					   only defined after parent format is
					   linked into the streaming header;
					   read-only
		dwFrameInterval		   indicates how frame interval can be
					   programmed; a number of values
					   separated by newline can be specified
		dwDefaultFrameInterval	   the frame interval the device would
					   like to use as default
		dwMaxVideoFrameBufferSize  the maximum number of bytes the
					   compressor will produce for a video
					   frame or still image
		dwMaxBitRate		   the maximum bit rate at the shortest
					   frame interval in bps
		dwMinBitRate		   the minimum bit rate at the longest
					   frame interval in bps
		wHeight			   height of decoded bitmap frame in px
		wWidth			   width of decoded bitmam frame in px
		bmCapabilities		   still image support, fixed frame-rate
					   support
		=========================  =====================================

What:           /config/usb-gadget/gadget/functions/uvc.name/streaming/framebased
Date:           Sept 2024
KernelVersion:  5.15
Description:    Framebased format descriptors

What:           /config/usb-gadget/gadget/functions/uvc.name/streaming/framebased/name
Date:           Sept 2024
KernelVersion:  5.15
Description:    Specific framebased format descriptors

                ==================      =======================================
                bFormatIndex            unique id for this format descriptor;
                                        only defined after parent header is
                                        linked into the streaming class;
                                        read-only
                bmaControls             this format's data for bmaControls in
                                        the streaming header
                bmInterlaceFlags        specifies interlace information,
                                        read-only
                bAspectRatioY           the X dimension of the picture aspect
                                        ratio, read-only
                bAspectRatioX           the Y dimension of the picture aspect
                                        ratio, read-only
                bDefaultFrameIndex      optimum frame index for this stream
                bBitsPerPixel           number of bits per pixel used to
                                        specify color in the decoded video
                                        frame
                guidFormat              globally unique id used to identify
                                        stream-encoding format
                ==================      =======================================

What:           /config/usb-gadget/gadget/functions/uvc.name/streaming/framebased/name/name
Date:           Sept 2024
KernelVersion:  5.15
Description:    Specific framebased frame descriptors

                =========================  =====================================
                bFrameIndex                unique id for this framedescriptor;
                                           only defined after parent format is
                                           linked into the streaming header;
                                           read-only
                dwFrameInterval            indicates how frame interval can be
                                           programmed; a number of values
                                           separated by newline can be specified
                dwDefaultFrameInterval     the frame interval the device would
                                           like to use as default
                dwBytesPerLine             Specifies the number of bytes per line
                                           of video for packed fixed frame size
                                           formats, allowing the receiver to
                                           perform stride alignment of the video.
                                           If the bVariableSize value (above) is
                                           TRUE (1), or if the format does not
                                           permit such alignment, this value shall
                                           be set to zero (0).
                dwMaxBitRate               the maximum bit rate at the shortest
                                           frame interval in bps
                dwMinBitRate               the minimum bit rate at the longest
                                           frame interval in bps
                wHeight                    height of decoded bitmap frame in px
                wWidth                     width of decoded bitmam frame in px
                bmCapabilities             still image support, fixed frame-rate
                                           support
                =========================  =====================================

What:		/config/usb-gadget/gadget/functions/uvc.name/streaming/header
Date:		Dec 2014
KernelVersion:	4.0
Description:	Streaming header descriptors

What:		/config/usb-gadget/gadget/functions/uvc.name/streaming/header/name
Date:		Dec 2014
KernelVersion:	4.0
Description:	Specific streaming header descriptors

		All attributes read only:

		====================	=====================================
		bTriggerUsage		how the host software will respond to
					a hardware trigger interrupt event
		bTriggerSupport		flag specifying if hardware
					triggering is supported
		bStillCaptureMethod	method of still image capture
					supported
		bTerminalLink		id of the output terminal to which
					the video endpoint of this interface
					is connected
		bmInfo			capabilities of this video streaming
					interface
		====================	=====================================

What:		/sys/class/udc/udc.name/device/gadget/video4linux/video.name/function_name
Date:		May 2018
KernelVersion:	4.19
Description:	UVC configfs function instance name
