What:		/sys/devices/platform/mlxplat/mlxreg-io/hwmon/hwmon*/asic_health
Date:		June 2018
KernelVersion:	4.19
Contact:	Vadi<PERSON> <<EMAIL>>
Description:	This file shows ASIC health status. The possible values are:
		0 - health failed, 2 - health OK, 3 - ASIC in booting state.

		The files are read only.

What:		/sys/devices/platform/mlxplat/mlxreg-io/hwmon/hwmon*/cpld1_version
What:		/sys/devices/platform/mlxplat/mlxreg-io/hwmon/hwmon*/cpld2_version
Date:		June 2018
KernelVersion:	4.19
Contact:	Vadi<PERSON> <<EMAIL>>
Description:	These files show with which CPLD versions have been burned
		on carrier and switch boards.

		The files are read only.

What:		/sys/devices/platform/mlxplat/mlxreg-io/hwmon/hwmon*/fan_dir
Date:		December 2018
KernelVersion:	5.0
Contact:	<PERSON>adi<PERSON> <<EMAIL>>
Description:	This file shows the system fans direction:
		forward direction - relevant bit is set 0;
		reversed direction - relevant bit is set 1.

		The files are read only.

What:		/sys/devices/platform/mlxplat/mlxreg-io/hwmon/hwmon*/cpld3_version
Date:		November 2018
KernelVersion:	5.0
Contact:	Vadim Pasternak <<EMAIL>>
Description:	These files show with which CPLD versions have been burned
		on LED or Gearbox board.

		The files are read only.

What:		/sys/devices/platform/mlxplat/mlxreg-io/hwmon/hwmon*/jtag_enable
Date:		November 2018
KernelVersion:	5.0
Contact:	Vadim Pasternak <<EMAIL>>
Description:	These files enable and disable the access to the JTAG domain.
		By default access to the JTAG domain is disabled.

		The file is read/write.

What:		/sys/devices/platform/mlxplat/mlxreg-io/hwmon/hwmon*/select_iio
Date:		June 2018
KernelVersion:	4.19
Contact:	Vadim Pasternak <<EMAIL>>
Description:	This file allows iio devices selection.

		Attribute select_iio can be written with 0 or with 1. It
		selects which one of iio devices can be accessed.

		The file is read/write.

What:		/sys/devices/platform/mlxplat/mlxreg-io/hwmon/hwmon*/psu1_on
		/sys/devices/platform/mlxplat/mlxreg-io/hwmon/hwmon*/psu2_on
		/sys/devices/platform/mlxplat/mlxreg-io/hwmon/hwmon*/pwr_cycle
		/sys/devices/platform/mlxplat/mlxreg-io/hwmon/hwmon*/pwr_down
Date:		June 2018
KernelVersion:	4.19
Contact:	Vadim Pasternak <<EMAIL>>
Description:	These files allow asserting system power cycling, switching
		power supply units on and off and system's main power domain
		shutdown.
		Expected behavior:
		When pwr_cycle is written 1: auxiliary power domain will go
		down and after short period (about 1 second) up.
		When  psu1_on or psu2_on is written 1, related unit will be
		disconnected from the power source, when written 0 - connected.
		If both are written 1 - power supplies main power domain will
		go down.
		When pwr_down is written 1, system's main power domain will go
		down.

		The files are write only.

What:		/sys/devices/platform/mlxplat/mlxreg-io/hwmon/hwmon*/reset_aux_pwr_or_ref
What:		/sys/devices/platform/mlxplat/mlxreg-io/hwmon/hwmon*/reset_asic_thermal
What:		/sys/devices/platform/mlxplat/mlxreg-io/hwmon/hwmon*/reset_hotswap_or_halt
What:		/sys/devices/platform/mlxplat/mlxreg-io/hwmon/hwmon*/reset_hotswap_or_wd
What:		/sys/devices/platform/mlxplat/mlxreg-io/hwmon/hwmon*/reset_fw_reset
What:		/sys/devices/platform/mlxplat/mlxreg-io/hwmon/hwmon*/reset_long_pb
What:		/sys/devices/platform/mlxplat/mlxreg-io/hwmon/hwmon*/reset_main_pwr_fail
What:		/sys/devices/platform/mlxplat/mlxreg-io/hwmon/hwmon*/reset_short_pb
What:		/sys/devices/platform/mlxplat/mlxreg-io/hwmon/hwmon*/reset_sw_reset
Date:		June 2018
KernelVersion:	4.19
Contact:	Vadim Pasternak <<EMAIL>>
Description:	These files show the system reset cause, as following: power
		auxiliary outage or power refresh, ASIC thermal shutdown, halt,
		hotswap, watchdog, firmware reset, long press power button,
		short press power button, software reset. Value 1 in file means
		this is reset cause, 0 - otherwise. Only one of the above
		causes could be 1 at the same time, representing only last
		reset cause.

		The files are read only.

What:		/sys/devices/platform/mlxplat/mlxreg-io/hwmon/hwmon*/reset_comex_pwr_fail
What:		/sys/devices/platform/mlxplat/mlxreg-io/hwmon/hwmon*/reset_from_comex
What:		/sys/devices/platform/mlxplat/mlxreg-io/hwmon/hwmon*/reset_system
What:		/sys/devices/platform/mlxplat/mlxreg-io/hwmon/hwmon*/reset_voltmon_upgrade_fail
Date:		November 2018
KernelVersion:	5.0
Contact:	Vadim Pasternak <<EMAIL>>
Description:	These files show the system reset cause, as following: ComEx
		power fail, reset from ComEx, system platform reset, reset
		due to voltage monitor devices upgrade failure,
		Value 1 in file means this is reset cause, 0 - otherwise.
		Only one bit could be 1 at the same time, representing only
		the last reset cause.

		The files are read only.

What:		/sys/devices/platform/mlxplat/mlxreg-io/hwmon/hwmon*/cpld4_version
Date:		November 2018
KernelVersion:	5.0
Contact:	Vadim Pasternak <<EMAIL>>
Description:	These files show with which CPLD versions have been burned
		on LED board.

		The files are read only.

What:		/sys/devices/platform/mlxplat/mlxreg-io/hwmon/hwmon*/reset_comex_thermal
What:		/sys/devices/platform/mlxplat/mlxreg-io/hwmon/hwmon*/reset_comex_wd
What:		/sys/devices/platform/mlxplat/mlxreg-io/hwmon/hwmon*/reset_from_asic
What:		/sys/devices/platform/mlxplat/mlxreg-io/hwmon/hwmon*/reset_reload_bios
What:		/sys/devices/platform/mlxplat/mlxreg-io/hwmon/hwmon*/reset_sff_wd
What:		/sys/devices/platform/mlxplat/mlxreg-io/hwmon/hwmon*/reset_swb_wd
Date:		June 2019
KernelVersion:	5.3
Contact:	Vadim Pasternak <<EMAIL>>
Description:	These files show the system reset cause, as following:
		COMEX thermal shutdown; wathchdog power off or reset was derived
		by one of the next components: COMEX, switch board or by Small Form
		Factor mezzanine, reset requested from ASIC, reset caused by BIOS
		reload. Value 1 in file means this is reset cause, 0 - otherwise.
		Only one of the above causes could be 1 at the same time, representing
		only last reset cause.

		The files are read only.

What:		/sys/devices/platform/mlxplat/mlxreg-io/hwmon/hwmon*/config1
What:		/sys/devices/platform/mlxplat/mlxreg-io/hwmon/hwmon*/config2
Date:		January 2020
KernelVersion:	5.6
Contact:	Vadim Pasternak <<EMAIL>>
Description:	These files show system static topology identification
		like system's static I2C topology, number and type of FPGA
		devices within the system and so on.

		The files are read only.

What:		/sys/devices/platform/mlxplat/mlxreg-io/hwmon/hwmon*/reset_ac_pwr_fail
What:		/sys/devices/platform/mlxplat/mlxreg-io/hwmon/hwmon*/reset_platform
What:		/sys/devices/platform/mlxplat/mlxreg-io/hwmon/hwmon*/reset_soc
What:		/sys/devices/platform/mlxplat/mlxreg-io/hwmon/hwmon*/reset_sw_pwr_off
Date:		January 2020
KernelVersion:	5.6
Contact:	Vadim Pasternak <<EMAIL>>
Description:	These files show the system reset causes, as following: reset
		due to AC power failure, reset invoked from software by
		assertion reset signal through CPLD. reset caused by signal
		asserted by SOC through ACPI register, reset invoked from
		software by assertion power off signal through CPLD.

		The files are read only.

What:		/sys/devices/platform/mlxplat/mlxreg-io/hwmon/hwmon*/pcie_asic_reset_dis
Date:		January 2020
KernelVersion:	5.6
Contact:	Vadim Pasternak <<EMAIL>>
Description:	This file allows to retain ASIC up during PCIe root complex
		reset, when attribute is set 1.

		The file is read/write.

What:		/sys/devices/platform/mlxplat/mlxreg-io/hwmon/hwmon*/vpd_wp
Date:		January 2020
KernelVersion:	5.6
Contact:	Vadim Pasternak <<EMAIL>>
Description:	This file allows to overwrite system VPD hardware write
		protection when attribute is set 1.

		The file is read/write.

What:		/sys/devices/platform/mlxplat/mlxreg-io/hwmon/hwmon*/voltreg_update_status
Date:		January 2020
KernelVersion:	5.6
Contact:	Vadim Pasternak <<EMAIL>>
Description:	This file exposes the configuration update status of burnable
		voltage regulator devices. The status values are as following:
		0 - OK; 1 - CRC failure; 2 = I2C failure; 3 - in progress.

		The file is read only.

What:		/sys/devices/platform/mlxplat/mlxreg-io/hwmon/hwmon*/ufm_version
Date:		January 2020
KernelVersion:	5.6
Contact:	Vadim Pasternak <<EMAIL>>
Description:	This file exposes the firmware version of burnable voltage
		regulator devices.

		The file is read only.

What:		/sys/devices/platform/mlxplat/mlxreg-io/hwmon/hwmon*/cpld1_pn
What:		/sys/devices/platform/mlxplat/mlxreg-io/hwmon/hwmon*/cpld2_pn
What:		/sys/devices/platform/mlxplat/mlxreg-io/hwmon/hwmon*/cpld3_pn
What:		/sys/devices/platform/mlxplat/mlxreg-io/hwmon/hwmon*/cpld4_pn
What:		/sys/devices/platform/mlxplat/mlxreg-io/hwmon/hwmon*/cpld1_version_min
What:		/sys/devices/platform/mlxplat/mlxreg-io/hwmon/hwmon*/cpld2_version_min
What:		/sys/devices/platform/mlxplat/mlxreg-io/hwmon/hwmon*/cpld3_version_min
What:		/sys/devices/platform/mlxplat/mlxreg-io/hwmon/hwmon*/cpld4_version_min
Date:		July 2020
KernelVersion:	5.9
Contact:	Vadim Pasternak <<EMAIL>>
Description:	These files show with which CPLD part numbers and minor
		versions have been burned CPLD devices equipped on a
		system.

		The files are read only.

What:		/sys/devices/platform/mlxplat/mlxreg-io/hwmon/hwmon*/bios_active_image
What:		/sys/devices/platform/mlxplat/mlxreg-io/hwmon/hwmon*/bios_auth_fail
What:		/sys/devices/platform/mlxplat/mlxreg-io/hwmon/hwmon*/bios_upgrade_fail
Date:		October 2021
KernelVersion:	5.16
Contact:	Vadim Pasternak <<EMAIL>>
Description:	The files represent BIOS statuses:

		bios_active_image: location of current active BIOS image:
		0: Top, 1: Bottom.
		The reported value should correspond to value expected by OS
		in case of BIOS safe mode is 0. This bit is related to Intel
		top-swap feature of DualBios on the same flash.

		bios_auth_fail: BIOS upgrade is failed because provided BIOS
		image is not signed correctly.

		bios_upgrade_fail: BIOS upgrade is failed by some other
		reason not because authentication. For example due to
		physical SPI flash problem.

		The files are read only.

What:		/sys/devices/platform/mlxplat/mlxreg-io/hwmon/hwmon*/lc1_enable
What:		/sys/devices/platform/mlxplat/mlxreg-io/hwmon/hwmon*/lc2_enable
What:		/sys/devices/platform/mlxplat/mlxreg-io/hwmon/hwmon*/lc3_enable
What:		/sys/devices/platform/mlxplat/mlxreg-io/hwmon/hwmon*/lc4_enable
What:		/sys/devices/platform/mlxplat/mlxreg-io/hwmon/hwmon*/lc5_enable
What:		/sys/devices/platform/mlxplat/mlxreg-io/hwmon/hwmon*/lc6_enable
What:		/sys/devices/platform/mlxplat/mlxreg-io/hwmon/hwmon*/lc7_enable
What:		/sys/devices/platform/mlxplat/mlxreg-io/hwmon/hwmon*/lc8_enable
Date:		October 2021
KernelVersion:	5.16
Contact:	Vadim Pasternak <<EMAIL>>
Description:	These files allow line cards enable state control.
		Expected behavior:
		When  lc{n}_enable is written 1, related line card is released
		from the reset state, when 0 - is hold in reset state.

		The files are read/write.

What:		/sys/devices/platform/mlxplat/mlxreg-io/hwmon/hwmon*/lc1_pwr
What:		/sys/devices/platform/mlxplat/mlxreg-io/hwmon/hwmon*/lc2_pwr
What:		/sys/devices/platform/mlxplat/mlxreg-io/hwmon/hwmon*/lc3_pwr
What:		/sys/devices/platform/mlxplat/mlxreg-io/hwmon/hwmon*/lc4_pwr
What:		/sys/devices/platform/mlxplat/mlxreg-io/hwmon/hwmon*/lc5_pwr
What:		/sys/devices/platform/mlxplat/mlxreg-io/hwmon/hwmon*/lc6_pwr
What:		/sys/devices/platform/mlxplat/mlxreg-io/hwmon/hwmon*/lc7_pwr
What:		/sys/devices/platform/mlxplat/mlxreg-io/hwmon/hwmon*/lc8_pwr
Date:		October 2021
KernelVersion:	5.16
Contact:	Vadim Pasternak <<EMAIL>>
Description:	These files switching line cards power on and off.
		Expected behavior:
		When  lc{n}_pwr is written 1, related line card is powered
		on, when written 0 - powered off.

		The files are read/write.

What:		/sys/devices/platform/mlxplat/mlxreg-io/hwmon/hwmon*/lc1_rst_mask
What:		/sys/devices/platform/mlxplat/mlxreg-io/hwmon/hwmon*/lc2_rst_mask
What:		/sys/devices/platform/mlxplat/mlxreg-io/hwmon/hwmon*/lc3_rst_mask
What:		/sys/devices/platform/mlxplat/mlxreg-io/hwmon/hwmon*/lc4_rst_mask
What:		/sys/devices/platform/mlxplat/mlxreg-io/hwmon/hwmon*/lc5_rst_mask
What:		/sys/devices/platform/mlxplat/mlxreg-io/hwmon/hwmon*/lc6_rst_mask
What:		/sys/devices/platform/mlxplat/mlxreg-io/hwmon/hwmon*/lc7_rst_mask
What:		/sys/devices/platform/mlxplat/mlxreg-io/hwmon/hwmon*/lc8_rst_mask
Date:		October 2021
KernelVersion:	5.16
Contact:	Vadim Pasternak <<EMAIL>>
Description:	These files clear line card reset bit enforced by ASIC, when it
		sets it due to some abnormal ASIC behavior.
		Expected behavior:
		When lc{n}_rst_mask is written 1, related line card reset bit
		is cleared, when written 0 - no effect.

		The files are write only.

What:		/sys/devices/platform/mlxplat/mlxreg-io/hwmon/hwmon*/os_started
Date:		October 2021
KernelVersion:	5.16
Contact:	Vadim Pasternak <<EMAIL>>
Description:	This file, when written 1, indicates to programmable devices
		that OS is taking control over it.

		The file is read/write.

What:		/sys/devices/platform/mlxplat/mlxreg-io/hwmon/hwmon*/pm_mgmt_en
Date:		October 2021
KernelVersion:	5.16
Contact:	Vadim Pasternak <<EMAIL>>
Description:	This file assigns power management control ownership.
		When power management control is provided by hardware, hardware
		will automatically power off one or more line previously
		powered line cards in case system power budget is getting
		insufficient. It could be in case when some of power units lost
		power good state.
		When pm_mgmt_en is written 1, power management control by
		software is enabled, 0 - power management control by hardware.
		Note that for any setting of pm_mgmt_en attribute hardware will
		not allow to power on any new line card in case system power
		budget is insufficient.
		Same in case software will try to power on several line cards
		at once - hardware will power line cards while system has
		enough power budget.
		Default is 0.

		The file is read/write.

What:		/sys/devices/platform/mlxplat/mlxreg-io/hwmon/hwmon*/psu3_on
What:		/sys/devices/platform/mlxplat/mlxreg-io/hwmon/hwmon*/psu4_on
Date:		October 2021
KernelVersion:	5.16
Contact:	Vadim Pasternak <<EMAIL>>
Description:	These files switching power supply units on and off.
		Expected behavior:
		When  psu3_on or psu4_on is written 1, related unit will be
		disconnected from the power source, when written 0 - connected.

		The files are write only.

What:		/sys/devices/platform/mlxplat/mlxreg-io/hwmon/hwmon*/shutdown_unlock
Date:		October 2021
KernelVersion:	5.16
Contact:	Vadim Pasternak <<EMAIL>>
Description:	This file allows to unlock ASIC after thermal shutdown event.
		When system thermal shutdown is enforced by ASIC, ASIC is
		getting locked and after system boot it will not be available.
		Software can decide to unlock it by setting this attribute to
		1 and then perform system power cycle by setting pwr_cycle
		attribute to 1 (power cycle of main power domain).
		Before setting shutdown_unlock to 1 it is recommended to
		validate that system reboot cause is reset_asic_thermal or
		reset_thermal_spc_or_pciesw.
		In case shutdown_unlock is not set 1, the only way to release
		ASIC from locking - is full system power cycle through the
		external power distribution unit.
		Default is 1.

		The file is read/write.

What:		/sys/devices/platform/mlxplat/i2c_mlxcpld.*/i2c-*/i2c-*/i2c-*/*-0032/mlxreg-io.*/hwmon/hwmon*/cpld1_pn
What:		/sys/devices/platform/mlxplat/i2c_mlxcpld.*/i2c-*/i2c-*/i2c-*/*-0032/mlxreg-io.*/hwmon/hwmon*/cpld1_version
What:		/sys/devices/platform/mlxplat/i2c_mlxcpld.*/i2c-*/i2c-*/i2c-*/*-0032/mlxreg-io.*/hwmon/hwmon*/cpld1_version_min
Date:		October 2021
KernelVersion:	5.16
Contact:	Vadim Pasternak <<EMAIL>>
Description:	These files show with which CPLD major and minor versions
		and part number has been burned CPLD device on line card.

		The files are read only.

What:		/sys/devices/platform/mlxplat/i2c_mlxcpld.*/i2c-*/i2c-*/i2c-*/*-0032/mlxreg-io.*/hwmon/hwmon*/fpga1_pn
What:		/sys/devices/platform/mlxplat/i2c_mlxcpld.*/i2c-*/i2c-*/i2c-*/*-0032/mlxreg-io.*/hwmon/hwmon*/fpga1_version
What:		/sys/devices/platform/mlxplat/i2c_mlxcpld.*/i2c-*/i2c-*/i2c-*/*-0032/mlxreg-io.*/hwmon/hwmon*/fpga1_version_min
Date:		October 2021
KernelVersion:	5.16
Contact:	Vadim Pasternak <<EMAIL>>
Description:	These files show with which FPGA major and minor versions
		and part number has been burned FPGA device on line card.

		The files are read only.

What:		/sys/devices/platform/mlxplat/i2c_mlxcpld.*/i2c-*/i2c-*/i2c-*/*-0032/mlxreg-io.*/hwmon/hwmon*/vpd_wp
Date:		October 2021
KernelVersion:	5.16
Contact:	Vadim Pasternak <<EMAIL>>
Description:	This file allow to overwrite line card VPD hardware write
		protection mode. When attribute is set 1 - write protection is
		disabled, when 0 - enabled.
		Default is 0.
		If the system is in locked-down mode writing this file will not
		be allowed.
		The purpose if this file is to allow line card VPD burning
		during production flow.

		The file is read/write.

What:		/sys/devices/platform/mlxplat/i2c_mlxcpld.*/i2c-*/i2c-*/i2c-*/*-0032/mlxreg-io.*/hwmon/hwmon*/reset_aux_pwr_or_ref
What:		/sys/devices/platform/mlxplat/i2c_mlxcpld.*/i2c-*/i2c-*/i2c-*/*-0032/mlxreg-io.*/hwmon/hwmon*/reset_dc_dc_pwr_fail
What:		/sys/devices/platform/mlxplat/i2c_mlxcpld.*/i2c-*/i2c-*/i2c-*/*-0032/mlxreg-io.*/hwmon/hwmon*/reset_fpga_not_done
What:		/sys/devices/platform/mlxplat/i2c_mlxcpld.*/i2c-*/i2c-*/i2c-*/*-0032/mlxreg-io.*/hwmon/hwmon*/reset_from_chassis
What:		/sys/devices/platform/mlxplat/i2c_mlxcpld.*/i2c-*/i2c-*/i2c-*/*-0032/mlxreg-io.*/hwmon/hwmon*/reset_line_card
What:		/sys/devices/platform/mlxplat/i2c_mlxcpld.*/i2c-*/i2c-*/i2c-*/*-0032/mlxreg-io.*/hwmon/hwmon*/reset_pwr_off_from_chassis
Date:		October 2021
KernelVersion:	5.16
Contact:	Vadim Pasternak <<EMAIL>>
Description:	These files show the line reset cause, as following: power
		auxiliary outage or power refresh, DC-to-DC power failure, FPGA reset
		failed, line card reset failed, power off from chassis.
		Value 1 in file means this is reset cause, 0 - otherwise. Only one of
		the above causes could be 1 at the same time, representing only last
		reset cause.

		The files are read only.

What:		/sys/devices/platform/mlxplat/i2c_mlxcpld.*/i2c-*/i2c-*/i2c-*/*-0032/mlxreg-io.*/hwmon/hwmon*/cpld_upgrade_en
What:		/sys/devices/platform/mlxplat/i2c_mlxcpld.*/i2c-*/i2c-*/i2c-*/*-0032/mlxreg-io.*/hwmon/hwmon*/fpga_upgrade_en
Date:		October 2021
KernelVersion:	5.16
Contact:	Vadim Pasternak <<EMAIL>>
Description:	These files allow CPLD and FPGA burning. Value 1 in file means burning
		is enabled, 0 - otherwise.
		If the system is in locked-down mode writing these files will
		not be allowed.
		The purpose of these files to allow line card CPLD and FPGA
		upgrade through the JTAG daisy-chain.

		The files are read/write.

What:		/sys/devices/platform/mlxplat/i2c_mlxcpld.*/i2c-*/i2c-*/i2c-*/*-0032/mlxreg-io.*/hwmon/hwmon*/qsfp_pwr_en
What:		/sys/devices/platform/mlxplat/i2c_mlxcpld.*/i2c-*/i2c-*/i2c-*/*-0032/mlxreg-io.*/hwmon/hwmon*/pwr_en
Date:		October 2021
KernelVersion:	5.16
Contact:	Vadim Pasternak <<EMAIL>>
Description:	These files allow to power on/off all QSFP ports and whole line card.
		The attributes are set 1 for power on, 0 - for power off.

		The files are read/write.

What:		/sys/devices/platform/mlxplat/i2c_mlxcpld.*/i2c-*/i2c-*/i2c-*/*-0032/mlxreg-io.*/hwmon/hwmon*/agb_spi_burn_en
What:		/sys/devices/platform/mlxplat/i2c_mlxcpld.*/i2c-*/i2c-*/i2c-*/*-0032/mlxreg-io.*/hwmon/hwmon*/fpga_spi_burn_en
Date:		October 2021
KernelVersion:	5.16
Contact:	Vadim Pasternak <<EMAIL>>
Description:	These files allow gearboxes and FPGA SPI flash burning.
		The attributes are set 1 to enable burning, 0 - to disable.
		If the system is in locked-down mode writing these files will
		not be allowed.
		The purpose of these files to allow line card Gearboxes and FPGA
		burning during production flow.

		The file is read/write.

What:		/sys/devices/platform/mlxplat/i2c_mlxcpld.*/i2c-*/i2c-*/i2c-*/*-0032/mlxreg-io.*/hwmon/hwmon*/max_power
What:		/sys/devices/platform/mlxplat/i2c_mlxcpld.*/i2c-*/i2c-*/i2c-*/*-0032/mlxreg-io.*/hwmon/hwmon*/config
Date:		October 2021
KernelVersion:	5.16
Contact:	Vadim Pasternak <<EMAIL>>
Description:	These files provide the maximum powered required for line card
		feeding and line card configuration Id.

		The files are read only.

What:		/sys/devices/platform/mlxplat/mlxreg-io/hwmon/hwmon*/phy_reset
Date:		May 2022
KernelVersion:	5.19
Contact:	Vadim Pasternak <<EMAIL>>
Description:	This file allows to reset PHY 88E1548 when attribute is set 0
		due to some abnormal PHY behavior.
		Expected behavior:
		When phy_reset is written 1, all PHY 88E1548 are released
		from the reset state, when 0 - are hold in reset state.

		The files are read/write.

What:		/sys/devices/platform/mlxplat/mlxreg-io/hwmon/hwmon*/mac_reset
Date:		May 2022
KernelVersion:	5.19
Contact:	Vadim Pasternak <<EMAIL>>
Description:	This file allows to reset ASIC MT52132 when attribute is set 0
		due to some abnormal ASIC behavior.
		Expected behavior:
		When mac_reset is written 1, the ASIC MT52132 is released
		from the reset state, when 0 - is hold in reset state.

		The files are read/write.

What:		/sys/devices/platform/mlxplat/mlxreg-io/hwmon/hwmon*/qsfp_pwr_good
Date:		May 2022
KernelVersion:	5.19
Contact:	Vadim Pasternak <<EMAIL>>
Description:	This file shows QSFP ports power status. The value is set to 0
		when one of any QSFP ports is plugged. The value is set to 1 when
		there are no any QSFP ports are plugged.
		The possible values are:
		0 - Power good, 1 - Not power good.

		The files are read only.

What:		/sys/devices/platform/mlxplat/mlxreg-io/hwmon/hwmon*/asic2_health
Date:		July 2022
KernelVersion:	5.20
Contact:	Vadim Pasternak <<EMAIL>>
Description:	This file shows 2-nd ASIC health status. The possible values are:
		0 - health failed, 2 - health OK, 3 - ASIC in booting state.

		The file is read only.

What:		/sys/devices/platform/mlxplat/mlxreg-io/hwmon/hwmon*/asic_reset
What:		/sys/devices/platform/mlxplat/mlxreg-io/hwmon/hwmon*/asic2_reset
Date:		July 2022
KernelVersion:	5.20
Contact:	Vadim Pasternak <<EMAIL>>
Description:	These files allow to each of ASICs by writing 1.

		The files are write only.

What:		/sys/devices/platform/mlxplat/mlxreg-io/hwmon/hwmon*/comm_chnl_ready
Date:		July 2022
KernelVersion:	5.20
Contact:	Vadim Pasternak <<EMAIL>>
Description:	This file is used to indicate remote end (for example BMC) that system
	        host CPU is ready for sending telemetry data to remote end.
		For indication the file should be written 1.

		The file is write only.

What:		/sys/devices/platform/mlxplat/mlxreg-io/hwmon/hwmon*/config3
Date:		January 2020
KernelVersion:	5.6
Contact:	Vadim Pasternak <<EMAIL>>
Description:	The file indicates COME module hardware configuration.
		The value is pushed by hardware through GPIO pins.
		The purpose is to expose some minor BOM changes for the same system SKU.

		The file is read only.

What:		/sys/devices/platform/mlxplat/mlxreg-io/hwmon/hwmon*/reset_pwr_converter_fail
Date:		February 2023
KernelVersion:	6.3
Contact:	Vadim Pasternak <<EMAIL>>
Description:	This file shows the system reset cause due to power converter
		devices failure.
		Value 1 in file means this is reset cause, 0 - otherwise.

		The file is read only.

What:		/sys/devices/platform/mlxplat/mlxreg-io/hwmon/hwmon*/erot1_ap_reset
What:		/sys/devices/platform/mlxplat/mlxreg-io/hwmon/hwmon*/erot2_ap_reset
Date:		February 2023
KernelVersion:	6.3
Contact:	Vadim Pasternak <<EMAIL>>
Description:	These files aim to monitor the status of the External Root of Trust (EROT)
		processor's RESET output to the Application Processor (AP).
		By reading this file, could be determined if the EROT has invalidated or
		revoked AP Firmware, at which point it will hold the AP in RESET until a
		valid firmware is loaded. This protects the AP from running an
		unauthorized firmware. In the normal flow, the AP reset should be released
		after the EROT validates the integrity of the FW, and it should be done so
		as quickly as possible so that the AP boots before the CPU starts to
		communicate to each ASIC.

		The files are read only.

What:		/sys/devices/platform/mlxplat/mlxreg-io/hwmon/hwmon*/erot1_recovery
What:		/sys/devices/platform/mlxplat/mlxreg-io/hwmon/hwmon*/erot2_recovery
What:		/sys/devices/platform/mlxplat/mlxreg-io/hwmon/hwmon*/erot1_reset
What:		/sys/devices/platform/mlxplat/mlxreg-io/hwmon/hwmon*/erot2_reset
Date:		February 2023
KernelVersion:	6.3
Contact:	Vadim Pasternak <<EMAIL>>
Description:	These files aim to perform External Root of Trust (EROT) recovery
		sequence after EROT device failure.
		These EROT devices protect ASICs from unauthorized access and in normal
		flow their reset should be released with system power – earliest power
		up stage, so that EROTs can begin boot and authentication process before
		CPU starts to communicate to ASICs.
		Issuing a reset to the EROT while asserting the recovery signal will cause
		the EROT Application Processor to enter recovery mode so that the EROT FW
		can be updated/recovered.
		For reset/recovery the related file should be toggled by 1/0.

		The files are read/write.

What:		/sys/devices/platform/mlxplat/mlxreg-io/hwmon/hwmon*/erot1_wp
What:		/sys/devices/platform/mlxplat/mlxreg-io/hwmon/hwmon*/erot2_wp
Date:		February 2023
KernelVersion:	6.3
Contact:	Vadim Pasternak <<EMAIL>>
Description:	These files allow access to External Root of Trust (EROT) for reset
		and recovery sequence after EROT device failure.
		Default is 0 (programming disabled).
		If the system is in locked-down mode writing this file will not be allowed.

		The files are read/write.

What:		/sys/devices/platform/mlxplat/mlxreg-io/hwmon/hwmon*/spi_chnl_select
Date:		February 2023
KernelVersion:	6.3
Contact:	Vadim Pasternak <<EMAIL>>
Description:	This file allows SPI chip selection for External Root of Trust (EROT)
		device Out-of-Band recovery.
		File can be written with 0 or with 1. It selects which EROT can be accessed
		through SPI device.

		The file is read/write.

What:		/sys/devices/platform/mlxplat/mlxreg-io/hwmon/hwmon*/asic_pg_fail
Date:		February 2023
KernelVersion:	6.3
Contact:	<NAME_EMAIL>
Description:	This file shows ASIC Power Good status.
		Value 1 in file means ASIC Power Good failed, 0 - otherwise.

		The file is read only.

What:		/sys/devices/platform/mlxplat/mlxreg-io/hwmon/hwmon*/clk_brd1_boot_fail
What:		/sys/devices/platform/mlxplat/mlxreg-io/hwmon/hwmon*/clk_brd2_boot_fail
What:		/sys/devices/platform/mlxplat/mlxreg-io/hwmon/hwmon*/clk_brd_fail
Date:		February 2023
KernelVersion:	6.3
Contact:	<NAME_EMAIL>
Description:	These files are related to clock boards status in system.
		- clk_brd1_boot_fail: warning about 1-st clock board failed to boot from CI.
		- clk_brd2_boot_fail: warning about 2-nd clock board failed to boot from CI.
		- clk_brd_fail: error about common clock board boot failure.

		The files are read only.

What:		/sys/devices/platform/mlxplat/mlxreg-io/hwmon/hwmon*/clk_brd_prog_en
Date:		February 2023
KernelVersion:	6.3
Contact:	Vadim Pasternak <<EMAIL>>
Description:	This file enables programming of clock boards.
		Default is 0 (programming disabled).
		If the system is in locked-down mode writing this file will not be allowed.

		The file is read/write.

What:		/sys/devices/platform/mlxplat/mlxreg-io/hwmon/hwmon*/pwr_converter_prog_en
Date:		February 2023
KernelVersion:	6.3
Contact:	Vadim Pasternak <<EMAIL>>
Description:	This file enables programming of power converters.
		Default is 0 (programming disabled).
		If the system is in locked-down mode writing this file will not be allowed.

		The file is read/write.

What:		/sys/devices/platform/mlxplat/mlxreg-io/hwmon/hwmon*/reset_ac_ok_fail
Date:		February 2023
KernelVersion:	6.3
Contact:	Vadim Pasternak <<EMAIL>>
Description:	This file shows the system reset cause due to AC power failure.
		Value 1 in file means this is reset cause, 0 - otherwise.

		The file is read only.

What:		/sys/devices/platform/mlxplat/mlxreg-io/hwmon/hwmon*/cpld5_pn
What:		/sys/devices/platform/mlxplat/mlxreg-io/hwmon/hwmon*/cpld5_version
What:		/sys/devices/platform/mlxplat/mlxreg-io/hwmon/hwmon*/cpld5_version_min
Date:		August 2023
KernelVersion:	6.6
Contact:	Vadim Pasternak <<EMAIL>>
Description:	These files show with which CPLD part numbers, version and minor
		versions have been burned the 5-th CPLD device equipped on a
		system.

		The files are read only.

What:		/sys/devices/platform/mlxplat/mlxreg-io/hwmon/hwmon*/jtag_cap
Date:		August 2023
KernelVersion:	6.6
Contact:	Vadim Pasternak <<EMAIL>>
Description:	This file indicates the available method of CPLD/FPGA devices
		field update through the JTAG chain:

		b00 - field update through LPC bus register memory space.
		b01 - Reserved.
		b10 - Reserved.
		b11 - field update through CPU GPIOs bit-banging.

		The file is read only.

What:		/sys/devices/platform/mlxplat/mlxreg-io/hwmon/hwmon*/lid_open
Date:		August 2023
KernelVersion:	6.6
Contact:	Vadim Pasternak <<EMAIL>>
Description:	1 - indicates that system lid is opened, otherwise 0.

		The file is read only.

What:		/sys/devices/platform/mlxplat/mlxreg-io/hwmon/hwmon*/reset_long_pwr_pb
Date:		August 2023
KernelVersion:	6.6
Contact:	Vadim Pasternak <<EMAIL>>
Description:	This file if set 1 indicates that system has been reset by
		long press of power button.

		The file is read only.

What:		/sys/devices/platform/mlxplat/mlxreg-io/hwmon/hwmon*/reset_swb_dc_dc_pwr_fail
Date:		August 2023
KernelVersion:	6.6
Contact:	Vadim Pasternak <<EMAIL>>
Description:	This file shows 1 in case the system reset happened due to the
		failure of any DC-DC power converter devices equipped on the
		switch board.

		The file is read only.

What:		/sys/devices/platform/mlxplat/mlxreg-io/hwmon/hwmon*/global_wp_request
Date:		May 2025
KernelVersion:	6.16
Contact:	Vadim Pasternak <<EMAIL>>
Description:	This file when written 1 activates request to allow access to
		the write protected flashes. Such request can be performed only
		for system equipped with BMC (Board Management Controller),
		which can grant access to protected flashes. In case BMC allows
		access - it will respond with "global_wp_response". BMC decides
		regarding time window of granted access. After granted window is
		expired, BMC will change value back to 0.
		Default value is 0.

		The file is read/write.

What:		/sys/devices/platform/mlxplat/mlxreg-io/hwmon/hwmon*/global_wp_response
Date:		May 2025
KernelVersion:	6.16
Contact:	Vadim Pasternak <<EMAIL>>
Description:	This file, when set 1, indicates that access to protected
		flashes have been granted to host CPU by BMC.
		Default value is 0.

		The file is read only.

What:		/sys/devices/platform/mlxplat/mlxreg-io/hwmon/hwmon*/shutdown_unlock
Date:		May 2025
KernelVersion:	6.16
Contact:	Vadim Pasternak <<EMAIL>>
Description:	When ASICs are getting overheated, system protection
		hardware mechanism enforces system reboot. After system
		reboot ASICs come up in locked state. To unlock ASICs,
		this file should be written 1
		Default value is 0.

		The file is read/write.

What:		/sys/devices/platform/mlxplat/i2c_mlxcpld.*/i2c-*/i2c-*/*-00**/mlxreg-io.*/hwmon/hwmon*/boot_progress
Date:		May 2025
KernelVersion:	6.16
Contact:	Vadim Pasternak <<EMAIL>>
Description:	These files show the Data Process Unit board boot progress
		state. Valid states are:
		- 4 : OS starting.
		- 5 : OS running.
		- 6 : Low-Power Standby.

		The file is read only.

What:		/sys/devices/platform/mlxplat/i2c_mlxcpld.*/i2c-*/i2c-*/*-00**/mlxreg-io.*/hwmon/hwmon*/dpu_id
Date:		May 2025
KernelVersion:	6.16
Contact:	Vadim Pasternak <<EMAIL>>
Description:	This file shows hardware Id of Data Process Unit board.

		The file is read only.

What:		/sys/devices/platform/mlxplat/i2c_mlxcpld.*/i2c-*/i2c-*/*-00**/mlxreg-io.*/hwmon/hwmon*/reset_aux_pwr_or_reload
What:		/sys/devices/platform/mlxplat/i2c_mlxcpld.*/i2c-*/i2c-*/*-00**/mlxreg-io.*/hwmon/hwmon*/reset_dpu_thermal
What:		/sys/devices/platform/mlxplat/i2c_mlxcpld.*/i2c-*/i2c-*/*-00**/mlxreg-io.*/hwmon/hwmon*/reset_from_main_board
Date:		May 2025
KernelVersion:	6.16
Contact:	Vadim Pasternak <<EMAIL>>
Description:	These files expose the cause of the most recent reset of the Data
		Processing Unit (DPU) board. The possible causes are:
		- Power auxiliary outage or power reload.
		- Thermal shutdown.
		- Reset request from the main board.
		Value 1 in file means this is reset cause, 0 - otherwise. Only one of
		the above causes could be 1 at the same time, representing only last
		reset cause.

		The files are read only.

What:		/sys/devices/platform/mlxplat/i2c_mlxcpld.*/i2c-*/i2c-*/*-00**/mlxreg-io.*/hwmon/hwmon*/perst_rst
What:		/sys/devices/platform/mlxplat/i2c_mlxcpld.*/i2c-*/i2c-*/*-00**/mlxreg-io.*/hwmon/hwmon*/phy_rst
What:		/sys/devices/platform/mlxplat/i2c_mlxcpld.*/i2c-*/i2c-*/*-00**/mlxreg-io.*/hwmon/hwmon*/tpm_rst
What:		/sys/devices/platform/mlxplat/i2c_mlxcpld.*/i2c-*/i2c-*/*-00**/mlxreg-io.*/hwmon/hwmon*/usbphy_rst
Date:		May 2025
KernelVersion:	6.16
Contact:	Vadim Pasternak <<EMAIL>>
Description:	These files allow to reset hardware components of Data Process
		Unit board. Respectively PCI, Ethernet PHY, TPM and USB PHY
		resets.
		Default values for all the attributes is 1. Writing 0 will
		cause reset of the related component.

		The files are read/write.

What:		/sys/devices/platform/mlxplat/i2c_mlxcpld.*/i2c-*/i2c-*/*-00**/mlxreg-io.*/hwmon/hwmon*/ufm_upgrade
Date:		May 2025
KernelVersion:	6.16
Contact:	Vadim Pasternak <<EMAIL>>
Description:	These files show status of Unified Fabric Manager upgrade.
		state. 0 - means upgrade is done, 1 - otherwise.

		The file is read only.
