# SPDX-License-Identifier: (GPL-2.0-only OR BSD-2-Clause)
%YAML 1.2
---
title: Memblock reserved memory regions

maintainers:
  - <PERSON> <<EMAIL>>

description: |
  Memblock can serialize its current memory reservations created with
  reserve_mem command line option across kexec through KHO.
  This object describes each such region.

properties:
  compatible:
    enum:
      - reserve-mem-v1

  start:
    description: |
      physical address (u64) of the reserved memory region.

  size:
    description: |
      size (u64) of the reserved memory region.

required:
  - compatible
  - start
  - size

additionalProperties: false

examples:
  - |
    n1 {
      compatible = "reserve-mem-v1";
      start = <0xc06b 0x4000000>;
      size = <0x04 0x00>;
    };
