# SPDX-License-Identifier: (GPL-2.0-only OR BSD-2-Clause)
%YAML 1.2
---
title: Memblock reserved memory

maintainers:
  - <PERSON> <<EMAIL>>

description: |
  Memblock can serialize its current memory reservations created with
  reserve_mem command line option across kexec through KHO.
  The post-KHO kernel can then consume these reservations and they are
  guaranteed to have the same physical address.

properties:
  compatible:
    enum:
      - reserve-mem-v1

patternProperties:
  "$[0-9a-f_]+^":
    $ref: reserve-mem.yaml#
    description: reserved memory regions

required:
  - compatible

additionalProperties: false

examples:
  - |
    memblock {
      compatible = "memblock-v1";
      n1 {
        compatible = "reserve-mem-v1";
        start = <0xc06b 0x4000000>;
        size = <0x04 0x00>;
      };
    };
