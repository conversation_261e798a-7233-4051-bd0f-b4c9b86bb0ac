# SPDX-License-Identifier: (GPL-2.0-only OR BSD-2-Clause)
%YAML 1.2
---
title: <PERSON><PERSON><PERSON> HandOver (KHO) root tree

maintainers:
  - <PERSON> <<EMAIL>>
  - <PERSON><PERSON> <changyuan<PERSON>@google.com>

description: |
  System memory preserved by KHO across kexec.

properties:
  compatible:
    enum:
      - kho-v1

  preserved-memory-map:
    description: |
      physical address (u64) of an in-memory structure describing all preserved
      folios and memory ranges.

patternProperties:
  "$[0-9a-f_]+^":
    $ref: sub-fdt.yaml#
    description: physical address of a KHO user's own FDT.

required:
  - compatible
  - preserved-memory-map

additionalProperties: false

examples:
  - |
    kho {
        compatible = "kho-v1";
        preserved-memory-map = <0xf0be16 0x1000000>;

        memblock {
                fdt = <0x80cc16 0x1000000>;
        };
    };
